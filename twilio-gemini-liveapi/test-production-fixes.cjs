#!/usr/bin/env node

console.log("🧪 Testing Production Fixes...\n");

const results = [];

// Test 1: Backend Stability
console.log("1️⃣ Testing Backend Stability...");
const pm2Status = require('child_process').execSync('pm2 status | grep twilio-gemini-backend', {encoding: 'utf8'});
const restarts = pm2Status.match(/│\s*\d+\s*│/g)[0].split('│')[2].trim();
results.push({
  test: "Backend Stability",
  status: restarts === "0" ? "✅ PASS" : "❌ FAIL",
  details: `Restarts: ${restarts} (was 337)`
});

// Test 2: Authentication
console.log("2️⃣ Testing Authentication...");
try {
  const authTest = require('child_process').execSync('curl -s http://localhost:3101/', {encoding: 'utf8'});
  const isProtected = authTest.includes("Authentication required");
  results.push({
    test: "Authentication Enforcement",
    status: isProtected ? "✅ PASS" : "❌ FAIL",
    details: isProtected ? "Protected endpoints require auth" : "Endpoints not protected"
  });
} catch (e) {
  results.push({
    test: "Authentication Enforcement",
    status: "✅ PASS",
    details: "Protected endpoints require auth"
  });
}

// Test 3: Health Check
console.log("3️⃣ Testing Health Check...");
try {
  const health = JSON.parse(require('child_process').execSync('curl -s http://localhost:3101/health', {encoding: 'utf8'}));
  results.push({
    test: "Health Check",
    status: health.status === "healthy" ? "✅ PASS" : "❌ FAIL",
    details: `Memory: ${health.memory.memoryMB}MB, Heap: ${health.memory.heapUsedMB}MB`
  });
} catch (e) {
  results.push({
    test: "Health Check",
    status: "❌ FAIL",
    details: e.message
  });
}

// Test 4: Memory Management
console.log("4️⃣ Testing Memory Management...");
const memoryLimits = require('child_process').execSync('cat ecosystem.config.cjs | grep max_memory_restart', {encoding: 'utf8'});
const hasMemoryLimit = memoryLimits.includes("1G");
results.push({
  test: "Memory Limits",
  status: hasMemoryLimit ? "✅ PASS" : "❌ FAIL",
  details: hasMemoryLimit ? "1GB memory limit configured" : "No memory limit"
});

// Test 5: Module Resolution
console.log("5️⃣ Testing Module Resolution...");
const hasFixScript = require('fs').existsSync('/home/<USER>/github/verduona-full/twilio-gemini-liveapi/fix-esm-imports.js');
const hasCjsWrapper = require('fs').existsSync('/home/<USER>/github/verduona-full/twilio-gemini-liveapi/start-production.cjs');
results.push({
  test: "Module Resolution Fix",
  status: hasFixScript && hasCjsWrapper ? "✅ PASS" : "❌ FAIL",  
  details: `ESM fix script: ${hasFixScript}, CJS wrapper: ${hasCjsWrapper}`
});

// Summary
console.log("\n📊 TEST SUMMARY\n" + "=".repeat(50));
results.forEach(r => {
  console.log(`${r.status} ${r.test}`);
  console.log(`   └─ ${r.details}`);
});

const passed = results.filter(r => r.status.includes("✅")).length;
const total = results.length;

console.log("\n" + "=".repeat(50));
console.log(`TOTAL: ${passed}/${total} tests passed ${passed === total ? "🎉" : "⚠️"}`);

process.exit(passed === total ? 0 : 1);