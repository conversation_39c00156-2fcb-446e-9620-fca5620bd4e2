# 🔍 The AI Code Audit Cookbook
## A Systematic Approach to Finding Anomalies in AI-Generated Code

### 🎯 Purpose
This cookbook provides a battle-tested methodology for auditing AI-generated codebases to find the "disconnected dots" - those subtle bugs and inconsistencies that arise when code is written by AI assistants without perfect context awareness.

## 📋 Pre-Audit Checklist

Before starting, gather:
- [ ] Project documentation (README, CONTRIBUTING, etc.)
- [ ] Environment setup files (.env.example, configs)
- [ ] Recent error logs or bug reports
- [ ] Deployment/build scripts
- [ ] Any "FIXME" or "TODO" documentation

## 🔬 The 12-Phase Audit Process

### Phase 1: Project Structure Analysis
**Goal**: Understand the architecture and identify structural anomalies

```bash
# Commands to run
find . -type f -name "*.md" | grep -E "(FIX|BLOCK|CRITICAL|TODO)"
ls -la | grep -E "(test|debug|temp)"
find . -name "package.json" -o -name "tsconfig.json" -o -name "*.config.*"
```

**Look for**:
- Files that suggest problems (CRITICAL_FIXES.md, etc.)
- Test/debug files in wrong locations
- Multiple config files for same tool
- Inconsistent module systems (.cjs vs .mjs vs .js)

### Phase 2: Dependency Analysis
**Goal**: Find missing, unused, or mismatched dependencies

```bash
# Check for imports not in package.json
grep -r "from ['\"]@" --include="*.ts" --include="*.js" | cut -d'"' -f2 | sort -u
grep -r "require\(['\"]" --include="*.ts" --include="*.js" | cut -d'"' -f2 | sort -u

# Find dynamic imports
grep -r "import(" --include="*.ts" --include="*.js"
```

**Red flags**:
- Imports from packages not in package.json
- Different packages imported for same purpose
- Mix of require() and import statements

### Phase 3: Function Call Forensics
**Goal**: Find non-existent or misnamed function calls

```bash
# Find function definitions vs calls
grep -r "function\s\+\w\+\|const\s\+\w\+\s*=.*=>" --include="*.ts" | grep -v test
grep -r "\.\w\+(" --include="*.ts" | grep -v "console\."

# Find TODO/FIXME near function calls
grep -B2 -A2 -r "TODO\|FIXME\|XXX\|HACK" --include="*.ts"

# Find "not implemented" patterns
grep -r "not implemented\|placeholder\|stub" -i --include="*.ts"
```

**Common AI mistakes**:
- Calling `processAudio()` when function is `processAudioData()`
- Wrong parameter order or count
- Calling methods that don't exist on objects

### Phase 4: Type System Audit
**Goal**: Find type mismatches and unsafe operations

```bash
# Find any types
grep -r ": any\|as any" --include="*.ts"

# Find non-null assertions
grep -r "!" --include="*.ts" | grep -v "!=" | grep -v "!=="

# Find type assertions
grep -r " as " --include="*.ts"

# Find @ts-ignore
grep -r "@ts-ignore\|@ts-nocheck" --include="*.ts"
```

**Patterns to investigate**:
- Function expecting object, receiving string
- Optional parameters used with non-null assertion
- Interfaces not matching actual usage

### Phase 5: Import/Export Mapping
**Goal**: Verify all imports have corresponding exports

```bash
# Find all exports
grep -r "export\s\+" --include="*.ts" | grep -v "export \*"

# Find all imports
grep -r "import.*from" --include="*.ts"

# Check for circular dependencies
find . -name "*.ts" -exec grep -l "import.*from.*{}" \; | sort | uniq -d
```

**Issues to find**:
- Named vs default export mismatches
- Circular dependency chains
- Re-exports that don't match original exports

### Phase 6: Async/Promise Inspection
**Goal**: Find race conditions and async issues

```bash
# Find async functions without try-catch
grep -A5 -B1 "async\s\+\w\+" --include="*.ts" | grep -v "try\s*{"

# Find .then without .catch
grep "\.then(" --include="*.ts" | grep -v "\.catch"

# Find Promise.all/race usage
grep -r "Promise\.\(all\|race\)" --include="*.ts"

# Find setTimeout/setInterval
grep -r "set\(Timeout\|Interval\)" --include="*.ts"
```

**Race condition indicators**:
- Shared Maps/Sets modified in async contexts
- Multiple timers on same resource
- State updates from multiple event handlers

### Phase 7: Error Handling Audit
**Goal**: Find missing or inadequate error handling

```bash
# Find empty catch blocks
grep -A2 "catch\s*(" --include="*.ts" | grep -A1 "{\s*$" | grep -B1 "}"

# Find throw statements
grep -r "throw\s\+" --include="*.ts"

# Find file operations without error handling
grep -r "readFile\|writeFile\|mkdir" --include="*.ts" | grep -v "catch\|\.catch"

# Find network operations
grep -r "fetch\|axios\|http" --include="*.ts" | grep -v "catch\|\.catch"
```

**Common gaps**:
- Synchronous file operations without try-catch
- Network requests without timeouts
- Silent failures (catch without re-throw or return)

### Phase 8: API Contract Validation
**Goal**: Ensure frontend/backend contracts match

```bash
# Find API endpoint definitions
grep -r "router\.\(get\|post\|put\|delete\)\|app\.\(get\|post\|put\|delete\)" --include="*.ts"

# Find frontend API calls
grep -r "fetch\|axios" --include="*.ts" --include="*.tsx" | grep -E "/(api|endpoint)"

# Find request/response types
grep -r "interface.*Request\|interface.*Response" --include="*.ts"
```

**Mismatches to find**:
- Field names different between sender/receiver
- Missing required fields
- Type mismatches (string vs number)
- Different endpoint paths

### Phase 9: Configuration Verification
**Goal**: Find missing or hardcoded configuration

```bash
# Find process.env usage
grep -r "process\.env\." --include="*.ts" | cut -d'.' -f3 | cut -d' ' -f1 | sort -u

# Compare with .env.example
comm -23 <(above command) <(grep "^[A-Z]" .env.example | cut -d'=' -f1 | sort)

# Find hardcoded values
grep -r "localhost\|127\.0\.0\.1\|3000\|8080" --include="*.ts"
grep -r "http://\|https://" --include="*.ts" | grep -v "process\.env"
```

**Configuration smells**:
- Hardcoded ports, URLs, timeouts
- Missing environment variables
- Inconsistent defaults
- No validation on startup

### Phase 10: State Management Analysis
**Goal**: Find state corruption possibilities

```bash
# Find global variables
grep -r "^let\s\+\w\+\|^const\s\+\w\+\s*=" --include="*.ts" | grep -v "export\|function"

# Find static class members
grep -r "static\s\+" --include="*.ts"

# Find Maps and Sets
grep -r "new\s\+\(Map\|Set\)" --include="*.ts"

# Find state mutations
grep -r "\.\(push\|pop\|shift\|unshift\|splice\|delete\)" --include="*.ts"
```

**State issues**:
- Global state without synchronization
- Direct mutations instead of immutable updates
- Memory leaks from uncleaned event listeners

### Phase 11: Authentication & Security Audit
**Goal**: Identify authentication bypasses and security vulnerabilities

```bash
# Find auth middleware usage
grep -r "auth\|Auth" --include="*.ts" | grep -v "author"

# Check for optional auth enforcement
grep -r "FORCE_AUTH\|NODE_ENV.*production" --include="*.ts"

# Find exposed endpoints without auth
grep -r "router\.\(get\|post\|put\|delete\)" --include="*.ts" -A2 | grep -v "auth"

# Check token validation
grep -r "token\|Token" --include="*.ts" | grep -E "(validate|verify|check)"
```

**Security red flags**:
- Optional authentication in production
- Weak token validation (only length checks)
- Missing auth middleware on sensitive endpoints
- Development mode bypasses in production

### Phase 12: Concurrency & Race Condition Analysis
**Goal**: Find shared resources accessed without proper synchronization

```bash
# Find shared Maps/Sets in static context
grep -r "static.*Map\|static.*Set" --include="*.ts"

# Find array mutations in async contexts
grep -B5 -A5 "\.push\|\.splice" --include="*.ts" | grep -B10 -A10 "async\|await"

# Find recovery/lock patterns
grep -r "lock\|Lock\|inProgress\|pending" --include="*.ts" -i

# Check for atomic operations
grep -r "recoveryInProgress\|bufferEarlyAudio\|rateLimitStore" --include="*.ts" -B2 -A2
```

**Race condition patterns**:
- Shared Maps without mutex/locks
- Check-then-act patterns without atomicity
- Buffer arrays modified from multiple async contexts
- Non-atomic session state transitions

## 📊 Analysis Tools & Techniques

### 1. Pattern Recognition Commands
```bash
# Find similar but different function names
find . -name "*.ts" -exec grep -o "\w\+(" {} \; | sort | uniq -c | sort -n

# Find files that often change together
git log --format="" --name-only | grep -v "^$" | sort | uniq -c | sort -n

# Find complex files (high line count + high complexity)
find . -name "*.ts" -exec wc -l {} \; | sort -n | tail -20
```

### 2. Automated Anomaly Detection Script with References
```bash
#!/bin/bash
# save as audit.sh - Enhanced version that outputs file:line references

echo "🔍 Starting AI Code Audit with References..."

echo ""
echo "📁 Problematic Files:"
find . -name "*test*" -o -name "*debug*" -o -name "*temp*" | grep -v node_modules | while read file; do
    echo "  - $file"
done

echo ""
echo "📦 Import Anomalies:"
grep -Hn "from ['\"]" --include="*.ts" | grep -v node_modules | while read line; do
    file=$(echo "$line" | cut -d: -f1)
    linenum=$(echo "$line" | cut -d: -f2)
    import=$(echo "$line" | grep -o "from ['\"].*['\"]" | cut -d'"' -f2)
    echo "  - $file:$linenum → imports '$import'"
done | sort | uniq

echo ""
echo "⚠️  TODO/FIXME Locations:"
grep -Hn "TODO\|FIXME\|HACK\|XXX" --include="*.ts" | while read match; do
    echo "  - $match"
done

echo ""
echo "🔄 Async Without Await:"
grep -Hn "async" --include="*.ts" | grep -v "await" | while read match; do
    echo "  - $match"
done

echo ""
echo "✅ Audit complete! References ready for AI assistant."
```

### 3. Issue Output Format for AI Assistants

When documenting issues, always use this format for easy AI processing:

```markdown
## Issue: [Issue Name]
- **File**: `path/to/file.ts:123`
- **Severity**: CRITICAL|HIGH|MEDIUM|LOW
- **Type**: type-mismatch|missing-function|race-condition|etc
- **Description**: What's wrong
- **Current Code**:
```typescript
// Line 123-125
const result = someFunction(wrongType);
```
- **Expected Code**:
```typescript
// Line 123-125
const result = someFunction(correctType);
```
- **AI Fix Command**: "Fix type mismatch at path/to/file.ts:123 by changing parameter type"
```

### 3. Visual Analysis Approach
Create a mental map:
1. **Entry points** → Where does execution start?
2. **Data flow** → How does data move through the system?
3. **State changes** → Where is state modified?
4. **External boundaries** → Where does the system interface with outside world?

## 🔧 Enhanced Audit Commands with File:Line Output

### Find Type Mismatches with Location
```bash
# Find function calls with potential type issues
grep -Hn "\.send(" --include="*.ts" | while read match; do
    file=$(echo "$match" | cut -d: -f1)
    line=$(echo "$match" | cut -d: -f2)
    content=$(echo "$match" | cut -d: -f3-)
    echo "Check: $file:$line"
    echo "  Code: $content"
    echo ""
done
```

### Find Missing Error Handling with Context
```bash
# Find async operations without try-catch, with context
grep -B2 -A5 -Hn "async\s\+\w\+" --include="*.ts" | grep -v "try\s*{" | \
awk '/^--$/ {print ""} /^[^-]/ {print}' | \
awk 'BEGIN {file=""} /:.*async/ {file=$0; next} /[^[:space:]]/ {if (file!="") print file "\n  " $0}'
```

### Generate AI-Ready Fix List
```bash
#!/bin/bash
# generate-fix-list.sh - Creates AI-ready fix instructions

echo "# AI Assistant Fix List"
echo "Generated: $(date)"
echo ""

# Type mismatches
echo "## Type Mismatch Fixes"
grep -Hn "geminiSession\.send(" --include="*.ts" | while read match; do
    file=$(echo "$match" | cut -d: -f1)
    line=$(echo "$match" | cut -d: -f2)
    echo "- Fix type mismatch at $file:$line - Change string parameter to GeminiLiveMessage object"
done

# Missing imports
echo -e "\n## Missing Import Fixes"
grep -Hn "from.*@google/genai" --include="*.ts" | while read match; do
    file=$(echo "$match" | cut -d: -f1)
    line=$(echo "$match" | cut -d: -f2)
    echo "- Add @google/genai to package.json dependencies (referenced at $file:$line)"
done

# API mismatches
echo -e "\n## API Contract Fixes"
grep -Hn "task:" --include="*.ts" --include="*.tsx" | grep -v "interface\|type\|//" | while read match; do
    file=$(echo "$match" | cut -d: -f1)
    line=$(echo "$match" | cut -d: -f2)
    echo "- Change 'task' to 'aiInstructions' at $file:$line"
done
```

## 🎯 Prioritization Matrix

| Severity | Impact | Examples | Fix Priority |
|----------|---------|----------|--------------|
| **CRITICAL** | Runtime crashes | Type mismatches, missing functions | Immediate |
| **HIGH** | Feature failures | API mismatches, race conditions | Same day |
| **MEDIUM** | Degraded experience | Missing error handling, validation | This week |
| **LOW** | Technical debt | Code organization, documentation | This month |

## 📝 AI-Optimized Audit Report Template

```markdown
# AI Code Audit Report - [Project Name]
Date: [Date]
Auditor: [Name/Tool]

## Executive Summary
- Total issues found: X
- Critical issues: X
- Estimated fix time: X hours

## Issues for AI Assistant to Fix

### CRITICAL-1: Type Mismatch in GeminiSession.send()
- **Files**: 
  - `src/session/websocket-routing.ts:151`
  - `src/audio/gemini-sender.ts:33`
- **Current Issue**: Function expects GeminiLiveMessage but receives string
- **Fix Instructions**:
  ```
  1. Open src/session/websocket-routing.ts
  2. Go to line 151
  3. Change: await connectionData.geminiSession.send(greetingPrompt);
  4. To: await connectionData.geminiSession.sendRealtimeInput({
       media: {
         data: Buffer.from(greetingPrompt, 'utf-8').toString('base64'),
         mimeType: 'text/plain'
       }
     });
  ```

### HIGH-1: Missing Package Dependency
- **File**: `package.json`
- **References**: 
  - `src/gemini/client.ts:5` (imports @google/genai)
  - `src/websocket/local-testing-handler.ts:12`
- **Fix Instructions**:
  ```
  Add to package.json dependencies:
  "@google/genai": "^0.1.0"
  Then run: npm install
  ```

### HIGH-2: API Contract Mismatch
- **Frontend File**: `call-center-frontend/components/ConfigForm.tsx:87`
- **Backend File**: `src/api/session-config-routes.ts:15-21`
- **Issue**: Frontend sends 'task' field, backend expects 'aiInstructions'
- **Fix Instructions**:
  ```
  In ConfigForm.tsx:87, change:
  { task: formData.instructions }
  To:
  { aiInstructions: formData.instructions }
  ```

## Batch Fix Commands for AI

```bash
# Run these commands to generate exact fix locations:
./audit-tools/generate-fix-list.sh > fixes-for-ai.md
```

## Verification Commands

After fixes, run:
```bash
npm run typecheck
npm run lint
npm test
```
```

## 🤖 Example: Complete Audit Output for AI Assistant

```markdown
# Twilio-Gemini Call Center - Audit Results
Generated: 2024-01-15 14:30:00

## 🔴 CRITICAL Issues (Fix Immediately)

### Issue 1: Type Mismatch - GeminiSession.send()
- **Location 1**: `src/session/websocket-routing.ts:151`
  ```typescript
  // CURRENT (Line 151):
  await connectionData.geminiSession.send(greetingPrompt);
  // SHOULD BE:
  await connectionData.geminiSession.sendRealtimeInput({
    media: {
      data: Buffer.from(greetingPrompt, 'utf-8').toString('base64'),
      mimeType: 'text/plain'
    }
  });
  ```
- **Location 2**: `src/audio/gemini-sender.ts:33`
  ```typescript
  // CURRENT (Line 33):
  await connectionData.geminiSession.send(JSON.stringify(realtimeInput));
  // SHOULD BE:
  await connectionData.geminiSession.sendRealtimeInput(realtimeInput);
  ```

### Issue 2: Missing Error Handling
- **Location**: `src/audio/audio-processor.ts:103-105`
  ```typescript
  // CURRENT:
  fs.mkdirSync(dir, { recursive: true });
  // SHOULD BE:
  try {
    fs.mkdirSync(dir, { recursive: true });
  } catch (error) {
    console.error('Failed to create directory:', error);
    return;
  }
  ```

## 🟠 HIGH Priority Issues

### Issue 3: Race Condition in Audio Buffers
- **Location**: `src/audio/audio-forwarding.ts:45-67`
- **Problem**: earlyAudioBuffers Map accessed without synchronization
- **Fix**: Add mutex pattern:
  ```typescript
  // Add at top of file:
  private audioBufferLocks = new Map<string, Promise<void>>();
  
  // Wrap buffer access:
  async bufferAudio(callSid: string, buffer: Buffer) {
    const lock = this.audioBufferLocks.get(callSid) || Promise.resolve();
    this.audioBufferLocks.set(callSid, lock.then(async () => {
      // existing buffer logic here
    }));
  }
  ```

## Quick Fix Script
Save as `apply-fixes.sh`:
```bash
#!/bin/bash
echo "Applying critical fixes..."

# Fix 1: Type mismatch
sed -i '151s/\.send(greetingPrompt)/\.sendRealtimeInput({media:{data:Buffer.from(greetingPrompt,"utf-8").toString("base64"),mimeType:"text\/plain"}})/' src/session/websocket-routing.ts

# Fix 2: Add to package.json
npm install @google/genai --save

echo "Fixes applied. Run 'npm run typecheck' to verify."
```
```

## 🚀 Post-Audit Action Plan

1. **Triage Meeting**
   - Review all findings
   - Assign priorities
   - Estimate effort

2. **Quick Wins** (1-2 hours)
   - Fix type mismatches
   - Add missing dependencies
   - Fix field name mismatches

3. **Systematic Fixes** (1-2 days)
   - Add error handling
   - Fix race conditions
   - Add validation

4. **Prevention** (ongoing)
   - Add linting rules
   - Create test suite
   - Set up CI/CD checks

## 🛡️ Prevention Strategies

### 1. AI Coding Guidelines
```markdown
When using AI to write code:
- Always verify function signatures exist
- Check types match between caller/callee
- Ensure imports have corresponding exports
- Add error handling immediately
- Test async operations thoroughly
```

### 2. Automated Checks
```json
// .github/workflows/ai-code-check.yml
{
  "checks": [
    "no-any-types",
    "no-empty-catches",
    "required-error-handling",
    "import-export-matching",
    "no-hardcoded-values"
  ]
}
```

### 3. Code Review Checklist
- [ ] All functions called actually exist
- [ ] Types match between interfaces
- [ ] Error handling is comprehensive
- [ ] No race conditions in async code
- [ ] Configuration is not hardcoded
- [ ] API contracts are validated

## 🎓 Learning from Common AI Mistakes

1. **Hallucinated Functions**: AI often creates plausible-sounding function names
2. **Type Confusion**: Mixing string/object parameters
3. **Incomplete Context**: Missing imports or exports
4. **Async Assumptions**: Forgetting await or error handling
5. **Configuration Gaps**: Not knowing all required env vars

## 🏁 Conclusion

This cookbook provides a systematic approach to finding and fixing issues in AI-generated code. The key is to be methodical, use automated tools where possible, and understand the common patterns of AI mistakes.

Remember: AI is a powerful tool, but it needs human oversight to ensure correctness, especially at integration points and system boundaries.

### Quick Reference Card
```bash
# The 5-minute audit
grep -r "TODO\|FIXME" --include="*.ts" | wc -l  # Technical debt
grep -r ": any" --include="*.ts" | wc -l        # Type safety
grep -r "catch\s*{" -A1 --include="*.ts" | grep "}" | wc -l  # Empty catches
find . -name "*.ts" -exec grep -l "export" {} \; | wc -l     # Module count
grep -r "process\.env\." --include="*.ts" | cut -d'.' -f3 | sort -u | wc -l  # Env vars
```

Happy auditing! 🔍✨