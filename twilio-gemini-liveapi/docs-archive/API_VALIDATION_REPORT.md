# API Contract Validation Report - Twilio-Gemini Project

## Executive Summary

This report documents API contract mismatches, missing validations, and data flow issues found in the Twilio-Gemini codebase as of the current analysis.

## 1. API Endpoint Mismatches

### 1.1 Frontend vs Backend Route Inconsistencies

#### Missing Backend Routes
The frontend calls these endpoints but they don't exist in the backend API routes:
- `/update-system-message` - Called by frontend but no backend implementation found
- `/save-config` - Referenced in frontend API proxy but no backend handler

#### Route Path Mismatches
- Frontend calls `/api/sessions/${callSid}/end` with POST
- Backend expects `/end-session/:callSid` (different path structure)

### 1.2 Request/Response Structure Mismatches

#### `/update-session-config` Endpoint
**Frontend sends:**
```typescript
{
  task: string,           // Frontend sends 'task'
  voice: string,
  model: string,
  targetName: string,
  targetPhoneNumber: string,
  outputLanguage: string  // Frontend includes this
}
```

**Backend expects:**
```typescript
{
  aiInstructions?: string,  // Backend expects 'aiInstructions', not 'task'
  voice?: string,
  model?: string,
  targetName?: string,
  targetPhoneNumber?: string
  // No outputLanguage field in backend type
}
```

#### `/make-call` Endpoint
**Frontend sends:**
```typescript
{
  to: string,
  from: string,
  // Other fields sent via update-session-config
}
```

**Backend expects additional fields:**
```typescript
{
  to: string,
  from: string,
  task?: string,
  voice?: string,
  model?: string,
  targetName?: string,
  targetPhoneNumber?: string,
  outputLanguage?: string
}
```

## 2. Missing Required Field Validations

### 2.1 Backend Validation Gaps

1. **No Zod or schema validation library** - The backend relies on manual validation
2. **Inconsistent validation patterns:**
   - Some endpoints validate required fields (e.g., `/make-call` validates phone numbers)
   - Others don't validate at all (e.g., `/update-session-config` doesn't validate aiInstructions)

3. **Missing validations:**
   - `/configure-call` - No validation for aiInstructions format
   - `/set-voice` - No validation that voice exists in available voices
   - `/set-model` - No validation that model exists in available models
   - WebSocket messages - No schema validation for incoming messages

### 2.2 Type Safety Issues

1. **Loose typing with `any`:**
   ```typescript
   interface AudioSettingsBody {
     [key: string]: any;  // Too permissive
   }
   ```

2. **Optional fields that should be required:**
   - `CallConfig.callSid` is optional but used as if required
   - `SessionConfig.scriptType` and `scriptId` are required in type but not validated

## 3. WebSocket Message Format Issues

### 3.1 Message Type Inconsistencies

1. **Local testing flow expects:**
   - `type: 'start-session'`
   - `type: 'audio'` or `type: 'audio-data'` (legacy)
   - `type: 'text-message'`

2. **Frontend sends:**
   ```javascript
   {
     type: 'start-session',
     voice: selectedVoice,
     model: selectedModel,
     language: selectedLanguage,      // Not in type definition
     aiInstructions: aiInstructions,
     campaignScript: campaignScript   // Not in type definition
   }
   ```

3. **Type definition expects:**
   ```typescript
   interface LocalStartMessage {
     type: 'start-session';
     aiInstructions?: string;
     voice?: string;
     model?: string;
     scriptId?: string;  // Frontend sends campaignScript instead
   }
   ```

### 3.2 Audio Data Format Mismatch

Frontend sends audio as:
- `audio: base64String` or `audioData: base64String`

Backend expects consistent field name but handles both due to legacy support.

## 4. API Path Hardcoding Issues

### 4.1 Environment-Dependent Paths
- Frontend hardcodes `BACKEND_URL` from environment variable
- Some API calls don't use the proxy pattern (direct backend calls)
- WebSocket URLs are constructed differently for local vs production

### 4.2 Inconsistent API Prefixes
- Some endpoints use `/api/` prefix, others don't
- Examples:
  - `/available-voices` (no prefix)
  - `/api/voice-config` (with prefix)
  - `/make-call` (no prefix)
  - `/api/sessions/:id/end` (with prefix)

## 5. Missing Error Response Contracts

No consistent error response format across endpoints:
- Some return `{ success: false, error: string }`
- Others return `{ error: string, message: string }`
- Some use `{ error: string, details: string }`

## 6. Recommendations for Fixes

### 6.1 Immediate Fixes (Critical)

1. **Fix `/update-session-config` mismatch:**
   - Backend should accept `task` field and map it to `aiInstructions`
   - Or frontend should send `aiInstructions` instead of `task`

2. **Standardize session end endpoint:**
   - Change backend route from `/end-session/:callSid` to `/api/sessions/:callSid/end`
   - Or update frontend to use the existing backend route

3. **Add missing backend routes:**
   - Implement `/update-system-message` endpoint
   - Implement `/save-config` endpoint or remove frontend references

### 6.2 Medium Priority Fixes

1. **Implement proper validation:**
   - Add Zod schemas for all request/response types
   - Validate at the route level before processing
   - Example:
     ```typescript
     const MakeCallSchema = z.object({
       to: z.string().regex(/^\+\d{10,15}$/),
       from: z.string().regex(/^\+\d{10,15}$/),
       task: z.string().optional(),
       // ... other fields
     });
     ```

2. **Standardize WebSocket message contracts:**
   - Create shared types between frontend and backend
   - Remove support for legacy message formats
   - Add message validation

3. **Fix type definitions:**
   - Remove `any` types
   - Make required fields non-optional
   - Add missing fields to interfaces

### 6.3 Long-term Improvements

1. **API Documentation:**
   - Generate OpenAPI/Swagger documentation
   - Use it to validate frontend/backend contracts
   - Consider using tools like `fastify-swagger`

2. **Shared Type Package:**
   - Create a shared npm package for types
   - Use it in both frontend and backend
   - Ensures consistency

3. **Contract Testing:**
   - Implement contract tests using tools like Pact
   - Verify API contracts in CI/CD pipeline

4. **Error Handling Standards:**
   - Define a standard error response format
   - Implement consistent error handling middleware
   - Example:
     ```typescript
     interface ApiError {
       error: {
         code: string;
         message: string;
         details?: any;
       };
       timestamp: string;
       path: string;
     }
     ```

## 7. Security Considerations

1. **Input Validation:** Many endpoints accept unvalidated input which could lead to injection attacks
2. **Type Coercion:** Using `any` types bypasses TypeScript's safety
3. **Phone Number Validation:** Inconsistent validation could allow malformed numbers

## Conclusion

The API has several contract mismatches that could cause runtime errors. The most critical issues are:
1. Field name mismatch between frontend and backend (`task` vs `aiInstructions`)
2. Missing backend endpoints that frontend expects
3. Lack of proper validation on most endpoints
4. Inconsistent WebSocket message formats

Implementing the recommended fixes will improve reliability and maintainability of the system.