# Architecture Refactoring Plan: From Monolithic to Micro-Components

## Executive Summary

This document outlines a comprehensive plan to refactor the Twilio-Gemini Live API codebase from its current tightly-coupled monolithic architecture to a loosely-coupled, event-driven micro-component architecture. This refactoring is essential for improving AI-assisted development, maintainability, and system reliability.

## Current Architecture Problems

### 1. Extreme Coupling and Complexity
- **SessionManager.ts**: 655 lines with imports from 24+ modules
- **Circular dependencies**: websocket-routing ↔ session-manager ↔ recovery ↔ websocket-routing
- **Deep import chains**: Some files have 10+ levels of transitive dependencies
- **Mixed concerns**: Business logic, infrastructure, and external services intertwined

### 2. AI Development Challenges
- Large files are difficult for AI to understand completely
- Complex dependencies make it risky to modify code
- Unclear boundaries lead to unintended side effects
- Testing requires understanding entire system context

### 3. Maintenance Issues
- Bug fixes often require changes across multiple files
- New features require modifying existing complex code
- Difficult to understand data flow through the system
- High cognitive load for developers and AI assistants

## Target Architecture: Event-Driven Micro-Components

### Core Principles

1. **One File, One Purpose**
   - Each file does exactly ONE thing
   - Maximum 150 lines per file
   - Clear input → process → output

2. **Event-Based Communication**
   - Components communicate via events, not direct imports
   - Loose coupling between components
   - Clear data flow through event chains

3. **Stateless Functions**
   - Prefer pure functions over stateful classes
   - State managed in dedicated stores
   - Immutable data transformations

4. **Clear Boundaries**
   - Distinct layers: handlers, services, storage
   - No circular dependencies
   - Maximum 3-4 imports per file

## New Directory Structure

```
/src/
├── core/                      # Minimal shared infrastructure (200 lines total)
│   ├── event-bus.js          # Central event system (50 lines)
│   ├── logger.js             # Simple logging (30 lines)
│   ├── config.js             # Config loader (40 lines)
│   └── types.js              # Shared type definitions (80 lines)
│
├── handlers/                  # Event/message handlers (50-80 lines each)
│   ├── audio/
│   │   ├── process-twilio-audio.js    # Handle Twilio audio chunk
│   │   ├── process-gemini-audio.js    # Handle Gemini audio chunk
│   │   ├── convert-audio-format.js    # Audio format conversion
│   │   └── buffer-audio.js            # Audio buffering logic
│   │
│   ├── messages/
│   │   ├── handle-twilio-start.js     # Process start message
│   │   ├── handle-twilio-stop.js      # Process stop message
│   │   ├── handle-twilio-media.js     # Process media message
│   │   ├── handle-gemini-response.js  # Process AI response
│   │   └── handle-transcript.js       # Process transcripts
│   │
│   └── session/
│       ├── create-session.js          # Create new session
│       ├── update-session.js          # Update session state
│       ├── end-session.js             # Clean up session
│       └── recover-session.js         # Session recovery
│
├── services/                  # External service connectors (40-60 lines each)
│   ├── twilio/
│   │   ├── connect-websocket.js       # WebSocket connection
│   │   ├── send-audio.js              # Send audio to Twilio
│   │   ├── make-call.js               # Initiate outbound call
│   │   └── validate-request.js        # Webhook validation
│   │
│   ├── gemini/
│   │   ├── create-client.js           # Create AI client
│   │   ├── send-audio.js              # Send audio to AI
│   │   ├── send-text.js               # Send text to AI
│   │   └── parse-response.js          # Parse AI responses
│   │
│   └── storage/
│       ├── session-store.js           # Session state storage
│       ├── transcript-store.js        # Transcript storage
│       ├── metrics-store.js           # Metrics storage
│       └── script-store.js            # Campaign scripts
│
├── pipelines/                 # Orchestration logic (80-100 lines each)
│   ├── audio-pipeline.js              # Audio processing flow
│   ├── session-pipeline.js            # Session lifecycle
│   ├── call-pipeline.js               # Call flow orchestration
│   └── recovery-pipeline.js           # Error recovery flow
│
└── api/                       # HTTP endpoints (30-60 lines each)
    ├── health.js                      # Health check endpoint
    ├── make-call.js                   # Initiate call API
    ├── get-status.js                  # Get call status API
    ├── get-transcript.js              # Get transcript API
    └── websocket.js                   # WebSocket upgrade
```

## Event Architecture

### Core Event Types

```javascript
// Session Lifecycle Events
'session.requested'        → { callSid, config }
'session.created'         → { callSid, sessionId }
'session.updated'         → { callSid, changes }
'session.failed'          → { callSid, error }
'session.ended'           → { callSid, reason }
'session.recovered'       → { callSid, attempt }

// Audio Flow Events
'audio.received.twilio'   → { callSid, base64Audio }
'audio.received.gemini'   → { callSid, audioData }
'audio.converted'         → { callSid, format, buffer }
'audio.buffered'          → { callSid, bufferId }
'audio.forwarded'         → { callSid, destination }
'audio.error'             → { callSid, error }

// Call Management Events
'call.requested'          → { phoneNumber, campaignId }
'call.initiated'          → { callSid, twilioSid }
'call.connected'          → { callSid, streamSid }
'call.ended'              → { callSid, duration }
'call.error'              → { callSid, error }

// AI Interaction Events
'ai.request.sent'         → { callSid, type, content }
'ai.response.received'    → { callSid, response }
'ai.transcript.ready'     → { callSid, transcript }
'ai.summary.ready'        → { callSid, summary }
'ai.error'                → { callSid, error }
```

### Event Flow Example

```
API: POST /make-call
  ↓
emit('call.requested')
  ↓
call-pipeline.js listens → validates → emit('session.requested')
  ↓
create-session.js listens → creates → emit('session.created')
  ↓
make-call.js listens → Twilio API → emit('call.initiated')
  ↓
WebSocket connects → emit('call.connected')
  ↓
Twilio sends audio → emit('audio.received.twilio')
  ↓
convert-audio-format.js listens → converts → emit('audio.converted')
  ↓
send-audio.js listens → Gemini API → emit('ai.request.sent')
  ↓
Gemini responds → emit('ai.response.received')
  ↓
process-gemini-audio.js → converts → emit('audio.converted')
  ↓
send-audio.js → Twilio → emit('audio.forwarded')
```

## Migration Strategy

### Phase 1: Foundation (Week 1)
**Goal**: Establish event system and basic infrastructure

1. Create `core/event-bus.js`
2. Create `core/logger.js` 
3. Create `core/config.js`
4. Create `services/storage/session-store.js`
5. Write tests for core infrastructure

**Deliverables**:
- Working event system
- Simple session storage
- Basic logging
- 20+ passing tests

### Phase 2: Audio Decomposition (Week 2)
**Goal**: Extract audio processing into micro-components

1. Extract audio format conversion functions
2. Create audio event handlers
3. Build audio-pipeline.js orchestrator
4. Remove audio logic from SessionManager
5. Test audio flow end-to-end

**Deliverables**:
- 5-6 audio handler files (50 lines each)
- Audio pipeline orchestrator
- Simplified SessionManager
- 30+ audio tests passing

### Phase 3: Session Simplification (Week 3)
**Goal**: Break down session management

1. Extract session creation logic
2. Extract session update logic
3. Extract session recovery logic
4. Create session-pipeline.js
5. Remove 400+ lines from SessionManager

**Deliverables**:
- 4 session handler files
- Session pipeline orchestrator
- SessionManager < 100 lines
- All session tests passing

### Phase 4: WebSocket Refactoring (Week 4)
**Goal**: Simplify WebSocket message handling

1. Create one handler per message type
2. Remove complex routing logic
3. Use events for all communication
4. Simplify connection management

**Deliverables**:
- 8-10 message handler files
- Simplified WebSocket setup
- Event-based message flow
- WebSocket tests passing

### Phase 5: Service Extraction (Week 5)
**Goal**: Create clean service interfaces

1. Extract Twilio API calls
2. Extract Gemini API calls
3. Create storage services
4. Remove external calls from handlers

**Deliverables**:
- 10-12 service files
- Clean API boundaries
- No direct external calls in handlers
- Service tests complete

### Phase 6: API Layer Cleanup (Week 6)
**Goal**: Simplify HTTP endpoints

1. One file per endpoint
2. Minimal logic in routes
3. Event-based processing
4. Clean error handling

**Deliverables**:
- 5-8 API endpoint files
- Event-driven API layer
- Simplified error handling
- API tests passing

## Implementation Guidelines

### For Each New Component

1. **Start Small**
   ```javascript
   // ✅ Good: Single purpose, clear boundaries
   export const convertMulawToPcm = (mulawBuffer) => {
     // 40 lines of conversion logic
     return pcmBuffer;
   };
   ```

2. **Use Events for Communication**
   ```javascript
   // ✅ Good: Loose coupling via events
   import { emit } from '../core/event-bus.js';
   
   export const processAudio = (callSid, audio) => {
     const result = convertAudio(audio);
     emit('audio.converted', { callSid, result });
   };
   ```

3. **Keep State in Stores**
   ```javascript
   // ✅ Good: Centralized state management
   import { getSession, updateSession } from '../services/storage/session-store.js';
   
   export const handleSessionUpdate = ({ callSid, changes }) => {
     const session = getSession(callSid);
     updateSession(callSid, { ...session, ...changes });
   };
   ```

4. **Write Tests First**
   ```javascript
   // ✅ Good: Test the component in isolation
   test('converts audio format correctly', () => {
     const input = Buffer.from([0x80, 0x80]);
     const output = convertMulawToPcm(input);
     assert.equal(output.length, 4);
   });
   ```

## Success Metrics

### Code Quality Metrics
- **File size**: No file > 150 lines
- **Import depth**: Maximum 3 levels
- **Circular dependencies**: Zero
- **Test coverage**: > 80%

### Development Metrics
- **AI comprehension**: Can understand any file completely
- **Change isolation**: Modifications affect < 3 files
- **Bug fix time**: Reduced by 50%
- **Feature addition**: No existing file modifications needed

### System Metrics
- **Memory usage**: Reduced by 30%
- **CPU usage**: Reduced by 20%
- **Startup time**: < 2 seconds
- **Error recovery**: < 5 seconds

## Common Patterns

### Event Handler Pattern
```javascript
// handlers/audio/process-twilio-audio.js
import { on, emit } from '../../core/event-bus.js';
import { convertMulawToPcm } from './convert-audio-format.js';

export const initializeHandler = () => {
  on('audio.received.twilio', async ({ callSid, base64Audio }) => {
    try {
      const mulawBuffer = Buffer.from(base64Audio, 'base64');
      const pcmBuffer = convertMulawToPcm(mulawBuffer);
      
      emit('audio.converted', {
        callSid,
        buffer: pcmBuffer,
        format: 'pcm16',
        source: 'twilio'
      });
    } catch (error) {
      emit('audio.error', { callSid, error: error.message });
    }
  });
};
```

### Service Pattern
```javascript
// services/gemini/send-audio.js
import { emit } from '../../core/event-bus.js';

export const sendAudioToGemini = async (sessionId, audioBuffer) => {
  try {
    const response = await geminiClient.sendAudio(audioBuffer);
    emit('ai.response.received', { sessionId, response });
    return { success: true };
  } catch (error) {
    emit('ai.error', { sessionId, error: error.message });
    return { success: false, error };
  }
};
```

### Pipeline Pattern
```javascript
// pipelines/audio-pipeline.js
import { on, emit } from '../core/event-bus.js';

export const initializePipeline = () => {
  // Twilio → Gemini flow
  on('audio.received.twilio', () => emit('audio.needs.conversion', { target: 'gemini' }));
  on('audio.converted', ({ source }) => {
    if (source === 'twilio') emit('audio.ready.for.gemini');
  });
  
  // Gemini → Twilio flow
  on('ai.response.received', () => emit('audio.needs.conversion', { target: 'twilio' }));
  on('audio.converted', ({ source }) => {
    if (source === 'gemini') emit('audio.ready.for.twilio');
  });
};
```

## Risks and Mitigations

### Risk 1: Breaking Existing Functionality
**Mitigation**: 
- Maintain old code during migration
- Run parallel systems during transition
- Comprehensive testing at each phase
- Feature flags for gradual rollout

### Risk 2: Performance Degradation
**Mitigation**:
- Benchmark before and after each phase
- Profile event system overhead
- Optimize hot paths
- Use event batching where needed

### Risk 3: Increased Complexity
**Mitigation**:
- Clear documentation for event flows
- Visual event flow diagrams
- Debugging tools for event tracing
- Training for team members

## Conclusion

This refactoring plan will transform the codebase from a tightly-coupled monolith into a loosely-coupled, event-driven system of micro-components. This new architecture will:

1. **Improve AI Development**: Small, focused files that AI can fully understand
2. **Enhance Maintainability**: Clear boundaries and single responsibilities
3. **Increase Reliability**: Isolated components with explicit dependencies
4. **Enable Scalability**: Event-driven architecture can scale horizontally
5. **Simplify Testing**: Pure functions and event flows are easy to test

The migration will take approximately 6 weeks, with each phase delivering working software. The end result will be a codebase that is not only easier for AI assistants to work with but also more maintainable, testable, and scalable for human developers.