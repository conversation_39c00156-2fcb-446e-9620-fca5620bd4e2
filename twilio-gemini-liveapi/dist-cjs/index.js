"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("dotenv/config");
const fastify_1 = __importDefault(require("fastify"));
const formbody_1 = __importDefault(require("@fastify/formbody"));
const websocket_1 = __importDefault(require("@fastify/websocket"));
const cors_1 = __importDefault(require("@fastify/cors"));
const static_1 = __importDefault(require("@fastify/static"));
const compress_1 = __importDefault(require("@fastify/compress"));
const rate_limit_1 = __importDefault(require("@fastify/rate-limit"));
const url_1 = require("url");
const path_1 = require("path");
const path_2 = __importDefault(require("path"));
// Import modular components
const client_1 = require("./src/gemini/client");
const voice_manager_1 = require("./src/gemini/voice-manager");
const model_manager_1 = require("./src/gemini/model-manager");
const context_manager_1 = require("./src/session/context-manager");
const session_manager_1 = require("./src/session/session-manager");
const health_monitor_1 = require("./src/session/health-monitor");
const recovery_manager_1 = require("./src/session/recovery-manager");
const summary_manager_1 = require("./src/session/summary-manager");
const lifecycle_manager_1 = require("./src/session/lifecycle-manager");
const script_manager_1 = require("./src/scripts/script-manager");
const audio_processor_1 = require("./src/audio/audio-processor");
const transcription_manager_1 = require("./src/audio/transcription-manager");
const heartbeat_manager_1 = require("./src/websocket/heartbeat-manager");
const handlers_1 = require("./src/websocket/handlers");
const routes_1 = require("./src/api/routes");
const management_1 = require("./src/api/management");
const testing_1 = require("./src/api/testing");
const shared_auth_1 = require("./src/middleware/shared-auth");
const error_handler_1 = require("./src/middleware/error-handler");
const logger_1 = require("./src/utils/logger");
const timer_manager_1 = require("./src/utils/timer-manager");
const bounded_map_1 = require("./src/utils/bounded-map");
const memory_monitor_1 = require("./src/utils/memory-monitor");
// Get directory paths
const __filename = (0, url_1.fileURLToPath)(import.meta.url);
const __dirname = (0, path_1.dirname)(__filename);
// Load configuration system
const config_1 = require("./src/config/config");
// Validate configuration on startup
(0, config_1.validateConfig)();
// Health check routes registration
function registerHealthCheckRoutes(fastify, dependencies) {
    // Basic health check
    fastify.get('/health', async (_request, reply) => {
        const memoryHealth = memory_monitor_1.memoryMonitor.getHealthSummary();
        const healthStatus = {
            status: memoryHealth.status === 'ok' ? 'healthy' : 'degraded',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: {
                ...process.memoryUsage(),
                status: memoryHealth.status,
                memoryMB: memoryHealth.memoryMB,
                heapUsedMB: memoryHealth.heapUsedMB
            },
            version: process.version,
            environment: process.env.NODE_ENV || 'development'
        };
        const statusCode = memoryHealth.status === 'critical' ? 503 : 200;
        return reply.code(statusCode).send(healthStatus);
    });
    // Detailed health check with component status
    fastify.get('/health/detailed', async (_request, reply) => {
        const { activeConnections, sessionManager, contextManager, lifecycleManager, recoveryManager, healthMonitor } = dependencies;
        const detailedHealth = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            components: {
                activeConnections: {
                    status: 'healthy',
                    count: activeConnections?.size || 0,
                    maxSize: activeConnections?.maxSize || 'unknown'
                },
                sessionManager: {
                    status: sessionManager ? 'healthy' : 'unavailable',
                    metrics: sessionManager ? 'available' : 'unavailable'
                },
                contextManager: {
                    status: contextManager ? 'healthy' : 'unavailable',
                    contexts: contextManager?.getContextStats?.() || 'unavailable'
                },
                lifecycleManager: {
                    status: lifecycleManager ? 'healthy' : 'unavailable'
                },
                recoveryManager: {
                    status: recoveryManager ? 'healthy' : 'unavailable',
                    metrics: recoveryManager?.getRecoveryMetrics?.() || 'unavailable'
                },
                healthMonitor: {
                    status: healthMonitor ? 'healthy' : 'unavailable'
                }
            }
        };
        reply.code(200).send(detailedHealth);
    });
    // Readiness check (for Kubernetes/Docker)
    fastify.get('/ready', async (_request, reply) => {
        const { activeConnections, sessionManager } = dependencies;
        const isReady = !!(activeConnections && sessionManager);
        if (isReady) {
            reply.code(200).send({ status: 'ready', timestamp: new Date().toISOString() });
        }
        else {
            reply.code(503).send({ status: 'not ready', timestamp: new Date().toISOString() });
        }
    });
    // Liveness check (for Kubernetes/Docker)
    fastify.get('/live', async (_request, reply) => {
        reply.code(200).send({ status: 'alive', timestamp: new Date().toISOString() });
    });
}
// Enhanced environment variable validation
function validateEnvironmentVariables() {
    const errors = [];
    const requiredVars = [
        'GEMINI_API_KEY',
        'TWILIO_ACCOUNT_SID',
        'TWILIO_AUTH_TOKEN',
        'PUBLIC_URL'
    ];
    const optionalVars = [
        'DEEPGRAM_API_KEY',
        'SUMMARY_GENERATION_PROMPT',
        'AI_PREPARE_MESSAGE'
    ];
    // Check required variables
    for (const varName of requiredVars) {
        if (!process.env[varName]) {
            errors.push(`Missing required environment variable: ${varName}`);
        }
    }
    // Warn about optional variables
    for (const varName of optionalVars) {
        if (!process.env[varName]) {
            logger_1.logger.warn(`⚠️ Optional environment variable not set: ${varName}`);
        }
    }
    // Validate URL format
    if (process.env.PUBLIC_URL && !process.env.PUBLIC_URL.match(/^https?:\/\/.+/)) {
        errors.push('PUBLIC_URL must be a valid HTTP/HTTPS URL');
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
// Validate environment variables
const envValidation = validateEnvironmentVariables();
if (!envValidation.isValid) {
    logger_1.logger.error('❌ Environment validation failed:', envValidation.errors);
    process.exit(1);
}
logger_1.logger.info('✅ Environment variables validated successfully');
// Global error handlers for production safety
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('🚨 Unhandled Promise Rejection', { promise, reason, stack: reason?.stack });
    // Log additional context for debugging
    logger_1.logger.error('🚨 Process info', {
        pid: process.pid,
        memory: process.memoryUsage(),
        uptime: process.uptime()
    });
    // In production, might want to restart after cleanup
    if (process.env.NODE_ENV === 'production') {
        logger_1.logger.error('🚨 Production environment: initiating graceful shutdown');
        process.exit(1);
    }
});
process.on('uncaughtException', (error) => {
    logger_1.logger.error('🚨 Uncaught Exception', { error });
    logger_1.logger.error('🚨 Process info', {
        pid: process.pid,
        memory: process.memoryUsage(),
        uptime: process.uptime()
    });
    // Uncaught exceptions are always fatal
    logger_1.logger.error('🚨 Fatal error: process will exit');
    process.exit(1);
});
process.on('warning', (warning) => {
    logger_1.logger.warn('⚠️ Node.js Warning', { name: warning.name, message: warning.message, stack: warning.stack });
});
// Get default voice and model from configuration
const GEMINI_DEFAULT_VOICE = config_1.config.ai.gemini.defaultVoice;
const GEMINI_DEFAULT_MODEL = config_1.config.ai.gemini.defaultModel;
// Get other configuration constants
const SUMMARY_GENERATION_PROMPT = config_1.config.prompts.summaryGeneration;
const AI_PREPARE_MESSAGE = config_1.config.prompts.aiPrepareOutbound;
const TWILIO_ACCOUNT_SID = config_1.config.auth.twilio.accountSid;
const TWILIO_AUTH_TOKEN = config_1.config.auth.twilio.authToken;
const PUBLIC_URL = config_1.config.server.publicUrl;
const PORT = config_1.config.server.port;
logger_1.logger.info('🚀 Twilio Gemini Live API Server starting...');
logger_1.logger.info(`📝 Using Gemini API Key: ${config_1.config.auth.gemini.apiKey ? 'SET ✅' : 'NOT SET ❌'}`);
logger_1.logger.info(`📞 Twilio Config: ${config_1.config.auth.twilio.accountSid ? 'SET ✅' : 'NOT SET ❌'}`);
logger_1.logger.info(`🎤 Deepgram API Key: ${config_1.config.auth.deepgram.apiKey ? 'SET ✅' : 'NOT SET ❌'}`);
logger_1.logger.info(`🔗 Public URL: ${config_1.config.server.publicUrl || 'NOT SET'}`);
logger_1.logger.info(`🤖 Default Model: ${GEMINI_DEFAULT_MODEL}`);
logger_1.logger.info(`🎵 Default Voice: ${GEMINI_DEFAULT_VOICE}`);
// Initialize core components
const geminiClient = (0, client_1.initializeGeminiClient)(config_1.config.auth.gemini.apiKey);
const contextManager = new context_manager_1.ContextManager();
// Tracks active connections with automatic cleanup when capacity is reached
const activeConnections = new bounded_map_1.BoundedMap(1000); // Increased from 500 to 1000 concurrent connections
// Pass activeConnections reference
const sessionManager = new session_manager_1.SessionManager(contextManager, geminiClient, activeConnections);
const healthMonitor = new health_monitor_1.ConnectionHealthMonitor();
const summaryManager = new summary_manager_1.SessionSummaryManager();
const lifecycleManager = new lifecycle_manager_1.SessionLifecycleManager(contextManager, healthMonitor, summaryManager);
// Set the lifecycle manager reference for cleanup
activeConnections.setLifecycleManager(lifecycleManager);
const recoveryManager = new recovery_manager_1.SessionRecoveryManager(contextManager, healthMonitor, sessionManager);
const scriptManager = new script_manager_1.ScriptManager();
const audioProcessor = new audio_processor_1.AudioProcessor();
const transcriptionManager = new transcription_manager_1.TranscriptionManager();
// Initialize Fastify server
const fastify = (0, fastify_1.default)({ logger: false });
// Register error handlers
(0, error_handler_1.registerErrorHandlers)(fastify);
(0, error_handler_1.registerErrorLoggingHook)(fastify);
// Register plugins
await fastify.register(websocket_1.default);
await fastify.register(cors_1.default, {
    origin: process.env.NODE_ENV === 'production'
        ? ['https://twilio-gemini.verduona.com', 'https://gemini-api.verduona.com', 'http://localhost:3011']
        : ['http://localhost:3000', 'http://localhost:3011', 'http://localhost:3101'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    optionsSuccessStatus: 200 // Use 200 instead of 204 for better compatibility
});
await fastify.register(formbody_1.default);
// Register authentication middleware
fastify.addHook('preHandler', shared_auth_1.validateSharedAuth);
// Enhanced CORS headers handling for development - AFTER auth middleware
fastify.addHook('onRequest', async (request, reply) => {
    const origin = request.headers.origin;
    const allowedOrigins = process.env.NODE_ENV === 'production'
        ? ['https://twilio-gemini.verduona.com', 'https://gemini-api.verduona.com', 'http://localhost:3011']
        : ['http://localhost:3000', 'http://localhost:3011', 'http://localhost:3101'];
    // Always set CORS headers for all requests from allowed origins
    if (origin && allowedOrigins.includes(origin)) {
        reply.header('Access-Control-Allow-Origin', origin);
        reply.header('Access-Control-Allow-Credentials', 'true');
        reply.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        reply.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');
        reply.header('Access-Control-Max-Age', '3600');
    }
    // Handle preflight OPTIONS requests early
    if (request.method === 'OPTIONS') {
        if (origin && allowedOrigins.includes(origin)) {
            reply.header('Access-Control-Allow-Origin', origin);
            reply.header('Access-Control-Allow-Credentials', 'true');
            reply.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            reply.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');
            reply.header('Access-Control-Max-Age', '3600');
        }
        return reply.code(200).send();
    }
});
// Add security headers middleware
fastify.addHook('onSend', async (request, reply, payload) => {
    reply.header('X-Frame-Options', 'DENY');
    reply.header('X-Content-Type-Options', 'nosniff');
    reply.header('X-XSS-Protection', '1; mode=block');
    reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    if (process.env.NODE_ENV === 'production') {
        reply.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        reply.header('Content-Security-Policy', 'default-src \'self\'; ' +
            'connect-src \'self\' wss: ws:; ' +
            'script-src \'self\' \'unsafe-inline\'; ' +
            'style-src \'self\' \'unsafe-inline\'; ' +
            'media-src \'self\' blob:; ' +
            'font-src \'self\';');
    }
    return payload;
});
// Register compression for better performance
await fastify.register(compress_1.default, {
    global: true,
    threshold: 1024 // Only compress responses larger than 1KB
});
// Register rate limiting for security
await fastify.register(rate_limit_1.default, {
    max: 100, // Maximum 100 requests
    timeWindow: '1 minute', // Per minute
    skipOnError: true // Don't count failed requests
});
// Register static file serving for management interfaces
if (!fastify.hasReplyDecorator || !fastify.hasReplyDecorator('sendFile')) {
    await fastify.register(static_1.default, {
        root: path_2.default.join(__dirname, 'static'),
        prefix: '/static/'
    });
}
else {
    logger_1.logger.warn('⚠️ Static routes already registered - skipping duplicate registration');
}
// Dependencies object for modules
const dependencies = {
    config: config_1.config,
    geminiClient,
    contextManager,
    sessionManager,
    healthMonitor,
    summaryManager,
    lifecycleManager,
    recoveryManager,
    scriptManager,
    audioProcessor,
    transcriptionManager,
    activeConnections,
    voiceManager: voice_manager_1.voiceManager,
    modelManager: model_manager_1.modelManager,
    GEMINI_DEFAULT_VOICE,
    GEMINI_DEFAULT_MODEL,
    SUMMARY_GENERATION_PROMPT,
    AI_PREPARE_MESSAGE,
    TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN,
    PUBLIC_URL
};
// Register health check endpoints first
logger_1.logger.info('🔧 Registering health check endpoints...');
registerHealthCheckRoutes(fastify, dependencies);
logger_1.logger.info('✅ Health check endpoints registered');
// Register API routes first so decorators like getNextCallConfig are available
logger_1.logger.info('🔧 Calling registerApiRoutes...');
try {
    (0, routes_1.registerApiRoutes)(fastify, dependencies);
    logger_1.logger.info('✅ API routes registration completed');
}
catch (error) {
    logger_1.logger.error('❌ Error in registerApiRoutes', { error: error });
    throw error;
}
// Expose getNextCallConfig to WebSocket handlers
if (typeof fastify.getNextCallConfig === 'function') {
    dependencies.getNextCallConfig = fastify.getNextCallConfig;
}
if (fastify.callConfigEmitter) {
    dependencies.callConfigEmitter = fastify.callConfigEmitter;
}
// Register WebSocket handlers after API routes
logger_1.logger.info('🔧 Registering WebSocket handlers...');
(0, handlers_1.registerWebSocketHandlers)(fastify, dependencies);
logger_1.logger.info('✅ WebSocket handlers registered');
// Register management routes
logger_1.logger.info('🔧 Registering management routes...');
(0, management_1.registerManagementRoutes)(fastify, dependencies);
logger_1.logger.info('✅ Management routes registered');
// Register testing routes
logger_1.logger.info('🔧 Registering testing routes...');
(0, testing_1.registerTestingRoutes)(fastify, dependencies);
logger_1.logger.info('✅ Testing routes registered');
// === MANAGEMENT INTERFACE ROUTES ===
// Incoming call management interface (secured)
fastify.get('/incoming', { preHandler: shared_auth_1.validateSharedAuth }, async (_request, reply) => {
    try {
        const html = await Promise.resolve().then(() => __importStar(require('fs/promises'))).then(fs => fs.readFile(path_2.default.join(__dirname, 'incoming-manager.html'), 'utf8'));
        reply.header('Content-Type', 'text/html');
        return html;
    }
    catch (error) {
        logger_1.logger.error('❌ Error loading incoming call management interface', { error: error });
        return reply.code(500).send({ error: 'Failed to load incoming call management interface' });
    }
});
// Outbound scripts management interface (secured)
fastify.get('/outbound-scripts', { preHandler: shared_auth_1.validateSharedAuth }, async (_request, reply) => {
    try {
        const html = await Promise.resolve().then(() => __importStar(require('fs/promises'))).then(fs => fs.readFile(path_2.default.join(__dirname, 'outbound-scripts-manager.html'), 'utf8'));
        reply.header('Content-Type', 'text/html');
        return html;
    }
    catch (error) {
        logger_1.logger.error('❌ Error loading outbound scripts management interface', { error: error });
        return reply.code(500).send({ error: 'Failed to load outbound scripts management interface' });
    }
});
// Redirect route for backward compatibility
fastify.get('/scripts', async (_request, reply) => {
    return reply.redirect('/outbound-scripts');
});
// Preload campaign scripts into cache
await scriptManager.preloadScripts();
// Cleanup old contexts and sessions periodically using timer manager
timer_manager_1.timerManager.setInterval('global_cleanup', () => {
    contextManager.cleanupOldContexts();
    lifecycleManager.cleanup();
}, 300000); // Every 5 minutes
// Start the server
const start = async () => {
    try {
        await fastify.listen({
            port: PORT,
            host: '0.0.0.0'
        });
        logger_1.logger.info(`🚀 Server listening on port ${PORT}`);
        logger_1.logger.info(`🔗 WebSocket endpoint: ws://localhost:${PORT}/media-stream`);
        logger_1.logger.info(`🧪 Local audio testing: ws://localhost:${PORT}/local-audio-session`);
        logger_1.logger.info(`🏥 Health check: http://localhost:${PORT}/health`);
        logger_1.logger.info(`📊 API documentation: http://localhost:${PORT}/`);
        // Start memory monitoring in production
        if (process.env.NODE_ENV === 'production') {
            memory_monitor_1.memoryMonitor.startMonitoring();
            logger_1.logger.info('📊 Memory monitoring started for production');
        }
        // Start memory monitoring in production
        if (process.env.NODE_ENV === 'production') {
            memory_monitor_1.memoryMonitor.startMonitoring();
            logger_1.logger.info('📊 Memory monitoring started');
        }
        // Log voice and model configuration
        model_manager_1.modelManager.logConfiguration();
        const voiceInfo = voice_manager_1.voiceManager.getVoiceCharacteristics(GEMINI_DEFAULT_VOICE)?.characteristics || 'unknown';
        logger_1.logger.info(`🎤 Default Voice: ${GEMINI_DEFAULT_VOICE} (${voiceInfo})`);
        logger_1.logger.info(`🎵 Available Voices: ${Object.keys(voice_manager_1.voiceManager.getAvailableVoices()).join(', ')}`);
        logger_1.logger.info(`🔧 Voice Selection: ${voice_manager_1.voiceManager.isVoiceSelectionEnabled() ? 'Enabled' : 'Disabled'}`);
        // Graceful shutdown handlers
        const gracefulShutdown = async (signal) => {
            logger_1.logger.info(`\n🛑 Received ${signal}, initiating graceful shutdown...`);
            try {
                await performShutdownSequence();
                logger_1.logger.info('✅ Graceful shutdown completed');
                process.exit(0);
            }
            catch (error) {
                logger_1.logger.error('❌ Error during graceful shutdown', { error: error });
                process.exit(1);
            }
        };
        const performShutdownSequence = async () => {
            // Stop accepting new connections
            logger_1.logger.info('🔒 Stopping server from accepting new connections...');
            await fastify.close();
            // Clean up all manager resources
            logger_1.logger.info('🧹 Cleaning up session management resources...');
            // Stop all WebSocket heartbeats
            heartbeat_manager_1.globalHeartbeatManager.stopAllHeartbeats();
            // Clean up managers
            await cleanupManagers();
            // Clean up all active sessions
            await cleanupActiveSessions();
            // Clean up periodic intervals using timer manager
            logger_1.logger.info('🗑️ Clearing periodic intervals...');
            timer_manager_1.timerManager.clearAll();
            // Clean up all Maps and Sets
            logger_1.logger.info('🗑️ Clearing data structures...');
            activeConnections.clear();
        };
        const cleanupManagers = async () => {
            const managers = [
                { obj: healthMonitor, method: 'stopHealthChecks', name: 'healthMonitor' },
                { obj: contextManager, method: 'clearAllContexts', name: 'contextManager' },
                { obj: recoveryManager, method: 'cleanup', name: 'recoveryManager' },
                { obj: summaryManager, method: 'cleanup', name: 'summaryManager' },
                { obj: transcriptionManager, method: 'cleanup', name: 'transcriptionManager' }
            ];
            for (const { obj, method, name } of managers) {
                if (obj && obj[method]) {
                    try {
                        obj[method]();
                    }
                    catch (error) {
                        logger_1.logger.warn(`⚠️ Error cleaning up ${name}`, { error });
                    }
                }
            }
            // Session manager specific cleanup
            if (sessionManager) {
                for (const [sessionId] of sessionManager.sessionMetrics) {
                    sessionManager.cleanupSession(sessionId);
                }
            }
        };
        const cleanupActiveSessions = async () => {
            if (!lifecycleManager) {
                return;
            }
            const activeSessions = lifecycleManager.getActiveSessions();
            logger_1.logger.info(`🔚 Ending ${activeSessions.length} active sessions...`);
            for (const sessionId of activeSessions) {
                try {
                    const connectionData = activeConnections.get(sessionId);
                    await lifecycleManager.forceEndSession(sessionId, connectionData, 'server_shutdown');
                }
                catch (error) {
                    logger_1.logger.warn(`⚠️ Error ending session ${sessionId}`, { error: error });
                }
            }
        };
        // Register shutdown handlers
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart
        // Handle PM2 graceful reload
        process.on('message', (msg) => {
            if (msg === 'shutdown') {
                gracefulShutdown('PM2_SHUTDOWN');
            }
        });
        logger_1.logger.info('✅ Server is ready to handle calls!');
    }
    catch (err) {
        logger_1.logger.error('❌ Error starting server', { error: err });
        process.exit(1);
    }
};
start();
//# sourceMappingURL=index.js.map