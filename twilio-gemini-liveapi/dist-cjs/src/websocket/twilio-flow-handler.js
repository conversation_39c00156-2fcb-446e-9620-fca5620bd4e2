"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleTwilioFlow = handleTwilioFlow;
const session_utils_1 = require("./session-utils");
const logger_1 = require("../utils/logger");
const heartbeat_manager_1 = require("./heartbeat-manager");
const websocket_1 = require("../types/websocket");
const twilio_session_helpers_1 = require("./twilio-session-helpers");
const performance_optimizations_1 = require("./performance-optimizations");
const message_schemas_1 = require("./message-schemas");
const startupOptimizer = new performance_optimizations_1.SessionStartupOptimizer({
    enableFastStart: true,
    skipNonEssentialChecks: false,
    preloadGeminiSession: false,
    parallelInitialization: true,
    timeoutMs: 8000
});
const configLoader = new performance_optimizations_1.FastConfigLoader();
const qualityMonitor = new performance_optimizations_1.ConnectionQualityMonitor();
// Twilio flow handler for both inbound and outbound calls
function handleTwilioFlow(connection, deps) {
    const { sessionManager, contextManager, activeConnections, healthMonitor, summaryManager, lifecycleManager, recoveryManager, transcriptionManager, flowType, getSessionConfig, isIncomingCall, config } = deps;
    const rawCallSid = connection.query?.CallSid;
    const callSid = Array.isArray(rawCallSid) ? rawCallSid[0] : (rawCallSid || `twilio-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`);
    logger_1.websocketLogger.info(`Twilio ${flowType} call started`, { callSid, flowType, isIncomingCall });
    const ws = (connection.socket || connection);
    // CRITICAL FIX: Don't use persistent session variables - always get fresh state from activeConnections
    // This prevents stale state issues between multiple calls
    logger_1.websocketLogger.debug('Starting fresh Twilio flow handler', { callSid });
    // Store event listeners for cleanup
    const eventListeners = new Map();
    const messageHandler = async (message) => {
        try {
            const rawData = JSON.parse(message.toString());
            // Validate message with Zod schema
            const validation = (0, message_schemas_1.validateTwilioMessage)(rawData);
            if (!validation.success) {
                logger_1.websocketLogger.warn('❌ Invalid Twilio message format', {
                    callSid,
                    error: (0, message_schemas_1.formatValidationError)(validation.error),
                    rawData
                });
                return;
            }
            const data = validation.data;
            // Twilio uses 'event' field for message type. Map it to our
            // internal message name so downstream handlers see consistent types.
            const rawType = data.event;
            const messageType = websocket_1.TWILIO_EVENT_MAP[rawType] || rawType;
            logger_1.websocketLogger.debug(`✅ Twilio ${flowType} message validated`, {
                callSid,
                event: data.event,
                messageType
            });
            switch (messageType) {
                case 'connected': {
                    // CRITICAL FIX: Handle 'connected' event that Twilio sends for some calls
                    logger_1.websocketLogger.debug('Twilio CONNECTED event received', { callSid, data });
                    logger_1.websocketLogger.info('Twilio connected event received', { callSid });
                    // Update connection state to indicate Twilio WebSocket is ready
                    const connectionData = activeConnections.get(callSid);
                    if (connectionData) {
                        connectionData.twilioConnected = true;
                        connectionData.lastActivity = Date.now();
                        logger_1.websocketLogger.debug('Twilio WebSocket connection confirmed', { callSid });
                        // If we have both Twilio connection and Gemini session, mark as fully ready
                        if (connectionData.geminiSession && connectionData.isSessionActive) {
                            // Mark as fully ready in session manager
                            connectionData.fullyReady = true;
                            logger_1.websocketLogger.info('Session fully ready - both Twilio and Gemini connected', { callSid });
                        }
                    }
                    else {
                        logger_1.websocketLogger.warn('Received connected event but no connection data found', { callSid });
                    }
                    break;
                }
                case 'start-session':
                    // Twilio sends 'start' event when stream begins
                    logger_1.websocketLogger.debug('Twilio START event received', { callSid, data });
                    try {
                        await handleTwilioStartSession(callSid, data, deps, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                    }
                    catch (error) {
                        logger_1.websocketLogger.error(`Failed to handle Twilio start session for ${callSid}`, error instanceof Error ? error : new Error(String(error)));
                        // Send error message to client
                        ws.send(JSON.stringify({
                            type: 'session-error',
                            error: 'Failed to initialize session. Please try again.',
                            critical: true
                        }));
                        // Clean up connection
                        activeConnections.delete(callSid);
                    }
                    break;
                case 'audio': {
                    // Always get fresh session state from activeConnections
                    const connectionData = activeConnections.get(callSid);
                    const geminiSession = connectionData?.geminiSession;
                    const isSessionActive = connectionData?.isSessionActive;
                    await handleTwilioMedia(callSid, data, geminiSession, isSessionActive || false, deps, activeConnections, lifecycleManager, recoveryManager);
                    break;
                }
                case 'mark':
                    logger_1.websocketLogger.debug('Mark received', { callSid });
                    break;
                case 'end-session':
                    logger_1.websocketLogger.info('Stop message received from Twilio', { callSid });
                    await handleTwilioEndSession(callSid, deps, activeConnections, lifecycleManager);
                    break;
                default:
                    logger_1.websocketLogger.warn(`Unknown Twilio message event: ${messageType}`, { callSid });
                    logger_1.websocketLogger.debug('Unknown message received', { callSid, data });
            }
        }
        catch (error) {
            if (error instanceof SyntaxError) {
                logger_1.websocketLogger.error('JSON parsing error for message', { error: error instanceof Error ? error : new Error(String(error)), callSid, message: message.toString() });
                logger_1.websocketLogger.error(`JSON parsing error for Twilio ${flowType} message`, error instanceof Error ? error : new Error(String(error)));
            }
            else {
                logger_1.websocketLogger.error(`Error processing Twilio ${flowType} message`, error instanceof Error ? error : new Error(String(error)), callSid);
            }
        }
    };
    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    ws.on('message', messageHandler);
    const closeHandler = async (code, reason) => {
        logger_1.websocketLogger.info(`Twilio ${flowType} connection closed`, {
            callSid,
            code,
            reason: reason ? reason.toString() : 'No reason',
            closeCode: code
        });
        // Stop heartbeat monitoring
        heartbeat_manager_1.globalHeartbeatManager.stopHeartbeat(callSid);
        qualityMonitor.stopMonitoring(callSid);
        startupOptimizer.cleanup(callSid);
        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // CRITICAL FIX: Don't end session immediately on WebSocket close
            // Twilio WebSocket can close/reconnect during normal call flow
            // Only end session if this is a deliberate call termination
            // Check if this is a normal close (1000) or abnormal close
            // Code 1005 means "no status received" and is common for Twilio WebSocket connections
            // const isNormalClose = code === 1000 || code === 1005; // Currently unused
            const isCallStillActive = connectionData.isSessionActive && connectionData.geminiSession;
            // CRITICAL FIX: Don't end session on WebSocket close unless call is actually terminated
            // Twilio WebSocket can close while call is still active
            // Only end session if we received a 'stop' message or call status indicates completion
            if (!isCallStillActive || connectionData.callCompleted || connectionData.stopReceived) {
                // Session already inactive or call explicitly ended - safe to end
                logger_1.websocketLogger.info(`Ending session due to inactive session or explicit call termination`, {
                    callSid,
                    code,
                    isCallStillActive,
                    callCompleted: connectionData.callCompleted,
                    stopReceived: connectionData.stopReceived
                });
                if (lifecycleManager) {
                    await lifecycleManager.endSession(callSid, connectionData, 'twilio_connection_closed');
                }
                else {
                    (0, session_utils_1.endSession)(callSid, deps, 'twilio_connection_closed');
                }
            }
            else {
                // WebSocket closed but session still active - don't end session
                logger_1.websocketLogger.warn(`WebSocket closed but session still active - keeping session alive`, {
                    callSid,
                    code,
                    isCallStillActive
                });
                connectionData.wsDisconnected = true;
                connectionData.lastDisconnectTime = Date.now();
                // Don't end session - let call status webhook handle actual call termination
            }
        }
        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler);
        }
        eventListeners.clear();
        // CRITICAL: Clean up connection data to prevent stale state
        logger_1.websocketLogger.debug('Cleaning up connection data to prevent stale state', { callSid });
        activeConnections.delete(callSid);
    };
    const errorHandler = async (error) => {
        logger_1.websocketLogger.error(`Twilio ${flowType} error`, error instanceof Error ? error : new Error(String(error)));
        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // Try recovery first if possible
            if (recoveryManager && contextManager.canRecover(callSid)) {
                logger_1.websocketLogger.info('Twilio WebSocket error detected, attempting session recovery', { callSid });
                contextManager.markSessionInterrupted(callSid, 'twilio_websocket_error');
                (0, session_utils_1.scheduleRecovery)(callSid, 'twilio_websocket_error', recoveryManager, activeConnections);
            }
            else {
                // Mark connection as errored but don't immediately end session
                // Let the call status webhook or explicit user action end the session
                logger_1.websocketLogger.warn('WebSocket error but no recovery available - marking connection as errored', { callSid });
                connectionData.wsError = true;
                connectionData.lastErrorTime = Date.now();
                // Clean up Deepgram transcription
                if (connectionData.deepgramConnection) {
                    deps.transcriptionManager.closeTranscription(callSid);
                }
                // Only end session if this is a critical error and session is not active
                if (!connectionData.isSessionActive || !connectionData.geminiSession) {
                    qualityMonitor.stopMonitoring(callSid);
                    startupOptimizer.cleanup(callSid);
                    (0, session_utils_1.endSession)(callSid, { ...deps, transcriptionManager: deps.transcriptionManager }, 'twilio_connection_error');
                }
            }
        }
    };
    // Register all event listeners
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}
async function handleTwilioStartSession(callSid, data, deps, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager) {
    logger_1.websocketLogger.info(`Starting Twilio ${flowType} session`, { callSid });
    const { config } = deps;
    startupOptimizer.startTracking(callSid);
    qualityMonitor.startMonitoring(callSid, ws);
    try {
        // Extract Twilio stream information
        const streamInfo = (0, twilio_session_helpers_1.extractTwilioStreamInfo)(data);
        // Get and validate session configuration using cached loader
        const configResult = await configLoader.loadConfig(callSid, () => (0, twilio_session_helpers_1.getValidSessionConfig)(callSid, getSessionConfig, deps, isIncomingCall), true);
        startupOptimizer.markConfigLoaded(callSid);
        const sessionConfig = configResult.config;
        if (!sessionConfig || !sessionConfig.aiInstructions || sessionConfig.aiInstructions.trim().length === 0) {
            const errorMessage = `No AI instructions available for ${flowType} session. Cannot create Gemini session without proper system instructions.`;
            logger_1.websocketLogger.error(errorMessage, {}, callSid);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: errorMessage,
                    code: 'MISSING_AI_INSTRUCTIONS'
                }));
            }
            qualityMonitor.stopMonitoring(callSid);
            startupOptimizer.cleanup(callSid);
            activeConnections.delete(callSid);
            return;
        }
        // Log stream information
        (0, twilio_session_helpers_1.logTwilioStreamInfo)(callSid, streamInfo, sessionConfig);
        // Create and store enhanced connection data
        const connectionData = (0, twilio_session_helpers_1.createTwilioConnectionData)(callSid, ws, streamInfo, sessionConfig, isIncomingCall, flowType);
        activeConnections.set(callSid, connectionData);
        // Track connection health
        healthMonitor.trackConnection(callSid, 'connected', {
            flowType,
            isTwilioCall: true,
            scriptId: sessionConfig.scriptId
        });
        // Create Gemini session with timeout handling
        logger_1.websocketLogger.debug('Creating Gemini session', { callSid });
        const sessionCreationTimeout = 30000; // 30 second timeout
        let geminiSession;
        try {
            geminiSession = await Promise.race([
                sessionManager.createGeminiSession(callSid, sessionConfig, connectionData),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Gemini session creation timeout')), sessionCreationTimeout))
            ]);
        }
        catch (error) {
            logger_1.websocketLogger.error(`Failed to create Gemini session for ${callSid}`, error instanceof Error ? error : new Error(String(error)));
            await (0, twilio_session_helpers_1.handleGeminiSessionFailure)(callSid, flowType, ws, deps, session_utils_1.endSession);
            return;
        }
        if (geminiSession) {
            connectionData.geminiSession = geminiSession;
            startupOptimizer.markGeminiSessionCreated(callSid);
            logger_1.websocketLogger.debug('Gemini session created, waiting for activation', { callSid });
            // Session should be active immediately after creation - no polling needed
            if (!connectionData.isSessionActive) {
                const waitStart = Date.now();
                const maxWaitTime = 1000; // Reduced from 10 seconds to 1 second
                // Give a very brief moment for async session activation, but no polling delays
                while (!connectionData.isSessionActive && (Date.now() - waitStart) < maxWaitTime) {
                    // Yield to event loop once without delay
                    await new Promise(resolve => process.nextTick(resolve));
                }
                if (!connectionData.isSessionActive) {
                    logger_1.websocketLogger.error(`Gemini session failed to activate within ${maxWaitTime}ms for ${callSid}`);
                    await (0, twilio_session_helpers_1.handleGeminiSessionFailure)(callSid, flowType, ws, deps, session_utils_1.endSession);
                    return;
                }
                logger_1.websocketLogger.info('Gemini session activated successfully', { callSid, activationTime: Date.now() - waitStart });
            }
            // Start lifecycle management
            (0, twilio_session_helpers_1.startLifecycleManagement)(callSid, lifecycleManager, connectionData, sessionConfig);
            // Start WebSocket heartbeat monitoring
            (0, twilio_session_helpers_1.startTwilioHeartbeat)(callSid, ws, heartbeat_manager_1.globalHeartbeatManager, activeConnections, config);
            // Live API setup is now handled by SessionManager during session creation
            logger_1.websocketLogger.info(`Live API setup handled by SessionManager for ${flowType}`, { callSid });
            // Log session configuration
            (0, twilio_session_helpers_1.logSessionConfiguration)(callSid, sessionConfig, flowType);
            // Send session started message
            (0, twilio_session_helpers_1.sendSessionStartedMessage)(ws, callSid, flowType, sessionConfig.scriptId);
            startupOptimizer.markSessionReady(callSid);
            logger_1.websocketLogger.info(`Twilio ${flowType} session started successfully`, { callSid });
        }
        else {
            // Handle Gemini session creation failure
            await (0, twilio_session_helpers_1.handleGeminiSessionFailure)(callSid, flowType, ws, deps, session_utils_1.endSession);
            return;
        }
    }
    catch (error) {
        (0, twilio_session_helpers_1.handleSessionStartError)(error, flowType, ws);
    }
}
async function handleTwilioMedia(callSid, data, _geminiSession, _isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager) {
    if (data.media && data.media.payload) {
        try {
            // Update activity for session persistence
            lifecycleManager.transitionState(callSid, 'active', 'media_received');
            // Get fresh connection data to check current session state
            const connectionData = activeConnections.get(callSid);
            // CRITICAL VALIDATION: Check connection state before processing audio
            if (!connectionData) {
                logger_1.websocketLogger.error('No connection data found for media processing', {}, callSid);
                return;
            }
            if (!connectionData.geminiSession) {
                logger_1.websocketLogger.error('No Gemini session for media processing', {}, callSid);
                return;
            }
            if (!connectionData.isSessionActive) {
                logger_1.websocketLogger.error('Session not active for media processing', {}, callSid);
                return;
            }
            // Additional validation for session readiness
            if (connectionData.geminiSessionError) {
                logger_1.websocketLogger.error('Gemini session has error', { error: connectionData.geminiSessionError }, callSid);
                return;
            }
            // Check if session is fully ready (both Twilio and Gemini connected)
            if (!connectionData.fullyReady && connectionData.twilioConnected !== undefined) {
                logger_1.websocketLogger.warn('Session not fully ready - processing audio anyway', { callSid, twilioConnected: connectionData.twilioConnected, geminiActive: connectionData.isSessionActive });
            }
            const currentIsSessionActive = connectionData.isSessionActive;
            const currentGeminiSession = connectionData.geminiSession;
            // Convert Twilio μ-law audio to PCM
            const ulawAudio = Buffer.from(data.media.payload, 'base64');
            // Final validation before sending audio
            if (deps.sessionManager && currentGeminiSession && currentIsSessionActive) {
                logger_1.websocketLogger.debug('Sending audio to Gemini - session validated and active', { callSid });
                await deps.sessionManager.sendAudioToGemini(callSid, currentGeminiSession, ulawAudio);
                // Update last activity timestamp
                connectionData.lastActivity = Date.now();
            }
            else {
                logger_1.websocketLogger.warn('Skipping audio send - validation failed', {
                    callSid,
                    hasSessionManager: !!deps.sessionManager,
                    hasGeminiSession: !!currentGeminiSession,
                    isSessionActive: currentIsSessionActive,
                    hasConnectionData: !!connectionData
                });
            }
        }
        catch (error) {
            logger_1.websocketLogger.error(`Error processing Twilio media for ${callSid}`, error instanceof Error ? error : new Error(String(error)));
            // Mark connection as having media processing error
            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                connectionData.lastMediaError = Date.now();
                connectionData.mediaErrorCount = (connectionData.mediaErrorCount || 0) + 1;
            }
            // Attempt recovery if needed
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(callSid, activeConnections)) {
                logger_1.websocketLogger.info('Attempting recovery due to media processing error', { callSid });
                await recoveryManager.recoverSession(callSid, 'media_processing_error', activeConnections);
            }
        }
    }
    else {
        logger_1.websocketLogger.warn('Received media message without payload', { callSid });
    }
}
async function handleTwilioEndSession(callSid, deps, activeConnections, lifecycleManager) {
    logger_1.websocketLogger.info('Ending Twilio session - stop received', { callSid });
    const connectionData = activeConnections.get(callSid);
    if (connectionData) {
        // Mark that stop was received to prevent premature session termination on WebSocket close
        connectionData.stopReceived = true;
        if (lifecycleManager) {
            await lifecycleManager.endSession(callSid, connectionData, 'twilio_stop_received');
        }
        else {
            (0, session_utils_1.endSession)(callSid, deps, 'twilio_stop_received');
        }
    }
    else {
        (0, session_utils_1.endSession)(callSid, deps, 'twilio_stop_received');
    }
    qualityMonitor.stopMonitoring(callSid);
    startupOptimizer.cleanup(callSid);
}
//# sourceMappingURL=twilio-flow-handler.js.map