{"version": 3, "file": "start-session.js", "sourceRoot": "", "sources": ["../../../src/websocket/start-session.ts"], "names": [], "mappings": ";;AAaA,gDA+KC;AAzLD,4CAAkD;AAU3C,KAAK,UAAU,kBAAkB,CACpC,SAAiB,EACjB,IAAuB,EACvB,IAAsB,EACtB,UAA+B,EAC/B,EAAa,EACb,QAAgB,EAChB,cAAuB,EACvB,gBAA2C,EAC3C,iBAA8C,EAC9C,aAAkB,EAClB,gBAAqB,EACrB,cAAmB;IAEnB,IAAI,CAAC;QACD,IAAI,aAAa,GAAG,MAAM,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAEtD,wBAAe,CAAC,IAAI,CAAC,6CAA6C,EAAE;YAChE,SAAS;YACT,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;YAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc;YACxC,oBAAoB,EAAE,IAAI,CAAC,cAAc,EAAE,MAAM,IAAI,CAAC;YACtD,cAAc;YACd,QAAQ;SACX,CAAC,CAAC;QAEH,qFAAqF;QACrF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,wBAAe,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3F,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAC7F,IAAI,YAAY,IAAI,YAAY,CAAC,cAAc,EAAE,CAAC;gBAC9C,wBAAe,CAAC,IAAI,CAAC,4BAA4B,EAAE;oBAC/C,SAAS;oBACT,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,iBAAiB,EAAE,YAAY,CAAC,cAAc,CAAC,MAAM;iBACxD,CAAC,CAAC;gBACH,aAAa,GAAG;oBACZ,GAAG,YAAY;oBACf,cAAc,EAAE,YAAY,CAAC,cAAc,EAAE,uDAAuD;oBACpG,UAAU,EAAE,IAAI;oBAChB,gBAAgB,EAAE,IAAI,CAAC,QAAQ;iBAClC,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,wBAAe,CAAC,IAAI,CAAC,uDAAuD,EAAE;oBAC1E,SAAS;oBACT,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBAC1B,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,gEAAgE;QAChE,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACxC,wBAAe,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBACjD,SAAS;gBACT,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;aAChD,CAAC,CAAC;YACH,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACvD,CAAC;QAED,8CAA8C;QAC9C,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxE,wBAAe,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAC3C,SAAS;gBACT,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,EAAE,EAAE,aAAa,CAAC,KAAK;aAC1B,CAAC,CAAC;QACP,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxE,wBAAe,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAC3C,SAAS;gBACT,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,EAAE,EAAE,aAAa,CAAC,KAAK;aAC1B,CAAC,CAAC;QACP,CAAC;QAED,8DAA8D;QAC9D,IAAI,aAAa,CAAC,cAAc,IAAI,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACnF,OAAO,CAAC,IAAI,CAAC,OAAO,SAAS,gCAAgC,aAAa,CAAC,cAAc,CAAC,MAAM,sCAAsC,CAAC,CAAC;YACxI,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,EAAE,CAAC;QAChF,CAAC;QAED,kFAAkF;QAClF,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,cAAc,IAAI,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtG,MAAM,YAAY,GAAG,oCAAoC,QAAQ,4EAA4E,CAAC;YAC9I,wBAAe,CAAC,KAAK,CAAC,sBAAsB,YAAY,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;YAC3E,wBAAe,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC/C,SAAS,EAAE,CAAC,CAAC,aAAa;gBAC1B,iBAAiB,EAAE,CAAC,CAAC,aAAa,EAAE,cAAc;gBAClD,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,IAAI,CAAC;gBAC7D,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,aAAa,EAAE,QAAQ;gBACjC,UAAU,EAAE,aAAa,EAAE,UAAU;aACxC,EAAE,SAAS,CAAC,CAAC;YAEd,IAAI,EAAE,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBACtB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACnB,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,YAAY;oBACnB,IAAI,EAAE,yBAAyB;iBAClC,CAAC,CAAC,CAAC;YACR,CAAC;YAED,4BAA4B;YAC5B,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACpC,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;QAC3D,CAAC;QAED,wBAAe,CAAC,IAAI,CAAC,mCAAmC,EAAE;YACtD,SAAS;YACT,iBAAiB,EAAE,aAAa,CAAC,cAAc,CAAC,MAAM;SACzD,CAAC,CAAC;QACH,MAAM,kBAAkB,GACpB,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;YAC9C,CAAC,aAAa,CAAC,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC7D,wBAAe,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;QAE1F,MAAM,cAAc,GAAG,oBAAoB,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAC5G,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAEjD,aAAa,CAAC,eAAe,CAAC,SAAS,EAAE,WAAW,EAAE;YAClD,QAAQ;YACR,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,aAAa,CAAC,QAAQ;SACnC,CAAC,CAAC;QAEH,qDAAqD;QACrD,oFAAoF;QACpF,MAAM,sBAAsB,GAAG,aAAa,CAAC,cAAc;YACrC,UAAU,CAAC,KAAK,EAAE,IAAI,KAAK,UAAU;YACrC,UAAU,CAAC,KAAK,EAAE,IAAI,KAAK,SAAS;YACpC,aAAa,CAAC,UAAU,KAAK,UAAU,CAAC;QAE9D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,EAAE;YAC3E,GAAG,aAAa;YAChB,WAAW,EAAE,YAAY;YACzB,cAAc,EAAE,sBAAsB,CAAC,+CAA+C;SACzF,EAAE,cAAc,CAAC,CAAC;QAEnB,cAAc,CAAC,aAAa,GAAG,aAAa,IAAI,SAAS,CAAC;QAE1D,IAAI,eAAe,GAAG,KAAK,CAAC;QAE5B,IAAI,aAAa,EAAE,CAAC;YAChB,eAAe,GAAG,IAAI,CAAC;YACvB,MAAM,iBAAiB,CAAC,SAAS,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;YACzD,IAAI,EAAE,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBACtB,EAAE,CAAC,IAAI,CACH,IAAI,CAAC,SAAS,CAAC;oBACX,IAAI,EAAE,iBAAiB;oBACvB,SAAS;oBACT,QAAQ;oBACR,QAAQ,EAAE,aAAa,CAAC,QAAQ;oBAChC,MAAM,EAAE;wBACJ,KAAK,EAAE,aAAa,CAAC,KAAK;wBAC1B,KAAK,EAAE,aAAa,CAAC,KAAK;wBAC1B,cAAc,EAAE,sBAAsB;wBACtC,oBAAoB,EAAE,CAAC,CAAC,cAAc,CAAC,kBAAkB;qBAC5D;iBACJ,CAAC,CACL,CAAC;YACN,CAAC;QACL,CAAC;QAED,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,CAAC;IAC9C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,IAAI,EAAE,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YACtB,EAAE,CAAC,IAAI,CACH,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,yBAA0B,KAAe,CAAC,OAAO,EAAE,EAAE,CAAC,CACxG,CAAC;QACN,CAAC;QACD,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;IAC3D,CAAC;AACL,CAAC;AAED,SAAS,oBAAoB,CACzB,SAAiB,EACjB,UAA+B,EAC/B,aAAkB,EAClB,QAAgB,EAChB,cAAuB;IAEvB,OAAO;QACH,OAAO,EAAE,CAAC,UAAU,CAAC,MAAM,IAAI,UAAU,CAAc;QACvD,SAAS;QACT,eAAe,EAAE,KAAK;QACtB,gBAAgB,EAAE,KAAK;QACvB,eAAe,EAAE,KAAK;QACtB,WAAW,EAAE,EAAE;QACf,eAAe,EAAE,EAAE;QACnB,cAAc,EAAE,EAAE;QAClB,gBAAgB,EAAE,EAAE;QACpB,cAAc;QACd,WAAW,EAAE,YAAY;QACzB,QAAQ;QACR,gBAAgB,EAAE,IAAI,CAAC,GAAG,EAAE;QAC5B,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;QACxB,UAAU,EAAE,aAAa,CAAC,UAAU,IAAI,cAAc;QACtD,iBAAiB,EAAE,aAAa,CAAC,iBAAiB,IAAI,aAAa;QACnE,sBAAsB,EAAE,aAAa,CAAC,cAAc;QACpD,QAAQ,EAAE,aAAa,CAAC,QAAQ;QAChC,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE;QAC1B,gBAAgB,EAAE,CAAC;QACnB,iBAAiB,EAAE,MAAM;QACzB,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE;QAC3B,mBAAmB,EAAE,IAAI;KAC5B,CAAC;AACN,CAAC;AAED,wGAAwG;AAExG,SAAS,mBAAmB,CACxB,SAAiB,EACjB,OAAY,EACZ,IAAsB,EACtB,cAA8B;IAE9B,wBAAe,CAAC,KAAK,CAAC,4BAA4B,EAAE;QAChD,SAAS;QACT,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;KAC1C,CAAC,CAAC;IAEH,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QAC1C,wBAAe,CAAC,KAAK,CAAC,0CAA0C,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACjF,OAAO;IACX,CAAC;IAED,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;IACvE,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;IAEhE,wBAAe,CAAC,KAAK,CAAC,aAAa,EAAE;QACjC,SAAS;QACT,WAAW,EAAE,CAAC,CAAC,KAAK;QACpB,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;KACvC,CAAC,CAAC;IAEH,gEAAgE;IAChE,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/C,yEAAyE;QACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC;QACrF,MAAM,OAAO,GAAG,mBAAmB,EAAE,OAAO,CAAC;QAE7C,wBAAe,CAAC,KAAK,CAAC,wBAAwB,EAAE;YAC5C,SAAS;YACT,WAAW,EAAE,CAAC,CAAC,KAAK;YACpB,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;YACnC,OAAO,EAAE,CAAC,CAAC,OAAO;YAClB,UAAU,EAAE,OAAO,EAAE,UAAU;SAClC,CAAC,CAAC;QAEH,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB;YACxD,wBAAe,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACtD,SAAS;gBACT,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;gBAC7B,QAAQ,EAAE,KAAK,CAAC,QAAQ;aAC3B,CAAC,CAAC;YAEH,IAAI,CAAC;gBACD,8DAA8D;gBAC9D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACxB,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,KAAK,CAAC,IAAI;oBACjB,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,yCAAyC;iBACrE,CAAC,CAAC,CAAC;gBACJ,wBAAe,CAAC,IAAI,CAAC,sCAAsC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YAChF,CAAC;YAAC,OAAO,SAAS,EAAE,CAAC;gBACjB,wBAAe,CAAC,KAAK,CAAC,kCAAkC,EAAE,SAAkB,EAAE,SAAS,CAAC,CAAC;gBACzF,wBAAe,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,CAAC;YACxG,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,wBAAe,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACtC,SAAS;gBACT,OAAO,EAAE,CAAC,CAAC,OAAO;gBAClB,UAAU,EAAE,OAAO,EAAE,UAAU;aAClC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED,IAAI,IAAI,EAAE,CAAC;QACP,wBAAe,CAAC,KAAK,CAAC,wBAAwB,EAAE;YAC5C,SAAS;YACT,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;SAClC,CAAC,CAAC;QACH,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3C,cAAc,CAAC,gBAAgB,GAAG,CAAC,CAAC;QACpC,cAAc,CAAC,iBAAiB,GAAG,MAAM,CAAC;IAC9C,CAAC;AACL,CAAC;AAED,KAAK,UAAU,iBAAiB,CAC5B,SAAiB,EACjB,IAAsB,EACtB,cAA8B;IAE9B,IAAI,CAAC;QACD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,4BAA4B,CAAC,SAAS,EAAE,cAAqB,CAAC,CAAC;QACpH,IAAI,YAAY,EAAE,CAAC;YACf,cAAc,CAAC,kBAAkB,GAAG,YAAmB,CAAC;QAC5D,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,6CAA6C;QAC7C,wBAAe,CAAC,IAAI,CAAC,4BAA4B,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;IAClF,CAAC;AACL,CAAC"}