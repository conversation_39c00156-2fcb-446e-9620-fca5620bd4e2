import type { WebSocket } from 'ws';
import type { HeartbeatStatus, HeartbeatStatistics } from '../types/websocket';
/**
 * WebSocket Heartbeat Manager
 * Provides ping-pong functionality to detect stale connections
 */
export declare class HeartbeatManager {
    private connections;
    private defaultInterval;
    private defaultTimeout;
    constructor();
    /**
     * Start heartbeat monitoring for a WebSocket connection
     */
    startHeartbeat(sessionId: string, ws: WebSocket, interval?: number | null, timeout?: number | null, onTimeout?: ((sessionId: string, ws: WebSocket) => void) | null): void;
    /**
     * Send a ping to the WebSocket connection
     */
    private sendPing;
    /**
     * Stop heartbeat monitoring for a session
     */
    stopHeartbeat(sessionId: string): void;
    /**
     * Get heartbeat status for a session
     */
    getHeartbeatStatus(sessionId: string): HeartbeatStatus | null;
    /**
     * Get all active heartbeat sessions
     */
    getActiveSessions(): string[];
    /**
     * Clean up all heartbeats (useful for shutdown)
     */
    stopAllHeartbeats(): void;
    /**
     * Get heartbeat statistics
     */
    getStatistics(): HeartbeatStatistics;
}
export declare const globalHeartbeatManager: HeartbeatManager;
//# sourceMappingURL=heartbeat-manager.d.ts.map