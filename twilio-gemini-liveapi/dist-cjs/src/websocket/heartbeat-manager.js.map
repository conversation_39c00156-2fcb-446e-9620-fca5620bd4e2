{"version": 3, "file": "heartbeat-manager.js", "sourceRoot": "", "sources": ["../../../src/websocket/heartbeat-manager.ts"], "names": [], "mappings": ";;;AAAA,4CAA4D;AAI5D;;;GAGG;AACH,MAAa,gBAAgB;IACjB,WAAW,CAA6B;IACxC,eAAe,CAAS;IACxB,cAAc,CAAS;IAE/B;QACI,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,8BAA8B;QAC5D,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC,aAAa;QAC3C,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAE,+BAA+B;IACjE,CAAC;IAED;;OAEG;IACH,cAAc,CACV,SAAiB,EACjB,EAAa,EACb,WAA0B,IAAI,EAC9B,UAAyB,IAAI,EAC7B,YAAiE,IAAI;QAErE,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,CAAC;YACpB,wBAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;YACzE,OAAO;QACX,CAAC;QAED,MAAM,iBAAiB,GAAG,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC;QAC3D,MAAM,gBAAgB,GAAG,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC;QAExD,qCAAqC;QACrC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAE9B,MAAM,aAAa,GAAkB;YACjC,EAAE;YACF,QAAQ,EAAE,iBAAiB;YAC3B,OAAO,EAAE,gBAAgB;YACzB,SAAS;YACT,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;YACpB,WAAW,EAAE,CAAC;SACjB,CAAC;QAEF,0CAA0C;QAC1C,MAAM,WAAW,GAAG,GAAG,EAAE;YACrB,wBAAM,CAAC,KAAK,CAAC,OAAO,SAAS,iBAAiB,CAAC,CAAC;YAChD,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACpC,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC;YAE9B,wCAAwC;YACxC,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;gBAC1B,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBACtC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC;YACnC,CAAC;QACL,CAAC,CAAC;QAEF,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC3B,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,oBAAoB;QAE7D,sBAAsB;QACtB,aAAa,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC7B,CAAC,EAAE,iBAAiB,CAAC,CAAC;QAEtB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAC/C,wBAAM,CAAC,IAAI,CAAC,OAAO,SAAS,kCAAkC,iBAAiB,gBAAgB,gBAAgB,KAAK,CAAC,CAAC;IAC1H,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,SAAiB;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACtD,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,wBAAM,CAAC,IAAI,CAAC,OAAO,SAAS,6CAA6C,CAAC,CAAC;YAC3E,OAAO;QACX,CAAC;QAED,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,aAAa,CAAC;QAEjD,IAAI,EAAE,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB;YACxC,wBAAM,CAAC,IAAI,CAAC,OAAO,SAAS,0CAA0C,CAAC,CAAC;YACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,wBAAM,CAAC,KAAK,CAAC,OAAO,SAAS,gBAAgB,CAAC,CAAC;YAC/C,EAAE,CAAC,IAAI,EAAE,CAAC;YACV,aAAa,CAAC,WAAW,EAAE,CAAC;YAE5B,gCAAgC;YAChC,aAAa,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBACtC,wBAAM,CAAC,IAAI,CAAC,OAAO,SAAS,iDAAiD,OAAO,IAAI,CAAC,CAAC;gBAE1F,IAAI,aAAa,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;oBACjC,wBAAM,CAAC,KAAK,CAAC,MAAM,SAAS,8BAA8B,aAAa,CAAC,WAAW,gBAAgB,CAAC,CAAC;oBACrG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;oBAE9B,IAAI,SAAS,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE,CAAC;wBAC/C,IAAI,CAAC;4BACD,SAAS,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;wBAC7B,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACb,wBAAM,CAAC,KAAK,CAAC,MAAM,SAAS,wCAAwC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBACrI,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC,EAAE,OAAO,CAAC,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wBAAM,CAAC,KAAK,CAAC,MAAM,SAAS,uBAAuB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChH,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,SAAiB;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACtD,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,OAAO,CAAC,mCAAmC;QAC/C,CAAC;QAED,iBAAiB;QACjB,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;YAC3B,aAAa,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC;QAED,gBAAgB;QAChB,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;YAC1B,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC;QAED,gDAAgD;QAChD,IAAI,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,EAAE,EAAE,CAAC;YAChD,aAAa,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACnC,wBAAM,CAAC,IAAI,CAAC,OAAO,SAAS,oCAAoC,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,SAAiB;QAChC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACtD,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO;YACH,SAAS;YACT,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,WAAW,EAAE,aAAa,CAAC,WAAW;YACtC,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,OAAO,EAAE,aAAa,CAAC,OAAO;YAC9B,QAAQ,EAAE,aAAa,CAAC,UAAU,KAAK,IAAI;SAC9C,CAAC;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,iBAAiB;QACb,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1C,wBAAM,CAAC,IAAI,CAAC,eAAe,QAAQ,CAAC,MAAM,oBAAoB,CAAC,CAAC;QAEhE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACzB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,aAAa;QACT,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;QACvD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,OAAO;YACH,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,MAAM;YACjE,iBAAiB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,MAAM;YACjE,eAAe,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAClC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACrF,CAAC;IACN,CAAC;CACJ;AApMD,4CAoMC;AAED,oCAAoC;AACvB,QAAA,sBAAsB,GAAG,IAAI,gBAAgB,EAAE,CAAC"}