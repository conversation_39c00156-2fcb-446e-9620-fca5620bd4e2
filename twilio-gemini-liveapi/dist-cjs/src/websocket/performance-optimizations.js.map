{"version": 3, "file": "performance-optimizations.js", "sourceRoot": "", "sources": ["../../../src/websocket/performance-optimizations.ts"], "names": [], "mappings": ";;;AAqJA,4EAuCC;AAKD,wDAkBC;AAnND,qEAAqE;AACrE,4CAAkD;AAoBlD;;GAEG;AACH,MAAa,uBAAuB;IACxB,OAAO,GAAuC,IAAI,GAAG,EAAE,CAAC;IACxD,MAAM,CAAyB;IAEvC,YAAY,SAAiC;QACzC,eAAe,EAAE,IAAI;QACrB,sBAAsB,EAAE,KAAK;QAC7B,oBAAoB,EAAE,KAAK;QAC3B,sBAAsB,EAAE,IAAI;QAC5B,SAAS,EAAE,KAAK,CAAC,mCAAmC;KACvD;QACG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,SAAiB;QAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE;YACxB,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE;YAC3B,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;YACf,oBAAoB,EAAE,CAAC;YACvB,YAAY,EAAE,CAAC;YACf,SAAS,EAAE,CAAC;SACf,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,SAAiB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,SAAiB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,SAAiB;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,SAAiB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAClC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC;YAEnE,0BAA0B;YAC1B,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAE/C,OAAO,OAAO,CAAC;QACnB,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,SAAiB;QACrB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,SAAiB,EAAE,OAA8B;QAC3E,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC;QAChE,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAC/D,MAAM,UAAU,GAAG,OAAO,CAAC,oBAAoB,GAAG,OAAO,CAAC,YAAY,CAAC;QACvE,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAEtE,wBAAe,CAAC,IAAI,CAAC,mCAAmC,SAAS,GAAG,EAAE;YAClE,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,QAAQ;YACR,UAAU;YACV,UAAU;YACV,SAAS;SACZ,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,OAAO,CAAC,SAAS,GAAG,KAAK,EAAE,CAAC;YAC5B,wBAAe,CAAC,IAAI,CAAC,kCAAkC,OAAO,CAAC,SAAS,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACjG,CAAC;QACD,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YAClB,wBAAe,CAAC,IAAI,CAAC,wBAAwB,QAAQ,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAC9E,CAAC;QACD,IAAI,UAAU,GAAG,IAAI,EAAE,CAAC;YACpB,wBAAe,CAAC,IAAI,CAAC,wBAAwB,UAAU,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAChF,CAAC;QACD,IAAI,UAAU,GAAG,IAAI,EAAE,CAAC;YACpB,wBAAe,CAAC,IAAI,CAAC,iCAAiC,UAAU,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACzF,CAAC;IACL,CAAC;CACJ;AAxHD,0DAwHC;AAED;;GAEG;AACH,SAAgB,gCAAgC,CAC5C,eAAyB,EACzB,SAAkC;IAElC,OAAO,KAAK,WAAU,UAAe,EAAE,IAAS;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,IAAI,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAEpF,6BAA6B;QAC7B,SAAS,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAEnC,4BAA4B;QAC5B,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAC9B,wBAAe,CAAC,KAAK,CAAC,iCAAiC,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YACzG,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBAC1D,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBAClC,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,4CAA4C;oBACnD,OAAO,EAAE,IAAI;iBAChB,CAAC,CAAC,CAAC;gBACJ,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC9B,CAAC;QACL,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;QAElC,IAAI,CAAC;YACD,2CAA2C;YAC3C,MAAM,eAAe,CAAC,UAAU,EAAE;gBAC9B,GAAG,IAAI;gBACP,SAAS;gBACT,SAAS;aACZ,CAAC,CAAC;YAEH,8BAA8B;YAC9B,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,wBAAe,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACvH,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC,CAAC;AACN,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,sBAAsB,CACxC,KAA8B,EAC9B,YAAoB,IAAI;IAExB,MAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;QACpD,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC;QACD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YACtC,cAAc;SACjB,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,wBAAe,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnH,MAAM,KAAK,CAAC;IAChB,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAa,gBAAgB;IACjB,WAAW,GAAqB,IAAI,GAAG,EAAE,CAAC;IAC1C,YAAY,GAAW,KAAK,CAAC,CAAC,aAAa;IAEnD;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,SAAiB,EACjB,YAAgD,EAChD,WAAoB,IAAI;QAExB,MAAM,QAAQ,GAAG,UAAU,SAAS,EAAE,CAAC;QAEvC,IAAI,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChE,wBAAe,CAAC,KAAK,CAAC,2BAA2B,SAAS,EAAE,CAAC,CAAC;gBAC9D,OAAO,MAAM,CAAC,MAAM,CAAC;YACzB,CAAC;QACL,CAAC;QAED,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,SAAS,CAAC,CAAC;YAE7C,IAAI,QAAQ,EAAE,CAAC;gBACX,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE;oBAC3B,MAAM;oBACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACxB,CAAC,CAAC;YACP,CAAC;YAED,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wBAAe,CAAC,KAAK,CAAC,6BAA6B,SAAS,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3H,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,iBAAiB;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YACpD,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC9C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU;QACN,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;CACJ;AAzDD,4CAyDC;AAED;;GAEG;AACH,MAAa,wBAAwB;IACzB,cAAc,GAAqB,IAAI,GAAG,EAAE,CAAC;IAErD;;OAEG;IACH,eAAe,CAAC,SAAiB,EAAE,EAAO;QACtC,MAAM,OAAO,GAAG;YACZ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,YAAY,EAAE,CAAC;YACf,UAAU,EAAE,CAAC;YACb,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;YACxB,OAAO,EAAE,MAAM;SAClB,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE5C,2BAA2B;QAC3B,IAAI,EAAE,EAAE,CAAC;YACL,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;gBAClB,OAAO,CAAC,YAAY,EAAE,CAAC;gBACvB,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACtC,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAChB,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,SAAiB;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,EAAE,CAAC;YAAA,OAAO;QAAA,CAAC;QAEvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC;QAC/C,MAAM,iBAAiB,GAAG,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC;QAErD,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,IAAI,iBAAiB,GAAG,KAAK,EAAE,CAAC;YACtD,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;QAC7B,CAAC;aAAM,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,IAAI,iBAAiB,GAAG,KAAK,EAAE,CAAC;YAC7D,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;QAC7B,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;QAC7B,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAC9B,OAAO,OAAO,CAAC,OAAO,CAAC;QAC3B,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,SAAiB;QAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;CACJ;AArED,4DAqEC"}