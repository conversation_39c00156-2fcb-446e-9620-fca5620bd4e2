{"version": 3, "file": "twilio-session-helpers.js", "sourceRoot": "", "sources": ["../../../src/websocket/twilio-session-helpers.ts"], "names": [], "mappings": ";;AA8BA,0DAOC;AAKD,sDA4GC;AAKD,gEA0CC;AAKD,kDAaC;AAKD,0DAgBC;AAKD,4DAUC;AAKD,oDAyBC;AAKD,8DAYC;AAKD,gEAiCC;AAKD,0DAcC;AAhWD,4CAAkD;AAwBlD;;GAEG;AACH,SAAgB,uBAAuB,CAAC,IAAmE;IACvG,MAAM,KAAK,GAAG,IAAI,CAAC,KAA4C,CAAC;IAChE,OAAO;QACH,SAAS,EAAE,KAAK,EAAE,SAA+B;QACjD,UAAU,EAAE,KAAK,EAAE,UAAgC;QACnD,aAAa,EAAE,KAAK,EAAE,OAA6B;KACtD,CAAC;AACN,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,qBAAqB,CACvC,OAAe,EACf,gBAA2C,EAC3C,IAAsB,EACtB,cAAuB;IAEvB,wBAAe,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC;IAEnG,IAAI,CAAC;QACD,IAAI,aAAa,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEpD,iDAAiD;QACjD,IAAI,aAAa,EAAE,CAAC;YAChB,wBAAe,CAAC,IAAI,CAAC,IAAI,OAAO,oCAAoC,EAAE;gBAClE,SAAS,EAAE,CAAC,CAAC,aAAa;gBAC1B,iBAAiB,EAAE,CAAC,CAAC,aAAa,CAAC,cAAc;gBACjD,iBAAiB,EAAE,aAAa,CAAC,cAAc,EAAE,MAAM,IAAI,CAAC;gBAC5D,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,KAAK,EAAE,aAAa,CAAC,KAAK;gBAC1B,KAAK,EAAE,aAAa,CAAC,KAAK;aAC7B,CAAC,CAAC;YAEH,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;gBAC/B,MAAM,OAAO,GAAG,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC/D,wBAAe,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAC3E,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,wBAAe,CAAC,IAAI,CAAC,uDAAuD,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC/F,CAAC;QAED,wCAAwC;QACxC,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAClD,wBAAe,CAAC,IAAI,CAAC,8DAA8D,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YAElG,wCAAwC;YACxC,IAAI,CAAC;gBACD,wBAAe,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC3E,MAAM,aAAa,GAAG,cAAc;oBAChC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,wBAAwB,EAAE;oBAC/C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,wBAAwB,EAAE,CAAC;gBAEpD,wBAAe,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC,CAAC;gBAEjF,IAAI,aAAa,EAAE,CAAC;oBAChB,wBAAe,CAAC,KAAK,CAAC,2CAA2C,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC,CAAC;oBACzG,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;oBAExF,IAAI,aAAa,EAAE,CAAC;wBAChB,wBAAe,CAAC,IAAI,CAAC,+BAA+B,EAAE;4BAClD,OAAO;4BACP,QAAQ,EAAE,aAAa;4BACvB,iBAAiB,EAAE,CAAC,CAAC,aAAa,CAAC,cAAc;4BACjD,iBAAiB,EAAE,aAAa,CAAC,cAAc,EAAE,MAAM,IAAI,CAAC;4BAC5D,UAAU,EAAE,aAAa,CAAC,UAAU;yBACvC,CAAC,CAAC;wBAEH,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;4BAC/B,MAAM,OAAO,GAAG,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;4BAC/D,wBAAe,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;wBACpF,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,wBAAe,CAAC,KAAK,CAAC,iDAAiD,aAAa,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;oBACzG,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,wBAAe,CAAC,KAAK,CAAC,iDAAiD,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC1F,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,wBAAe,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAC3H,wBAAe,CAAC,KAAK,CAAC,wBAAwB,EAAE;oBAC5C,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC/D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;iBAC9F,EAAE,OAAO,CAAC,CAAC;YAChB,CAAC;QACL,CAAC;QAED,mBAAmB;QACnB,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC,aAAa,IAAI,aAAa,CAAC,cAAc,IAAI,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEpH,wBAAe,CAAC,KAAK,CAAC,iCAAiC,EAAE;YACrD,SAAS,EAAE,CAAC,CAAC,aAAa;YAC1B,iBAAiB,EAAE,CAAC,CAAC,aAAa,EAAE,cAAc;YAClD,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,IAAI,CAAC;YAC7D,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,aAAa,EAAE,QAAQ;YACjC,YAAY,EAAE,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;SAClD,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,wBAAe,CAAC,KAAK,CAAC,6EAA6E,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;YAClH,wBAAe,CAAC,KAAK,CAAC,iDAAiD,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO;YACH,MAAM,EAAE,aAAa;YACrB,OAAO,EAAE,OAAO;SACnB,CAAC;IACN,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,wBAAe,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC1H,wBAAe,CAAC,KAAK,CAAC,sBAAsB,EAAE;YAC1C,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;SAC9F,EAAE,OAAO,CAAC,CAAC;QACZ,OAAO;YACH,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,KAAK;SACjB,CAAC;IACN,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAgB,0BAA0B,CACtC,OAAe,EACf,EAAa,EACb,UAA4B,EAC5B,aAAkC,EAClC,cAAuB,EACvB,QAAgB;IAEhB,OAAO;QACH,EAAE,EAAE,EAAE,EAAE,uCAAuC;QAC/C,QAAQ,EAAE,EAAE,EAAE,4DAA4D;QAC1E,OAAO;QACP,SAAS,EAAE,OAAO;QAClB,SAAS,EAAE,UAAU,CAAC,SAAS,EAAE,qCAAqC;QACtE,cAAc,EAAE,CAAC,EAAE,sDAAsD;QACzE,eAAe,EAAE,KAAK;QACtB,gBAAgB,EAAE,KAAK;QACvB,eAAe,EAAE,KAAK;QACtB,WAAW,EAAE,EAAE;QACf,eAAe,EAAE,EAAE;QACnB,cAAc,EAAE,EAAE;QAClB,gBAAgB,EAAE,EAAE;QACpB,cAAc;QACd,WAAW,EAAE,aAAa;QAC1B,QAAQ;QACR,gBAAgB,EAAE,IAAI,CAAC,GAAG,EAAE;QAC5B,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;QACxB,UAAU,EAAE,aAAa,CAAC,UAAU,IAAI,SAAS;QACjD,iBAAiB,EAAE,aAAa,CAAC,iBAAiB,IAAI,aAAa;QACnE,sBAAsB,EAAE,aAAa,CAAC,cAAc;QACpD,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,SAAS;QAC7C,YAAY,EAAE,IAAI;QAClB,gDAAgD;QAChD,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,0BAA0B;QACtD,gBAAgB,EAAE,CAAC,EAAE,6BAA6B;QAClD,iBAAiB,EAAE,MAAM,EAAE,2BAA2B;QACtD,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,8BAA8B;QAC3D,mBAAmB,EAAE,IAAI,EAAE,8BAA8B;QACzD,8BAA8B;QAC9B,sBAAsB,EAAE,IAAI,EAAE,2CAA2C;QACzE,aAAa,EAAE,CAAC,CAAC,oCAAoC;KACxD,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAC/B,OAAe,EACf,UAA4B,EAC5B,aAAkC;IAElC,wBAAe,CAAC,KAAK,CAAC,oBAAoB,EAAE;QACxC,OAAO;QACP,SAAS,EAAE,UAAU,CAAC,SAAS;QAC/B,UAAU,EAAE,UAAU,CAAC,UAAU;QACjC,aAAa,EAAE,UAAU,CAAC,aAAa;QACvC,WAAW,EAAE,CAAC,CAAC,aAAa;QAC5B,eAAe,EAAE,CAAC,CAAC,aAAa,EAAE,cAAc;KACnD,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CACnC,OAAe,EACf,aAAkC,EAClC,QAAgB;IAEhB,wBAAe,CAAC,KAAK,CAAC,wBAAwB,EAAE;QAC5C,OAAO;QACP,QAAQ;QACR,eAAe,EAAE,CAAC,CAAC,aAAa,CAAC,cAAc;QAC/C,kBAAkB,EAAE,aAAa,CAAC,cAAc,EAAE,MAAM,IAAI,CAAC;QAC7D,QAAQ,EAAE,aAAa,CAAC,QAAQ;QAChC,UAAU,EAAE,aAAa,CAAC,UAAU;QACpC,KAAK,EAAE,aAAa,CAAC,KAAK;QAC1B,KAAK,EAAE,aAAa,CAAC,KAAK;QAC1B,cAAc,EAAE,aAAa,CAAC,cAAc;KAC/C,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CACpC,OAAe,EACf,gBAAqB,EACrB,cAA8B,EAC9B,aAAkB;IAElB,IAAI,gBAAgB,EAAE,CAAC;QACnB,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,aAAa,EAAE,CAAC,CAAC;QAC/E,wBAAe,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACtE,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAChC,OAAe,EACf,EAAO,EACP,sBAA2B,EAC3B,iBAA8C,EAC9C,MAAW;IAEX,oFAAoF;IACpF,sBAAsB,CAAC,cAAc,CACjC,OAAO,EACP,EAAE,EACF,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAClC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EACjC,CAAC,SAAiB,EAAE,EAAO,EAAE,EAAE;QAC3B,wBAAe,CAAC,IAAI,CAAC,8DAA8D,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAClG,gEAAgE;QAChE,kFAAkF;QAClF,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,cAAc,EAAE,CAAC;YACjB,cAAc,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACvC,cAAc,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACjD,wBAAe,CAAC,IAAI,CAAC,sDAAsD,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC9F,CAAC;IACL,CAAC,CACJ,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CACrC,EAAO,EACP,OAAe,EACf,QAAgB,EAChB,QAAgB;IAEhB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;QACnB,IAAI,EAAE,iBAAiB;QACvB,OAAO;QACP,QAAQ;QACR,QAAQ;KACX,CAAC,CAAC,CAAC;AACR,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,0BAA0B,CAC5C,OAAe,EACf,QAAgB,EAChB,EAAO,EACP,IAAsB,EACtB,UAA6E;IAE7E,wBAAe,CAAC,KAAK,CAAC,uCAAuC,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAE3F,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;QACnB,IAAI,EAAE,eAAe;QACrB,KAAK,EAAE,0DAA0D;QACjE,QAAQ,EAAE,IAAI;KACjB,CAAC,CAAC,CAAC;IAEJ,sDAAsD;IACtD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CACtC,OAAO,EACP,4FAA4F,CAC/F,CAAC;QACN,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,wBAAe,CAAC,KAAK,CACjB,uCAAuC,EACvC,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CACtD,CAAC;QACN,CAAC;IACL,CAAC;IAED,gCAAgC;IAChC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,uBAAuB,CAAC,CAAC;IACnD,EAAE,CAAC,KAAK,EAAE,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CACnC,KAAc,EACd,QAAgB,EAChB,EAAO;IAEP,wBAAe,CAAC,KAAK,CACjB,yBAAyB,QAAQ,UAAU,EAC3C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC5D,CAAC;IAEF,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;QACnB,IAAI,EAAE,eAAe;QACrB,KAAK,EAAE,yBAA0B,KAAe,CAAC,OAAO,EAAE;KAC7D,CAAC,CAAC,CAAC;AACR,CAAC"}