"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractTwilioStreamInfo = extractTwilioStreamInfo;
exports.getValidSessionConfig = getValidSessionConfig;
exports.createTwilioConnectionData = createTwilioConnectionData;
exports.logTwilioStreamInfo = logTwilioStreamInfo;
exports.logSessionConfiguration = logSessionConfiguration;
exports.startLifecycleManagement = startLifecycleManagement;
exports.startTwilioHeartbeat = startTwilioHeartbeat;
exports.sendSessionStartedMessage = sendSessionStartedMessage;
exports.handleGeminiSessionFailure = handleGeminiSessionFailure;
exports.handleSessionStartError = handleSessionStartError;
const logger_1 = require("../utils/logger");
/**
 * Extracts Twilio stream information from start message
 */
function extractTwilioStreamInfo(data) {
    const start = data.start;
    return {
        streamSid: start?.streamSid,
        accountSid: start?.accountSid,
        twilioCallSid: start?.callSid
    };
}
/**
 * Validates and gets session configuration with fallback
 */
async function getValidSessionConfig(callSid, getSessionConfig, deps, isIncomingCall) {
    logger_1.websocketLogger.debug('Starting session config validation', { callSid, incoming: isIncomingCall });
    try {
        let sessionConfig = await getSessionConfig(callSid);
        // DETAILED LOGGING: Log initial config retrieval
        if (sessionConfig) {
            logger_1.websocketLogger.info(`[${callSid}] Initial session config retrieved`, {
                hasConfig: !!sessionConfig,
                hasAiInstructions: !!sessionConfig.aiInstructions,
                instructionLength: sessionConfig.aiInstructions?.length || 0,
                scriptId: sessionConfig.scriptId,
                scriptType: sessionConfig.scriptType,
                voice: sessionConfig.voice,
                model: sessionConfig.model
            });
            if (sessionConfig.aiInstructions) {
                const preview = sessionConfig.aiInstructions.substring(0, 200);
                logger_1.websocketLogger.debug('AI Instructions preview', { callSid, preview });
            }
        }
        else {
            logger_1.websocketLogger.warn('No initial session config found from getSessionConfig', { callSid });
        }
        // Ensure we have a valid session config
        if (!sessionConfig || !sessionConfig.aiInstructions) {
            logger_1.websocketLogger.warn('No valid session config found, attempting fallback mechanism', { callSid });
            // Try to get current script as fallback
            try {
                logger_1.websocketLogger.debug('Attempting fallback script retrieval', { callSid });
                const currentScript = isIncomingCall
                    ? deps.scriptManager.getCurrentIncomingScript()
                    : deps.scriptManager.getCurrentOutboundScript();
                logger_1.websocketLogger.debug('Current script from manager', { callSid, currentScript });
                if (currentScript) {
                    logger_1.websocketLogger.debug('Loading script config for fallback script', { callSid, scriptId: currentScript });
                    sessionConfig = await deps.scriptManager.getScriptConfig(currentScript, isIncomingCall);
                    if (sessionConfig) {
                        logger_1.websocketLogger.info('Fallback script config loaded', {
                            callSid,
                            scriptId: currentScript,
                            hasAiInstructions: !!sessionConfig.aiInstructions,
                            instructionLength: sessionConfig.aiInstructions?.length || 0,
                            scriptType: sessionConfig.scriptType
                        });
                        if (sessionConfig.aiInstructions) {
                            const preview = sessionConfig.aiInstructions.substring(0, 200);
                            logger_1.websocketLogger.debug('Fallback AI Instructions preview', { callSid, preview });
                        }
                    }
                    else {
                        logger_1.websocketLogger.error(`Fallback script config is null for script ID: ${currentScript}`, {}, callSid);
                    }
                }
                else {
                    logger_1.websocketLogger.error('No current script available from script manager', {}, callSid);
                }
            }
            catch (error) {
                logger_1.websocketLogger.error('Error getting fallback script', error instanceof Error ? error : new Error(String(error)), callSid);
                logger_1.websocketLogger.error('Fallback error details', {
                    message: error instanceof Error ? error.message : String(error),
                    stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined
                }, callSid);
            }
        }
        // Final validation
        const isValid = !!(sessionConfig && sessionConfig.aiInstructions && sessionConfig.aiInstructions.trim().length > 0);
        logger_1.websocketLogger.debug('Final session config validation', {
            hasConfig: !!sessionConfig,
            hasAiInstructions: !!sessionConfig?.aiInstructions,
            instructionLength: sessionConfig?.aiInstructions?.length || 0,
            isValid: isValid,
            scriptId: sessionConfig?.scriptId,
            configSource: sessionConfig ? 'loaded' : 'none'
        }, callSid);
        if (!isValid) {
            logger_1.websocketLogger.error('CRITICAL: Session config validation failed - no valid AI instructions found', {}, callSid);
            logger_1.websocketLogger.error('This will cause Gemini session creation to fail', {}, callSid);
        }
        return {
            config: sessionConfig,
            isValid: isValid
        };
    }
    catch (error) {
        logger_1.websocketLogger.error('Error getting session config', error instanceof Error ? error : new Error(String(error)), callSid);
        logger_1.websocketLogger.error('Config error details', {
            message: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined
        }, callSid);
        return {
            config: null,
            isValid: false
        };
    }
}
/**
 * Creates enhanced connection data for Twilio sessions
 */
function createTwilioConnectionData(callSid, ws, streamInfo, sessionConfig, isIncomingCall, flowType) {
    return {
        ws: ws, // Standardized WebSocket property name
        twilioWs: ws, // Also store as twilioWs for audio forwarding compatibility
        callSid,
        sessionId: callSid,
        streamSid: streamInfo.streamSid, // Add streamSid for audio forwarding
        sequenceNumber: 0, // Initialize sequence number for Twilio audio packets
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'twilio_call',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId || 'default',
        isTwilioCall: true,
        // Add missing properties to match testing flows
        lastAIResponse: Date.now(), // Track AI responsiveness
        responseTimeouts: 0, // Count consecutive timeouts
        connectionQuality: 'good', // Track connection quality
        lastContextSave: Date.now(), // For periodic context saving
        contextSaveInterval: null, // For periodic context saving
        // Audio forwarding properties
        audioForwardingEnabled: true, // Enable audio forwarding for Twilio calls
        lastAudioSent: 0 // Track last audio packet sent time
    };
}
/**
 * Logs Twilio stream information
 */
function logTwilioStreamInfo(callSid, streamInfo, sessionConfig) {
    logger_1.websocketLogger.debug('Twilio stream info', {
        callSid,
        streamSid: streamInfo.streamSid,
        accountSid: streamInfo.accountSid,
        twilioCallSid: streamInfo.twilioCallSid,
        configFound: !!sessionConfig,
        hasInstructions: !!sessionConfig?.aiInstructions
    });
}
/**
 * Logs session configuration details
 */
function logSessionConfiguration(callSid, sessionConfig, flowType) {
    logger_1.websocketLogger.debug('Session config details', {
        callSid,
        flowType,
        hasInstructions: !!sessionConfig.aiInstructions,
        instructionsLength: sessionConfig.aiInstructions?.length || 0,
        scriptId: sessionConfig.scriptId,
        scriptType: sessionConfig.scriptType,
        voice: sessionConfig.voice,
        model: sessionConfig.model,
        isIncomingCall: sessionConfig.isIncomingCall
    });
}
/**
 * Starts lifecycle management for a session
 */
function startLifecycleManagement(callSid, lifecycleManager, connectionData, sessionConfig) {
    if (lifecycleManager) {
        lifecycleManager.initializeSession(callSid, { connectionData, sessionConfig });
        logger_1.websocketLogger.info('Lifecycle management started', { callSid });
    }
}
/**
 * Starts WebSocket heartbeat monitoring for Twilio
 */
function startTwilioHeartbeat(callSid, ws, globalHeartbeatManager, activeConnections, config) {
    // Note: Twilio WebSockets may not respond to standard pings, so use longer timeouts
    globalHeartbeatManager.startHeartbeat(callSid, ws, config.websocket.heartbeatInterval, config.websocket.heartbeatTimeout, (sessionId, ws) => {
        logger_1.websocketLogger.warn('Twilio WebSocket heartbeat timeout - connection may be stale', { callSid });
        // Don't immediately end session on heartbeat timeout for Twilio
        // Mark connection as potentially stale but let call status webhook handle cleanup
        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            connectionData.heartbeatTimeout = true;
            connectionData.lastHeartbeatTimeout = Date.now();
            logger_1.websocketLogger.info('Marked Twilio connection as having heartbeat timeout', { callSid });
        }
    });
}
/**
 * Sends session started message to WebSocket
 */
function sendSessionStartedMessage(ws, callSid, flowType, scriptId) {
    ws.send(JSON.stringify({
        type: 'session-started',
        callSid,
        flowType,
        scriptId
    }));
}
/**
 * Handles Gemini session creation failure
 */
async function handleGeminiSessionFailure(callSid, flowType, ws, deps, endSession) {
    logger_1.websocketLogger.error(`Failed to create Gemini session for ${flowType} call`, { callSid });
    ws.send(JSON.stringify({
        type: 'session-error',
        error: 'Failed to initialize AI session. Please try again later.',
        critical: true
    }));
    // For Twilio calls, we should end the call gracefully
    if (deps.twilioHelper) {
        try {
            await deps.twilioHelper.endCallWithMessage(callSid, 'We apologize, but we are unable to process your call at this time. Please try again later.');
        }
        catch (err) {
            logger_1.websocketLogger.error('Failed to end call with error message', err instanceof Error ? err : new Error(String(err)));
        }
    }
    // Clean up and close connection
    endSession(callSid, deps, 'gemini_session_failed');
    ws.close();
}
/**
 * Handles session start errors
 */
function handleSessionStartError(error, flowType, ws) {
    logger_1.websocketLogger.error(`Error starting Twilio ${flowType} session`, error instanceof Error ? error : new Error(String(error)));
    ws.send(JSON.stringify({
        type: 'session-error',
        error: `Session start failed: ${error.message}`
    }));
}
//# sourceMappingURL=twilio-session-helpers.js.map