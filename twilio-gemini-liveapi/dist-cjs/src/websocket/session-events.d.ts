import type { ConnectionData, WebSocketDependencies } from '../types/websocket';
export declare function handleEndSession(sessionId: string, deps: WebSocketDependencies, activeConnections: Map<string, ConnectionData>, lifecycleManager: any): Promise<void>;
export declare function handleRequestSummary(sessionId: string, _deps: WebSocketDependencies, activeConnections: Map<string, ConnectionData>, summaryManager: any, contextManager: any): Promise<void>;
//# sourceMappingURL=session-events.d.ts.map