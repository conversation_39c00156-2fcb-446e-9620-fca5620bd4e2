{"version": 3, "file": "session-utils.js", "sourceRoot": "", "sources": ["../../../src/websocket/session-utils.ts"], "names": [], "mappings": ";;AAKA,gCA8DC;AAED,8CAKC;AAED,4DAQC;AAED,0CAMC;AAED,4CAcC;AA5GD,4CAAkD;AAClD,2DAA6D;AAE7D,0DAAsD;AAE/C,KAAK,UAAU,UAAU,CAC5B,SAAiB,EACjB,IAA2B,EAC3B,SAAiB,gBAAgB;IAEjC,MAAM,EACF,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,cAAc,EACd,oBAAoB,EACpB,gBAAgB,EACnB,GAAG,IAAI,CAAC;IAET,wBAAe,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;IAE9D,IAAI,CAAC;QACD,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,wBAAe,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YACrE,OAAO;QACX,CAAC;QAED,4BAA4B;QAC5B,0CAAsB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAEhD,yBAAyB;QACzB,IAAI,oBAAoB,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAC5D,oBAAoB,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACvD,CAAC;QAED,gCAAgC;QAChC,IAAI,cAAc,IAAI,cAAc,CAAC,gBAAgB,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;YACvF,IAAI,CAAC;gBACD,MAAM,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;YACnF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,wBAAe,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACjH,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,IAAI,cAAc,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;YACjD,IAAI,CAAC;gBACD,MAAM,cAAc,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,wBAAe,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzH,CAAC;QACL,CAAC;QAED,mBAAmB;QACnB,IAAI,cAAc,EAAE,CAAC;YAChB,cAAsB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC;QAED,iCAAiC;QACjC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEpC,wBAAe,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;IAE9E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,wBAAe,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrH,CAAC;AACL,CAAC;AAED,SAAgB,iBAAiB,CAC7B,SAAiB,EACjB,iBAA8C;IAE9C,OAAO,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC;AAED,SAAgB,wBAAwB,CACpC,SAAiB,EACjB,iBAA8C;IAE9C,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACxD,IAAI,cAAc,EAAE,CAAC;QACjB,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7C,CAAC;AACL,CAAC;AAED,SAAgB,eAAe,CAC3B,SAAiB,EACjB,iBAA8C;IAE9C,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACxD,OAAO,CAAC,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,eAAe,CAAC,CAAC;AAChE,CAAC;AAED,SAAgB,gBAAgB,CAC5B,SAAiB,EACjB,MAAc,EACd,eAAoB,EACpB,iBAA8C,EAC9C,QAAgB,IAAI;IAEpB,4BAAY,CAAC,UAAU,CAAC,GAAG,SAAS,WAAW,EAAE,KAAK,IAAI,EAAE;QACxD,IAAI,CAAC;YACD,MAAM,eAAe,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAC/E,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,wBAAe,CAAC,KAAK,CAAC,iCAAiC,EAAE,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClH,CAAC;IACL,CAAC,EAAE,KAAK,CAAC,CAAC;AACd,CAAC"}