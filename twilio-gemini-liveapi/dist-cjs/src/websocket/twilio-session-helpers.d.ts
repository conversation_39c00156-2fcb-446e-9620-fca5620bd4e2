import { ConnectionData } from '../types/global';
import { FlowDependencies } from '../types/websocket';
import type { WebSocket } from 'ws';
export interface TwilioStreamInfo {
    streamSid?: string;
    accountSid?: string;
    twilioCallSid?: string;
}
export interface TwilioSessionConfig {
    aiInstructions: string;
    voice: string;
    model: string;
    targetName?: string;
    targetPhoneNumber?: string;
    scriptId?: string;
    [key: string]: unknown;
}
export interface SessionConfigResult {
    config: TwilioSessionConfig | null;
    isValid: boolean;
}
/**
 * Extracts Twilio stream information from start message
 */
export declare function extractTwilioStreamInfo(data: Record<string, unknown> | {
    start?: Record<string, unknown>;
}): TwilioStreamInfo;
/**
 * Validates and gets session configuration with fallback
 */
export declare function getValidSessionConfig(callSid: string, getSessionConfig: (callSid?: string) => any, deps: FlowDependencies, isIncomingCall: boolean): Promise<SessionConfigResult>;
/**
 * Creates enhanced connection data for Twilio sessions
 */
export declare function createTwilioConnectionData(callSid: string, ws: WebSocket, streamInfo: TwilioStreamInfo, sessionConfig: TwilioSessionConfig, isIncomingCall: boolean, flowType: string): ConnectionData;
/**
 * Logs Twilio stream information
 */
export declare function logTwilioStreamInfo(callSid: string, streamInfo: TwilioStreamInfo, sessionConfig: TwilioSessionConfig): void;
/**
 * Logs session configuration details
 */
export declare function logSessionConfiguration(callSid: string, sessionConfig: TwilioSessionConfig, flowType: string): void;
/**
 * Starts lifecycle management for a session
 */
export declare function startLifecycleManagement(callSid: string, lifecycleManager: any, connectionData: ConnectionData, sessionConfig: any): void;
/**
 * Starts WebSocket heartbeat monitoring for Twilio
 */
export declare function startTwilioHeartbeat(callSid: string, ws: any, globalHeartbeatManager: any, activeConnections: Map<string, ConnectionData>, config: any): void;
/**
 * Sends session started message to WebSocket
 */
export declare function sendSessionStartedMessage(ws: any, callSid: string, flowType: string, scriptId: string): void;
/**
 * Handles Gemini session creation failure
 */
export declare function handleGeminiSessionFailure(callSid: string, flowType: string, ws: any, deps: FlowDependencies, endSession: (callSid: string, deps: FlowDependencies, reason: string) => void): Promise<void>;
/**
 * Handles session start errors
 */
export declare function handleSessionStartError(error: unknown, flowType: string, ws: any): void;
//# sourceMappingURL=twilio-session-helpers.d.ts.map