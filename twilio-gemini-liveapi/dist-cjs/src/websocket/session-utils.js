"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.endSession = endSession;
exports.getConnectionData = getConnectionData;
exports.updateConnectionActivity = updateConnectionActivity;
exports.isSessionActive = isSessionActive;
exports.scheduleRecovery = scheduleRecovery;
const logger_1 = require("../utils/logger");
const heartbeat_manager_1 = require("./heartbeat-manager");
const timer_manager_1 = require("../utils/timer-manager");
async function endSession(sessionId, deps, reason = 'user_requested') {
    const { activeConnections, sessionManager, contextManager, summaryManager, transcriptionManager, lifecycleManager } = deps;
    logger_1.websocketLogger.info(`Ending session`, { sessionId, reason });
    try {
        const connectionData = activeConnections.get(sessionId);
        if (!connectionData) {
            logger_1.websocketLogger.warn(`Session not found for cleanup`, { sessionId });
            return;
        }
        // Stop heartbeat monitoring
        heartbeat_manager_1.globalHeartbeatManager.stopHeartbeat(sessionId);
        // Clean up transcription
        if (transcriptionManager && connectionData.deepgramConnection) {
            transcriptionManager.closeTranscription(sessionId);
        }
        // Generate summary if requested
        if (summaryManager && connectionData.summaryRequested && !connectionData.summaryReceived) {
            try {
                await summaryManager.requestSummary(sessionId, connectionData, contextManager);
            }
            catch (error) {
                logger_1.websocketLogger.error(`Error generating summary`, error instanceof Error ? error : new Error(String(error)));
            }
        }
        // Clean up Gemini session
        if (sessionManager && connectionData.geminiSession) {
            try {
                await sessionManager.cleanupSession(sessionId);
            }
            catch (error) {
                logger_1.websocketLogger.error(`Error cleaning up Gemini session`, error instanceof Error ? error : new Error(String(error)));
            }
        }
        // Clean up context
        if (contextManager) {
            contextManager.clearContext(sessionId);
        }
        // Remove from active connections
        activeConnections.delete(sessionId);
        logger_1.websocketLogger.info(`Session ended successfully`, { sessionId, reason });
    }
    catch (error) {
        logger_1.websocketLogger.error(`Error during session cleanup`, error instanceof Error ? error : new Error(String(error)));
    }
}
function getConnectionData(sessionId, activeConnections) {
    return activeConnections.get(sessionId);
}
function updateConnectionActivity(sessionId, activeConnections) {
    const connectionData = activeConnections.get(sessionId);
    if (connectionData) {
        connectionData.lastActivity = Date.now();
    }
}
function isSessionActive(sessionId, activeConnections) {
    const connectionData = activeConnections.get(sessionId);
    return !!(connectionData && connectionData.isSessionActive);
}
function scheduleRecovery(sessionId, reason, recoveryManager, activeConnections, delay = 1000) {
    timer_manager_1.timerManager.setTimeout(`${sessionId}_recovery`, async () => {
        try {
            await recoveryManager.recoverSession(sessionId, reason, activeConnections);
        }
        catch (err) {
            logger_1.websocketLogger.error('Error during scheduled recovery', err instanceof Error ? err : new Error(String(err)));
        }
    }, delay);
}
//# sourceMappingURL=session-utils.js.map