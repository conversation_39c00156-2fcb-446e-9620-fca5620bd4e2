{"version": 3, "file": "message-schemas.js", "sourceRoot": "", "sources": ["../../../src/websocket/message-schemas.ts"], "names": [], "mappings": ";;;AA6LA,sDAWC;AAKD,oDAWC;AAKD,sDAWC;AAKD,gDAcC;AASD,sDAMC;AA1QD,6BAAwB;AAExB,2CAA2C;AAC3C,yBAAyB;AACzB,2CAA2C;AAE3C;;GAEG;AACU,QAAA,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,KAAK,EAAE,OAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IACzB,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;QACZ,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5B,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC;QAChD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC;QAC7C,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;QACtC,gBAAgB,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;KACjD,CAAC;IACF,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACnC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,KAAK,EAAE,OAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IACzB,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;QACZ,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,uBAAuB;QACnD,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC5B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAChC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAC/B,CAAC;IACF,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAChC,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACxC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,KAAK,EAAE,OAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAChC,IAAI,EAAE,OAAC,CAAC,MAAM,CAAC;QACX,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QACjC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KACjC,CAAC,CAAC,QAAQ,EAAE;CAChB,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,KAAK,EAAE,OAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAChC,IAAI,EAAE,OAAC,CAAC,MAAM,CAAC;QACX,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;KACnB,CAAC,CAAC,QAAQ,EAAE;CAChB,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,4BAA4B,GAAG,OAAC,CAAC,MAAM,CAAC;IACjD,KAAK,EAAE,OAAC,CAAC,OAAO,CAAC,WAAW,CAAC;IAC7B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC/B,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACjC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,OAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE;IAC7D,gCAAwB;IACxB,gCAAwB;IACxB,+BAAuB;IACvB,+BAAuB;IACvB,oCAA4B;CAC/B,CAAC,CAAC;AAEH,2CAA2C;AAC3C,gCAAgC;AAChC,2CAA2C;AAE3C;;GAEG;AACU,QAAA,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,EAAE,sBAAsB;IACxE,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACrC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAClC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IACrC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,EAAE,uBAAuB;IACrD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,yBAAyB;CAC7D,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,IAAI,EAAE,OAAC,CAAC,OAAO,CAAC,cAAc,CAAC;IAC/B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAC1B,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,4BAA4B,GAAG,OAAC,CAAC,MAAM,CAAC;IACjD,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,sBAAsB;CACtE,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,2BAA2B,GAAG,OAAC,CAAC,MAAM,CAAC;IAChD,IAAI,EAAE,OAAC,CAAC,OAAO,CAAC,WAAW,CAAC;CAC/B,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,8BAA8B,GAAG,OAAC,CAAC,MAAM,CAAC;IACnD,IAAI,EAAE,OAAC,CAAC,OAAO,CAAC,eAAe,CAAC;CACnC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,gCAAgC,GAAG,OAAC,CAAC,MAAM,CAAC;IACrD,IAAI,EAAE,OAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;CACrC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,+BAA+B,GAAG,OAAC,CAAC,MAAM,CAAC;IACpD,IAAI,EAAE,OAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC;IACjC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC/B,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,kBAAkB,GAAG,OAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE;IAC3D,+BAAuB;IACvB,+BAAuB;IACvB,8BAAsB;IACtB,oCAA4B;IAC5B,mCAA2B;IAC3B,sCAA8B;IAC9B,wCAAgC;IAChC,uCAA+B;CAClC,CAAC,CAAC;AAEH,2CAA2C;AAC3C,gCAAgC;AAChC,2CAA2C;AAE3C;;GAEG;AACH,wDAAmF;AAEtE,QAAA,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACjC,KAAK,EAAE,0BAAW;IAClB,KAAK,EAAE,0BAAW;IAClB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IAC5C,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IACnD,UAAU,EAAE,+BAAgB;IAC5B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3B,cAAc,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACtC,UAAU,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CACrC,CAAC,CAAC;AAEH,2CAA2C;AAC3C,qBAAqB;AACrB,2CAA2C;AAE3C;;GAEG;AACH,SAAgB,qBAAqB,CAAC,IAAa;IAK/C,MAAM,MAAM,GAAG,2BAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACnD,OAAO;QACH,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC9C,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK;KACnD,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAAC,IAAa;IAK9C,MAAM,MAAM,GAAG,0BAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAClD,OAAO;QACH,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC9C,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK;KACnD,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,IAAa;IAK/C,MAAM,MAAM,GAAG,2BAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACnD,OAAO;QACH,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC9C,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK;KACnD,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAC9B,MAAsB,EACtB,IAAa;IAMb,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACtC,OAAO;QACH,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC9C,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK;KACnD,CAAC;AACN,CAAC;AAED,2CAA2C;AAC3C,mBAAmB;AACnB,2CAA2C;AAE3C;;GAEG;AACH,SAAgB,qBAAqB,CAAC,KAAiB;IACnD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QACpC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;IACxD,CAAC,CAAC,CAAC;IACH,OAAO,sBAAsB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACrD,CAAC"}