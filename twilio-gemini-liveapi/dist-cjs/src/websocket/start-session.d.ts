import type { WebSocketConnection, FlowDependencies, ConnectionData, SessionData, LocalStartMessage } from '../types/websocket';
import type { WebSocket } from 'ws';
export declare function handleStartSession(sessionId: string, data: LocalStartMessage, deps: FlowDependencies, connection: WebSocketConnection, ws: WebSocket, flowType: string, isIncomingCall: boolean, getSessionConfig: (callSid?: string) => any, activeConnections: Map<string, ConnectionData>, healthMonitor: any, lifecycleManager: any, sessionManager: any): Promise<SessionData>;
//# sourceMappingURL=start-session.d.ts.map