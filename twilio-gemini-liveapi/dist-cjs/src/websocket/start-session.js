"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleStartSession = handleStartSession;
const logger_1 = require("../utils/logger");
async function handleStartSession(sessionId, data, deps, connection, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager) {
    try {
        let sessionConfig = await getSessionConfig(sessionId);
        logger_1.websocketLogger.info('START SESSION - Processing script selection', {
            sessionId,
            hasScriptId: !!data.scriptId,
            scriptId: data.scriptId,
            hasAiInstructions: !!data.aiInstructions,
            aiInstructionsLength: data.aiInstructions?.length || 0,
            isIncomingCall,
            flowType
        });
        // PRIORITY 1: Handle explicit script ID selection (from frontend campaign selection)
        if (data.scriptId) {
            logger_1.websocketLogger.info('Loading specific script ID', { sessionId, scriptId: data.scriptId });
            const scriptConfig = await deps.scriptManager.getScriptConfig(data.scriptId, isIncomingCall);
            if (scriptConfig && scriptConfig.aiInstructions) {
                logger_1.websocketLogger.info('Successfully loaded script', {
                    sessionId,
                    scriptId: data.scriptId,
                    instructionLength: scriptConfig.aiInstructions.length
                });
                sessionConfig = {
                    ...scriptConfig,
                    aiInstructions: scriptConfig.aiInstructions, // Don't add [TESTING MODE] prefix for selected scripts
                    isTestMode: true,
                    selectedScriptId: data.scriptId
                };
            }
            else {
                logger_1.websocketLogger.warn('Failed to load script, falling back to default config', {
                    sessionId,
                    scriptId: data.scriptId
                });
            }
        }
        // PRIORITY 2: Handle direct AI instructions (from manual input)
        if (data.aiInstructions && !data.scriptId) {
            logger_1.websocketLogger.info('Using direct AI instructions', {
                sessionId,
                instructionLength: data.aiInstructions.length
            });
            sessionConfig.aiInstructions = data.aiInstructions;
        }
        // PRIORITY 3: Apply voice and model overrides
        if (data.voice) {
            sessionConfig.voice = deps.voiceManager.getValidGeminiVoice(data.voice);
            logger_1.websocketLogger.info('Voice override applied', {
                sessionId,
                from: data.voice,
                to: sessionConfig.voice
            });
        }
        if (data.model) {
            sessionConfig.model = deps.modelManager.getValidGeminiModel(data.model);
            logger_1.websocketLogger.info('Model override applied', {
                sessionId,
                from: data.model,
                to: sessionConfig.model
            });
        }
        // Ensure instructions are long enough; otherwise use fallback
        if (sessionConfig.aiInstructions && sessionConfig.aiInstructions.trim().length < 100) {
            console.warn(`⚠️ [${sessionId}] AI instructions too short (${sessionConfig.aiInstructions.length} chars), using fallback instructions`);
            sessionConfig.aiInstructions = deps.scriptManager.getFallbackInstructions();
        }
        // CRITICAL VALIDATION: Ensure AI instructions are present before creating session
        if (!sessionConfig || !sessionConfig.aiInstructions || sessionConfig.aiInstructions.trim().length === 0) {
            const errorMessage = `No AI instructions available for ${flowType} session. Cannot create Gemini session without proper system instructions.`;
            logger_1.websocketLogger.error(`VALIDATION FAILED: ${errorMessage}`, {}, sessionId);
            logger_1.websocketLogger.error('Session config validation', {
                hasConfig: !!sessionConfig,
                hasAiInstructions: !!sessionConfig?.aiInstructions,
                instructionLength: sessionConfig?.aiInstructions?.length || 0,
                flowType: flowType,
                scriptId: sessionConfig?.scriptId,
                isTestMode: sessionConfig?.isTestMode
            }, sessionId);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: errorMessage,
                    code: 'MISSING_AI_INSTRUCTIONS'
                }));
            }
            // Clean up and return early
            activeConnections.delete(sessionId);
            return { geminiSession: null, isSessionActive: false };
        }
        logger_1.websocketLogger.info('AI instructions validation passed', {
            sessionId,
            instructionLength: sessionConfig.aiInstructions.length
        });
        const instructionPreview = sessionConfig.aiInstructions.substring(0, 150) +
            (sessionConfig.aiInstructions.length > 150 ? '...' : '');
        logger_1.websocketLogger.debug('Instructions preview', { sessionId, preview: instructionPreview });
        const connectionData = createConnectionData(sessionId, connection, sessionConfig, flowType, isIncomingCall);
        activeConnections.set(sessionId, connectionData);
        healthMonitor.trackConnection(sessionId, 'connected', {
            flowType,
            isTestMode: true,
            scriptId: sessionConfig.scriptId
        });
        // Use SessionManager for consistent session creation
        // Determine if this is an incoming call based on session config or query parameters
        const detectedIsIncomingCall = sessionConfig.isIncomingCall ||
            connection.query?.type === 'incoming' ||
            connection.query?.flow === 'inbound' ||
            sessionConfig.scriptType === 'incoming';
        const geminiSession = await deps.sessionManager.createGeminiSession(sessionId, {
            ...sessionConfig,
            sessionType: 'local_test',
            isIncomingCall: detectedIsIncomingCall // Properly detect incoming vs outbound testing
        }, connectionData);
        connectionData.geminiSession = geminiSession || undefined;
        let isSessionActive = false;
        if (geminiSession) {
            isSessionActive = true;
            await initTranscription(sessionId, deps, connectionData);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-started',
                    sessionId,
                    flowType,
                    scriptId: sessionConfig.scriptId,
                    config: {
                        voice: sessionConfig.voice,
                        model: sessionConfig.model,
                        isIncomingCall: detectedIsIncomingCall,
                        transcriptionEnabled: !!connectionData.deepgramConnection
                    }
                }));
            }
        }
        return { geminiSession, isSessionActive };
    }
    catch (error) {
        if (ws.readyState === 1) {
            ws.send(JSON.stringify({ type: 'session-error', error: `Session start failed: ${error.message}` }));
        }
        return { geminiSession: null, isSessionActive: false };
    }
}
function createConnectionData(sessionId, connection, sessionConfig, flowType, isIncomingCall) {
    return {
        localWs: (connection.socket || connection),
        sessionId,
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'local_test',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Test Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId,
        isTestMode: true,
        lastAIResponse: Date.now(),
        responseTimeouts: 0,
        connectionQuality: 'good',
        lastContextSave: Date.now(),
        contextSaveInterval: null
    };
}
// createGeminiSession function removed - now using SessionManager.createGeminiSession() for consistency
function handleGeminiMessage(sessionId, message, deps, connectionData) {
    logger_1.websocketLogger.debug('handleGeminiMessage called', {
        sessionId,
        messageKeys: Object.keys(message || {})
    });
    if (message.setupComplete || message.goAway) {
        logger_1.websocketLogger.debug('Skipping setupComplete or goAway message', { sessionId });
        return;
    }
    const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
    const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
    logger_1.websocketLogger.debug('Audio check', {
        sessionId,
        audioExists: !!audio,
        dataLength: audio?.data?.length || 0
    });
    // ENHANCED AUDIO HANDLING - Match working local testing handler
    if (audio && audio.data && audio.data.length > 0) {
        // Get fresh connection data to ensure we have the latest WebSocket state
        const freshConnectionData = deps.activeConnections?.get(sessionId) || connectionData;
        const localWs = freshConnectionData?.localWs;
        logger_1.websocketLogger.debug('Audio forwarding check', {
            sessionId,
            audioExists: !!audio,
            dataLength: audio.data?.length || 0,
            localWs: !!localWs,
            readyState: localWs?.readyState
        });
        if (localWs && localWs.readyState === 1) { // WebSocket.OPEN
            logger_1.websocketLogger.debug('Sending audio response to client', {
                sessionId,
                size: audio.data?.length || 0,
                mimeType: audio.mimeType
            });
            try {
                // Send audio back to client with metadata for proper playback
                localWs.send(JSON.stringify({
                    type: 'audio',
                    audio: audio.data,
                    mimeType: audio.mimeType // Include mime type for sample rate info
                }));
                logger_1.websocketLogger.info('Audio sent successfully to WebSocket', { sessionId });
            }
            catch (sendError) {
                logger_1.websocketLogger.error('Error sending audio to WebSocket', sendError, sessionId);
                logger_1.websocketLogger.debug('WebSocket state after error', { readyState: localWs.readyState }, sessionId);
            }
        }
        else {
            logger_1.websocketLogger.warn('Cannot send audio', {
                sessionId,
                localWs: !!localWs,
                readyState: localWs?.readyState
            });
        }
    }
    if (text) {
        logger_1.websocketLogger.debug('Text response received', {
            sessionId,
            preview: text.substring(0, 100)
        });
        connectionData.lastAIResponse = Date.now();
        connectionData.responseTimeouts = 0;
        connectionData.connectionQuality = 'good';
    }
}
async function initTranscription(sessionId, deps, connectionData) {
    try {
        const dgConnection = await deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData);
        if (dgConnection) {
            connectionData.deepgramConnection = dgConnection;
        }
    }
    catch (error) {
        // Silently handle Deepgram connection errors
        logger_1.websocketLogger.warn('Deepgram connection failed', error, sessionId);
    }
}
//# sourceMappingURL=start-session.js.map