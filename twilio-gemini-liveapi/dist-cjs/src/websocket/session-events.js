"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleEndSession = handleEndSession;
exports.handleRequestSummary = handleRequestSummary;
const session_utils_1 = require("./session-utils");
async function handleEndSession(sessionId, deps, activeConnections, lifecycleManager) {
    const connectionData = activeConnections.get(sessionId);
    if (connectionData) {
        if (connectionData.deepgramConnection) {
            deps.transcriptionManager.closeTranscription(sessionId);
        }
        if (lifecycleManager) {
            await lifecycleManager.endSession(sessionId, connectionData, 'user_end_testing');
        }
        else {
            await (0, session_utils_1.endSession)(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
        }
    }
    else {
        await (0, session_utils_1.endSession)(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
    }
}
async function handleRequestSummary(sessionId, _deps, activeConnections, summaryManager, contextManager) {
    const summaryConnectionData = activeConnections.get(sessionId);
    if (summaryConnectionData && summaryManager) {
        await summaryManager.requestSummary(sessionId, summaryConnectionData, contextManager);
    }
}
//# sourceMappingURL=session-events.js.map