"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerWebSocketHandlers = registerWebSocketHandlers;
const transcription_manager_1 = require("../audio/transcription-manager");
const twilio_flow_handler_1 = require("./twilio-flow-handler");
const local_testing_handler_1 = require("./local-testing-handler");
const config_handlers_1 = require("./config-handlers");
const logger_1 = require("../utils/logger");
const websocket_helpers_1 = require("./websocket-helpers");
// WebSocket handlers for different connection types
function registerWebSocketHandlers(fastify, dependencies) {
    // Initialize global transcription manager
    const transcriptionManager = new transcription_manager_1.TranscriptionManager();
    // Add transcription manager to dependencies for consistency
    const enhancedDependencies = {
        ...dependencies,
        transcriptionManager
    };
    // Register all WebSocket endpoints using helper functions
    const endpointConfigs = (0, websocket_helpers_1.createStandardEndpointConfigs)({
        handleOutboundCall,
        handleInboundCall,
        handleOutboundTesting,
        handleInboundTesting
    });
    (0, websocket_helpers_1.registerMultipleWebSocketEndpoints)(fastify, endpointConfigs, enhancedDependencies);
}
// 1. OUTBOUND CALLS - Handle real outbound calls via Twilio
function handleOutboundCall(connection, deps) {
    (0, websocket_helpers_1.logWebSocketConnection)('outbound', deps.isTestMode || false);
    (0, twilio_flow_handler_1.handleTwilioFlow)(connection, {
        ...deps,
        flowType: 'outbound_call',
        getSessionConfig: (callSid) => (0, config_handlers_1.getOutboundCallConfig)(deps, callSid),
        isIncomingCall: false
    });
}
// 2. INBOUND CALLS - Handle real inbound calls via Twilio
function handleInboundCall(connection, deps) {
    (0, websocket_helpers_1.logWebSocketConnection)('inbound', deps.isTestMode || false);
    (0, twilio_flow_handler_1.handleTwilioFlow)(connection, {
        ...deps,
        flowType: 'inbound_call',
        getSessionConfig: (callSid) => (0, config_handlers_1.getInboundCallConfig)(deps, callSid),
        isIncomingCall: true
    });
}
// 3. OUTBOUND TESTING MODE - Test outbound scripts locally
function handleOutboundTesting(connection, deps) {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    (0, websocket_helpers_1.logWebSocketConnection)('outbound', true, 'testing mode');
    if (enableDetailedLogging) {
        logger_1.websocketLogger.debug(`[OUTBOUND TEST] Connection object: ${!!connection}`);
        logger_1.websocketLogger.debug(`[OUTBOUND TEST] Connection socket: ${!!connection.socket}`);
    }
    (0, local_testing_handler_1.handleLocalTestingFlow)(connection, {
        ...deps,
        flowType: 'outbound_test',
        getSessionConfig: (callSid) => (0, config_handlers_1.getOutboundTestConfig)(deps),
        getOutboundTestConfig: config_handlers_1.getOutboundTestConfig,
        getInboundTestConfig: config_handlers_1.getInboundTestConfig,
        isIncomingCall: false
    });
}
// 4. INBOUND TESTING MODE - Test inbound scripts locally
function handleInboundTesting(connection, deps) {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    (0, websocket_helpers_1.logWebSocketConnection)('inbound', true, 'testing mode');
    if (enableDetailedLogging) {
        logger_1.websocketLogger.debug(`[INBOUND TEST] Connection details logged`);
    }
    (0, local_testing_handler_1.handleLocalTestingFlow)(connection, {
        ...deps,
        flowType: 'inbound_test',
        getSessionConfig: (callSid) => (0, config_handlers_1.getInboundTestConfig)(deps),
        getOutboundTestConfig: config_handlers_1.getOutboundTestConfig,
        getInboundTestConfig: config_handlers_1.getInboundTestConfig,
        isIncomingCall: true
    });
}
//# sourceMappingURL=handlers.js.map