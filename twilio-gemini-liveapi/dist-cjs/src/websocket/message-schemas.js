"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionConfigSchema = exports.LocalMessageSchema = exports.LocalAudioResponseMessageSchema = exports.LocalRequestSummaryMessageSchema = exports.LocalTurnCompleteMessageSchema = exports.LocalHeartbeatMessageSchema = exports.LocalEndSessionMessageSchema = exports.LocalTextMessageSchema = exports.LocalAudioMessageSchema = exports.LocalStartMessageSchema = exports.TwilioMessageSchema = exports.TwilioConnectedMessageSchema = exports.TwilioMarkMessageSchema = exports.TwilioStopMessageSchema = exports.TwilioMediaMessageSchema = exports.TwilioStartMessageSchema = void 0;
exports.validateTwilioMessage = validateTwilioMessage;
exports.validateLocalMessage = validateLocalMessage;
exports.validateSessionConfig = validateSessionConfig;
exports.validateWithSchema = validateWithSchema;
exports.formatValidationError = formatValidationError;
const zod_1 = require("zod");
// ========================================
// TWILIO MESSAGE SCHEMAS
// ========================================
/**
 * Twilio Media Stream - Start message schema
 */
exports.TwilioStartMessageSchema = zod_1.z.object({
    event: zod_1.z.literal('start'),
    start: zod_1.z.object({
        streamSid: zod_1.z.string().min(1),
        accountSid: zod_1.z.string().regex(/^AC[a-f0-9]{32}$/),
        callSid: zod_1.z.string().regex(/^CA[a-f0-9]{32}$/),
        tracks: zod_1.z.array(zod_1.z.string()).optional(),
        customParameters: zod_1.z.record(zod_1.z.any()).optional()
    }),
    streamSid: zod_1.z.string().optional()
});
/**
 * Twilio Media Stream - Media message schema
 */
exports.TwilioMediaMessageSchema = zod_1.z.object({
    event: zod_1.z.literal('media'),
    media: zod_1.z.object({
        payload: zod_1.z.string().min(1), // Base64 encoded audio
        chunk: zod_1.z.string().optional(),
        timestamp: zod_1.z.string().optional(),
        track: zod_1.z.string().optional()
    }),
    streamSid: zod_1.z.string().optional(),
    sequenceNumber: zod_1.z.string().optional()
});
/**
 * Twilio Media Stream - Stop message schema
 */
exports.TwilioStopMessageSchema = zod_1.z.object({
    event: zod_1.z.literal('stop'),
    streamSid: zod_1.z.string().optional(),
    stop: zod_1.z.object({
        accountSid: zod_1.z.string().optional(),
        callSid: zod_1.z.string().optional()
    }).optional()
});
/**
 * Twilio Media Stream - Mark message schema
 */
exports.TwilioMarkMessageSchema = zod_1.z.object({
    event: zod_1.z.literal('mark'),
    streamSid: zod_1.z.string().optional(),
    mark: zod_1.z.object({
        name: zod_1.z.string()
    }).optional()
});
/**
 * Twilio Media Stream - Connected message schema
 */
exports.TwilioConnectedMessageSchema = zod_1.z.object({
    event: zod_1.z.literal('connected'),
    protocol: zod_1.z.string().optional(),
    version: zod_1.z.string().optional()
});
/**
 * Union of all Twilio message types
 */
exports.TwilioMessageSchema = zod_1.z.discriminatedUnion('event', [
    exports.TwilioStartMessageSchema,
    exports.TwilioMediaMessageSchema,
    exports.TwilioStopMessageSchema,
    exports.TwilioMarkMessageSchema,
    exports.TwilioConnectedMessageSchema
]);
// ========================================
// LOCAL BROWSER MESSAGE SCHEMAS
// ========================================
/**
 * Local browser - Start session message schema
 */
exports.LocalStartMessageSchema = zod_1.z.object({
    type: zod_1.z.enum(['start-session', 'start_session']), // Accept both formats
    aiInstructions: zod_1.z.string().optional(),
    voice: zod_1.z.string().optional(),
    model: zod_1.z.string().optional(),
    scriptId: zod_1.z.string().optional()
});
/**
 * Local browser - Audio data message schema
 */
exports.LocalAudioMessageSchema = zod_1.z.object({
    type: zod_1.z.enum(['audio-data', 'audio']),
    audio: zod_1.z.string().optional(), // Base64 encoded audio
    audioData: zod_1.z.string().optional() // Alternative field name
});
/**
 * Local browser - Text message schema
 */
exports.LocalTextMessageSchema = zod_1.z.object({
    type: zod_1.z.literal('text-message'),
    text: zod_1.z.string().min(1)
});
/**
 * Local browser - End session message schema
 */
exports.LocalEndSessionMessageSchema = zod_1.z.object({
    type: zod_1.z.enum(['end-session', 'end_session']) // Accept both formats
});
/**
 * Local browser - Heartbeat message schema
 */
exports.LocalHeartbeatMessageSchema = zod_1.z.object({
    type: zod_1.z.literal('heartbeat')
});
/**
 * Local browser - Turn complete message schema
 */
exports.LocalTurnCompleteMessageSchema = zod_1.z.object({
    type: zod_1.z.literal('turn-complete')
});
/**
 * Local browser - Request summary message schema
 */
exports.LocalRequestSummaryMessageSchema = zod_1.z.object({
    type: zod_1.z.literal('request-summary')
});
/**
 * Local browser - Audio response message schema (from server)
 */
exports.LocalAudioResponseMessageSchema = zod_1.z.object({
    type: zod_1.z.literal('audio-response'),
    audio: zod_1.z.string().optional()
});
/**
 * Union of all local browser message types
 */
exports.LocalMessageSchema = zod_1.z.discriminatedUnion('type', [
    exports.LocalStartMessageSchema,
    exports.LocalAudioMessageSchema,
    exports.LocalTextMessageSchema,
    exports.LocalEndSessionMessageSchema,
    exports.LocalHeartbeatMessageSchema,
    exports.LocalTurnCompleteMessageSchema,
    exports.LocalRequestSummaryMessageSchema,
    exports.LocalAudioResponseMessageSchema
]);
// ========================================
// SESSION CONFIGURATION SCHEMAS
// ========================================
/**
 * Session configuration schema
 */
const shared_types_1 = require("../types/shared-types");
exports.SessionConfigSchema = zod_1.z.object({
    aiInstructions: zod_1.z.string().min(1),
    voice: shared_types_1.VoiceSchema,
    model: shared_types_1.ModelSchema,
    targetName: zod_1.z.string().nullable().optional(),
    targetPhoneNumber: zod_1.z.string().nullable().optional(),
    scriptType: shared_types_1.ScriptTypeSchema,
    scriptId: zod_1.z.string().min(1),
    isIncomingCall: zod_1.z.boolean().optional(),
    isTestMode: zod_1.z.boolean().optional()
});
// ========================================
// VALIDATION HELPERS
// ========================================
/**
 * Validates a Twilio message and returns parsed data or error
 */
function validateTwilioMessage(data) {
    const result = exports.TwilioMessageSchema.safeParse(data);
    return {
        success: result.success,
        data: result.success ? result.data : undefined,
        error: result.success ? undefined : result.error
    };
}
/**
 * Validates a local browser message and returns parsed data or error
 */
function validateLocalMessage(data) {
    const result = exports.LocalMessageSchema.safeParse(data);
    return {
        success: result.success,
        data: result.success ? result.data : undefined,
        error: result.success ? undefined : result.error
    };
}
/**
 * Validates session configuration and returns parsed data or error
 */
function validateSessionConfig(data) {
    const result = exports.SessionConfigSchema.safeParse(data);
    return {
        success: result.success,
        data: result.success ? result.data : undefined,
        error: result.success ? undefined : result.error
    };
}
/**
 * Generic validation function for any schema
 */
function validateWithSchema(schema, data) {
    const result = schema.safeParse(data);
    return {
        success: result.success,
        data: result.success ? result.data : undefined,
        error: result.success ? undefined : result.error
    };
}
// ========================================
// ERROR FORMATTING
// ========================================
/**
 * Formats Zod validation errors into a readable string
 */
function formatValidationError(error) {
    const issues = error.issues.map(issue => {
        const path = issue.path.join('.');
        return `${path ? `${path}: ` : ''}${issue.message}`;
    });
    return `Validation failed: ${issues.join(', ')}`;
}
//# sourceMappingURL=message-schemas.js.map