{"version": 3, "file": "audio-handlers.js", "sourceRoot": "", "sources": ["../../../src/websocket/audio-handlers.ts"], "names": [], "mappings": ";;AAUA,0CAkCC;AAED,8CAcC;AAED,gDAaC;AAnED,4CAAgD;AAEzC,KAAK,UAAU,eAAe,CACjC,SAAiB,EACjB,IAAuB,EACvB,aAAkB,EAClB,eAAwB,EACxB,IAA2B,EAC3B,iBAA8C,EAC9C,gBAAkC,EAClC,eAAgC,EAChC,QAAgB;IAEhB,IAAI,aAAa,IAAI,eAAe,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QACrE,IAAI,CAAC;YACD,kEAAkE;YAClE,gBAAgB,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YACxE,6EAA6E;YAC7E,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;YACvD,MAAM,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;YAC1F,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACxD,IAAI,cAAc,IAAI,cAAc,CAAC,kBAAkB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBACnF,IAAI,CAAC;oBACD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;oBACvD,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAC/E,CAAC;gBAAC,OAAO,kBAAkB,EAAE,CAAC;oBAC1B,sBAAa,CAAC,KAAK,CAAC,sCAAsC,EAAE,kBAA2B,EAAE,SAAS,CAAC,CAAC;gBACxG,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACxD,IAAI,cAAc,IAAI,eAAe,IAAI,eAAe,CAAC,aAAa,CAAC,SAAS,EAAE,iBAAiB,CAAC,EAAE,CAAC;gBACnG,MAAM,eAAe,CAAC,cAAc,CAAC,SAAS,EAAE,wBAAwB,EAAE,iBAAiB,CAAC,CAAC;YACjG,CAAC;QACL,CAAC;IACL,CAAC;AACL,CAAC;AAEM,KAAK,UAAU,iBAAiB,CACnC,SAAiB,EACjB,IAAsB,EACtB,aAAkB,EAClB,eAAwB,EACxB,IAA2B;IAE3B,IAAI,aAAa,IAAI,eAAe,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACpF,CAAC;QAAC,OAAO,SAAS,EAAE,CAAC;YACjB,sBAAa,CAAC,KAAK,CAAC,8BAA8B,EAAE,SAAkB,EAAE,SAAS,CAAC,CAAC;QACvF,CAAC;IACL,CAAC;AACL,CAAC;AAEM,KAAK,UAAU,kBAAkB,CACpC,SAAiB,EACjB,aAAkB,EAClB,eAAwB,EACxB,IAA2B;IAE3B,IAAI,aAAa,IAAI,eAAe,EAAE,CAAC;QACnC,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,iBAAiB,EAAE,CAAC;YACzB,sBAAa,CAAC,KAAK,CAAC,uCAAuC,EAAE,iBAA0B,EAAE,SAAS,CAAC,CAAC;QACxG,CAAC;IACL,CAAC;AACL,CAAC"}