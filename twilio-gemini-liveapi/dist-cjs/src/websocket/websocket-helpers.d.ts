import { FastifyInstance } from 'fastify';
import type { WebSocketConnection, FlowDependencies } from '../types/websocket';
export interface WebSocketEndpointConfig {
    path: string;
    handler: (connection: WebSocketConnection, deps: FlowDependencies) => void;
    callType: 'inbound' | 'outbound';
    isTestMode: boolean;
    logMessage?: string;
}
/**
 * Validates WebSocket authentication
 */
export declare function validateWebSocketAuth(req: any): boolean;
/**
 * Handles WebSocket authentication failure
 */
export declare function handleAuthFailure(connection: any, endpoint: string): void;
/**
 * Creates enhanced dependencies for WebSocket handlers
 */
export declare function createEnhancedDependencies(baseDependencies: any, callType: 'inbound' | 'outbound', isTestMode: boolean): FlowDependencies;
/**
 * Registers a WebSocket endpoint with authentication and error handling
 */
export declare function registerWebSocketEndpoint(fastify: FastifyInstance, config: WebSocketEndpointConfig, baseDependencies: any): void;
/**
 * Registers multiple WebSocket endpoints efficiently
 */
export declare function registerMultipleWebSocketEndpoints(fastify: FastifyInstance, configs: WebSocketEndpointConfig[], baseDependencies: any): void;
/**
 * Creates standard WebSocket endpoint configurations
 */
export declare function createStandardEndpointConfigs(handlers: {
    handleOutboundCall: (connection: WebSocketConnection, deps: FlowDependencies) => void;
    handleInboundCall: (connection: WebSocketConnection, deps: FlowDependencies) => void;
    handleOutboundTesting: (connection: WebSocketConnection, deps: FlowDependencies) => void;
    handleInboundTesting: (connection: WebSocketConnection, deps: FlowDependencies) => void;
}): WebSocketEndpointConfig[];
/**
 * Logs WebSocket connection details
 */
export declare function logWebSocketConnection(callType: 'inbound' | 'outbound', isTestMode: boolean, additionalInfo?: string): void;
/**
 * Creates flow dependencies with proper typing
 */
export declare function createFlowDependencies(baseDeps: any, overrides: Partial<FlowDependencies>): FlowDependencies;
//# sourceMappingURL=websocket-helpers.d.ts.map