{"version": 3, "file": "websocket-helpers.js", "sourceRoot": "", "sources": ["../../../src/websocket/websocket-helpers.ts"], "names": [], "mappings": ";;AAiBA,sDAwEC;AAKD,8CAGC;AAKD,gEAUC;AAKD,8DA6CC;AAKD,gFAQC;AAKD,sEA+CC;AAKD,wDASC;AAKD,wDAQC;AA3PD,4CAA8D;AAC9D,mCAAyC;AAUzC;;GAEG;AACH,SAAgB,qBAAqB,CAAC,GAAQ;IAC1C,gEAAgE;IAChE,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC;QACnD,GAAG,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAEzD,IAAI,eAAe,EAAE,CAAC;QAClB,mBAAU,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,oFAAoF;IACpF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;QAC7E,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,mBAAU,CAAC,KAAK,CAAC,kDAAkD,EAAE;gBACjE,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,EAAE,EAAE,GAAG,CAAC,MAAM,EAAE,aAAa;aAChC,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAEhD,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;gBACtD,mBAAU,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBACpD,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,wBAAwB;YACxB,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gBACpB,mBAAU,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBAC/C,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,mCAAmC;YACnC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;YAC3I,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,mBAAU,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;gBACpE,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,mEAAmE;YACnE,IAAI,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE,CAAC;gBACtC,mBAAU,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;gBACtD,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,IAAA,wBAAe,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;gBACjE,mBAAU,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBAC/C,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,mBAAU,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,mBAAU,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACjH,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED,0DAA0D;IAC1D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;QAClC,mBAAU,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;IACnF,CAAC;SAAM,CAAC;QACJ,mBAAU,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;IACzF,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,UAAe,EAAE,QAAgB;IAC/D,mBAAU,CAAC,KAAK,CAAC,wCAAwC,QAAQ,EAAE,CAAC,CAAC;IACrE,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,yBAAyB,CAAC,CAAC;AAC7D,CAAC;AAED;;GAEG;AACH,SAAgB,0BAA0B,CACtC,gBAAqB,EACrB,QAAgC,EAChC,UAAmB;IAEnB,OAAO;QACH,GAAG,gBAAgB;QACnB,QAAQ;QACR,UAAU;KACO,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CACrC,OAAwB,EACxB,MAA+B,EAC/B,gBAAqB;IAErB,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;QAC/B,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,UAAe,EAAE,GAAQ,EAAE,EAAE;YACxE,8BAA8B;YAC9B,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACpB,wBAAe,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACxC,wBAAe,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YACzE,CAAC;YAED,kDAAkD;YAClD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3D,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;YACnE,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC;YAE3D,IAAI,OAAO,EAAE,CAAC;gBACV,wBAAe,CAAC,IAAI,CAAC,sCAAsC,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5F,CAAC;iBAAM,CAAC;gBACJ,wBAAe,CAAC,IAAI,CAAC,mCAAmC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;YAC7F,CAAC;YAED,0BAA0B;YAC1B,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,iBAAiB,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC3C,OAAO;YACX,CAAC;YAED,qDAAqD;YACrD,MAAM,YAAY,GAAG,0BAA0B,CAC3C,gBAAgB,EAChB,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,UAAU,CACpB,CAAC;YAEF,uEAAuE;YACtE,UAAkB,CAAC,WAAW,GAAG,WAAW,CAAC;YAC7C,UAAkB,CAAC,OAAO,GAAG,OAAO,CAAC;YAEtC,mBAAmB;YACnB,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,SAAgB,kCAAkC,CAC9C,OAAwB,EACxB,OAAkC,EAClC,gBAAqB;IAErB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACrB,yBAAyB,CAAC,OAAO,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,SAAgB,6BAA6B,CAAC,QAK7C;IACG,OAAO;QACH;YACI,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,QAAQ,CAAC,kBAAkB;YACpC,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,mDAAmD;SAClE;QACD;YACI,IAAI,EAAE,uBAAuB;YAC7B,OAAO,EAAE,QAAQ,CAAC,iBAAiB;YACnC,QAAQ,EAAE,SAAS;YACnB,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,2DAA2D;SAC1E;QACD;YACI,IAAI,EAAE,gBAAgB;YACtB,OAAO,EAAE,QAAQ,CAAC,qBAAqB;YACvC,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,IAAI;SACnB;QACD;YACI,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,QAAQ,CAAC,oBAAoB;YACtC,QAAQ,EAAE,SAAS;YACnB,UAAU,EAAE,IAAI;SACnB;QACD;YACI,IAAI,EAAE,sBAAsB;YAC5B,OAAO,EAAE,QAAQ,CAAC,qBAAqB;YACvC,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,IAAI;SACnB;QACD;YACI,IAAI,EAAE,cAAc;YACpB,OAAO,EAAE,QAAQ,CAAC,qBAAqB;YACvC,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,0DAA0D;SACzE;KACJ,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CAClC,QAAgC,EAChC,UAAmB,EACnB,cAAuB;IAEvB,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IACpC,MAAM,IAAI,GAAG,cAAc,CAAC,CAAC,CAAC,MAAM,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1D,wBAAe,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC;AACpE,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CAClC,QAAa,EACb,SAAoC;IAEpC,OAAO;QACH,GAAG,QAAQ;QACX,GAAG,SAAS;KACK,CAAC;AAC1B,CAAC"}