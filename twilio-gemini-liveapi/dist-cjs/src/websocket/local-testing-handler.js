"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleLocalTestingFlow = handleLocalTestingFlow;
const session_utils_1 = require("./session-utils");
const logger_1 = require("../utils/logger");
const heartbeat_manager_1 = require("./heartbeat-manager");
const genai_1 = __importDefault(require("@google/genai"));
const { Modality } = genai_1.default;
const performance_optimizations_1 = require("./performance-optimizations");
const message_schemas_1 = require("./message-schemas");
// Performance optimization instances
const startupOptimizer = new performance_optimizations_1.SessionStartupOptimizer({
    enableFastStart: true,
    skipNonEssentialChecks: false,
    preloadGeminiSession: false,
    parallelInitialization: true,
    timeoutMs: 8000 // Reduced from 15+ seconds to 8 seconds
});
const configLoader = new performance_optimizations_1.FastConfigLoader();
const qualityMonitor = new performance_optimizations_1.ConnectionQualityMonitor();
// Common local testing flow handler (for both inbound and outbound testing)
function handleLocalTestingFlow(connection, deps) {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    if (enableDetailedLogging) {
        logger_1.websocketLogger.debug('HANDLELOCALTESTINGFLOW CALLED!', { flowType: deps.flowType });
    }
    const { sessionManager, contextManager, activeConnections, healthMonitor, summaryManager, lifecycleManager, recoveryManager, transcriptionManager, flowType, getSessionConfig, isIncomingCall, SUMMARY_GENERATION_PROMPT } = deps;
    const sessionId = `${flowType}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    if (enableDetailedLogging) {
        logger_1.websocketLogger.debug('About to log testing session started', { sessionId });
    }
    logger_1.websocketLogger.info(`${flowType.toUpperCase()} testing session started`, { sessionId });
    if (enableDetailedLogging) {
        logger_1.websocketLogger.debug(`Setting up message handler for ${flowType} testing`, { sessionId });
        logger_1.websocketLogger.debug('Step 1: About to check WebSocket state', { sessionId });
    }
    try {
        if (enableDetailedLogging) {
            logger_1.websocketLogger.debug('Connection object inspected', { sessionId, hasConnection: !!connection });
            logger_1.websocketLogger.debug('Connection keys', { sessionId, keys: Object.keys(connection) });
            logger_1.websocketLogger.debug('Socket object present', { sessionId, hasSocket: !!connection.socket });
        }
        // Try different possible WebSocket references
        const ws = connection.socket || connection;
        if (enableDetailedLogging) {
            logger_1.websocketLogger.debug('Using WebSocket', { sessionId, hasWs: !!ws });
            logger_1.websocketLogger.debug('WebSocket readyState', { sessionId, readyState: ws.readyState });
            logger_1.websocketLogger.debug('WebSocket protocol', { sessionId, protocol: ws.protocol });
            logger_1.websocketLogger.debug('Step 3: About to declare variables', { sessionId });
        }
    }
    catch (wsError) {
        logger_1.websocketLogger.error('Error checking WebSocket state', wsError instanceof Error ? wsError : new Error(String(wsError)), sessionId);
        if (enableDetailedLogging) {
            logger_1.websocketLogger.error('Error stack trace', { stack: wsError.stack }, sessionId);
        }
        // Don't return early - continue with handler setup
        if (enableDetailedLogging) {
            logger_1.websocketLogger.debug('Continuing with handler setup despite WebSocket error', { sessionId });
        }
    }
    let geminiSession = null;
    let isSessionActive = false;
    if (enableDetailedLogging) {
        logger_1.websocketLogger.debug('Step 4: Variables declared, about to attach message handler', { sessionId });
    }
    // Use the correct WebSocket reference
    const ws = (connection.socket || connection);
    if (enableDetailedLogging) {
        logger_1.websocketLogger.debug('Attaching message handler', { sessionId, hasWs: !!ws });
    }
    // Store event listeners for cleanup
    const eventListeners = new Map();
    const messageHandler = async (message) => {
        if (enableDetailedLogging) {
            logger_1.websocketLogger.debug('MESSAGE HANDLER CALLED!', { sessionId });
            logger_1.websocketLogger.debug('MESSAGE HANDLER CALLED!', { sessionId });
            logger_1.websocketLogger.debug('Raw message received', { sessionId, preview: message.toString().substring(0, 200) });
        }
        try {
            const rawData = JSON.parse(message.toString());
            // Validate message with Zod schema
            const validation = (0, message_schemas_1.validateLocalMessage)(rawData);
            if (!validation.success) {
                logger_1.websocketLogger.warn('❌ Invalid local message format', {
                    sessionId,
                    error: (0, message_schemas_1.formatValidationError)(validation.error),
                    rawData
                });
                return;
            }
            const data = validation.data;
            if (enableDetailedLogging) {
                logger_1.websocketLogger.debug('✅ Local message validated', { sessionId, messageType: data.type });
                logger_1.websocketLogger.debug(`Parsed message type: ${data.type}`, { sessionId });
                logger_1.websocketLogger.debug('Message data keys', { sessionId, keys: Object.keys(data) });
                logger_1.websocketLogger.debug('===== MESSAGE SWITCH DEBUG =====', { sessionId });
                logger_1.websocketLogger.debug(`data.type = "${data.type}"`, { sessionId });
                logger_1.websocketLogger.debug('About to enter switch statement', { sessionId });
                logger_1.websocketLogger.debug('=====================================', { sessionId });
            }
            switch (data.type) {
                case 'start-session': {
                    if (enableDetailedLogging) {
                        logger_1.websocketLogger.debug('ENTERING START-SESSION HANDLER', { sessionId, flowType });
                    }
                    const sessionData = await handleLocalStartSession(sessionId, data, deps, connection, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                    geminiSession = sessionData.geminiSession;
                    isSessionActive = sessionData.isSessionActive;
                    if (enableDetailedLogging) {
                        logger_1.websocketLogger.debug('START-SESSION HANDLER COMPLETED', { sessionId, hasGeminiSession: !!geminiSession, isSessionActive });
                    }
                    break;
                }
                case 'audio-data':
                    // Normalize legacy type name to the preferred 'audio'
                    data.type = 'audio';
                // Fall through to 'audio' case
                case 'audio':
                    await handleAudioData(sessionId, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager, flowType);
                    break;
                case 'text-message':
                    await handleTextMessage(sessionId, data, geminiSession, isSessionActive, deps);
                    break;
                case 'turn-complete':
                    await handleTurnComplete(sessionId, geminiSession, isSessionActive, deps);
                    break;
                case 'end-session':
                    await handleEndSession(sessionId, deps, activeConnections, lifecycleManager);
                    break;
                case 'request-summary':
                    await handleRequestSummary(sessionId, deps, activeConnections, summaryManager, contextManager);
                    break;
                case 'heartbeat':
                    // Handle heartbeat messages silently - just acknowledge
                    logger_1.websocketLogger.debug(`Heartbeat received for ${flowType} testing`, { sessionId });
                    break;
                case 'audio-response':
                    // Handle audio-response events (frontend might be echoing back audio)
                    logger_1.websocketLogger.debug(`Audio response received for ${flowType} testing`, { sessionId });
                    // Don't process these - they're just echoes from the frontend
                    break;
                default:
                    logger_1.websocketLogger.warn(`Unknown ${flowType} testing event: ${data.type}`, { sessionId });
            }
        }
        catch (error) {
            logger_1.websocketLogger.error(`Error processing ${flowType} testing message`, error instanceof Error ? error : new Error(String(error)), sessionId);
        }
    };
    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    logger_1.websocketLogger.debug('ATTACHING MESSAGE HANDLER TO WebSocket', { sessionId, readyState: ws.readyState });
    ws.on('message', messageHandler);
    const closeHandler = async (code, reason) => {
        logger_1.websocketLogger.info(`${flowType.toUpperCase()} testing connection closed`, {
            sessionId,
            code,
            reason: reason ? reason.toString() : 'No reason'
        });
        // Stop heartbeat monitoring
        heartbeat_manager_1.globalHeartbeatManager.stopHeartbeat(sessionId);
        // This is a user-initiated session end via connection close
        const connectionData = activeConnections.get(sessionId);
        if (connectionData) {
            // Clean up Deepgram transcription
            if (connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(sessionId);
            }
            if (deps.lifecycleManager) {
                await deps.lifecycleManager.endSession(sessionId, connectionData, 'user_close_testing');
            }
            else {
                (0, session_utils_1.endSession)(sessionId, deps, 'connection_closed');
            }
        }
        else {
            (0, session_utils_1.endSession)(sessionId, deps, 'connection_closed');
        }
        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler);
        }
        eventListeners.clear();
    };
    const errorHandler = async (error) => {
        logger_1.websocketLogger.error(`${flowType.toUpperCase()} testing error`, { sessionId, error });
        logger_1.websocketLogger.error('Error stack', { sessionId, stack: error.stack });
        // WebSocket error in testing - try to recover session instead of ending it
        const connectionData = activeConnections.get(sessionId);
        if (connectionData && recoveryManager && contextManager.canRecover(sessionId)) {
            logger_1.websocketLogger.info('Testing WebSocket error detected, attempting session recovery', { sessionId });
            contextManager.markSessionInterrupted(sessionId, 'testing_websocket_error');
            // Don't end session immediately - let recovery manager handle it
            (0, session_utils_1.scheduleRecovery)(sessionId, 'testing_websocket_error', recoveryManager, activeConnections);
        }
        else {
            // Clean up Deepgram transcription before ending session
            if (connectionData && connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(sessionId);
            }
            // Only end session if recovery is not possible
            (0, session_utils_1.endSession)(sessionId, { ...deps, transcriptionManager }, 'connection_error');
        }
    };
    // Register all event listeners and store for cleanup
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}
// Helper functions for message handling
async function handleLocalStartSession(sessionId, data, deps, connection, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager) {
    logger_1.websocketLogger.debug('===== OPTIMIZED START-SESSION =====', { sessionId });
    logger_1.websocketLogger.info('Starting testing session with performance optimizations', { sessionId, flowType });
    // Start performance tracking
    startupOptimizer.startTracking(sessionId);
    qualityMonitor.startMonitoring(sessionId, ws);
    try {
        // Optimized configuration loading with caching
        logger_1.websocketLogger.debug('Loading config with optimization', { sessionId, flowType });
        logger_1.websocketLogger.debug('getSessionConfig function type', { sessionId, type: typeof getSessionConfig });
        // Test the getSessionConfig function directly first
        logger_1.websocketLogger.debug('Testing getSessionConfig directly', { sessionId });
        try {
            const directConfig = await getSessionConfig();
            logger_1.websocketLogger.debug('Direct getSessionConfig result', {
                sessionId,
                hasResult: !!directConfig,
                resultType: typeof directConfig,
                resultKeys: directConfig ? Object.keys(directConfig) : [],
                hasAiInstructions: !!directConfig?.aiInstructions,
                aiInstructionsLength: directConfig?.aiInstructions?.length || 0
            });
        }
        catch (directError) {
            logger_1.websocketLogger.error('Error calling getSessionConfig directly', directError, sessionId);
        }
        let sessionConfig = await configLoader.loadConfig(sessionId, getSessionConfig, true // Use cache for better performance
        );
        startupOptimizer.markConfigLoaded(sessionId);
        logger_1.websocketLogger.debug('Optimized config loaded', { sessionId, loaded: !!sessionConfig });
        if (sessionConfig) {
            logger_1.websocketLogger.debug('Initial session config', {
                hasAiInstructions: !!sessionConfig.aiInstructions,
                aiInstructionsLength: sessionConfig.aiInstructions?.length || 0,
                voice: sessionConfig.voice,
                model: sessionConfig.model,
                scriptType: sessionConfig.scriptType,
                scriptId: sessionConfig.scriptId
            });
        }
        else {
            logger_1.websocketLogger.error(`getSessionConfig returned null/undefined for ${flowType}`, {}, sessionId);
        }
        // 🔍 ENHANCED DEBUGGING: Log detailed session config information
        logger_1.websocketLogger.debug('DETAILED SESSION CONFIG DEBUG', {
            hasSessionConfig: !!sessionConfig,
            sessionConfigKeys: sessionConfig ? Object.keys(sessionConfig) : [],
            hasAiInstructions: !!sessionConfig?.aiInstructions,
            aiInstructionsType: typeof sessionConfig?.aiInstructions,
            aiInstructionsLength: sessionConfig?.aiInstructions?.length || 0,
            aiInstructionsPreview: sessionConfig?.aiInstructions?.substring(0, 100) || 'N/A',
            flowType: flowType,
            isIncomingCall: isIncomingCall
        });
        // CRITICAL VALIDATION: Ensure AI instructions are present before proceeding
        if (!sessionConfig || !sessionConfig.aiInstructions || sessionConfig.aiInstructions.trim().length === 0) {
            const errorMessage = `No AI instructions available for ${flowType} session. Cannot create Gemini session without proper system instructions.`;
            logger_1.websocketLogger.error(`VALIDATION FAILED: ${errorMessage}`, {}, sessionId);
            logger_1.websocketLogger.error('Session config details', {
                hasConfig: !!sessionConfig,
                configKeys: sessionConfig ? Object.keys(sessionConfig) : [],
                aiInstructions: sessionConfig?.aiInstructions || 'undefined'
            }, sessionId);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: errorMessage,
                    code: 'MISSING_AI_INSTRUCTIONS',
                    debug: {
                        flowType: flowType,
                        hasConfig: !!sessionConfig,
                        configKeys: sessionConfig ? Object.keys(sessionConfig) : []
                    }
                }));
            }
            // Cleanup and return early
            qualityMonitor.stopMonitoring(sessionId);
            startupOptimizer.cleanup(sessionId);
            activeConnections.delete(sessionId);
            return { geminiSession: null, isSessionActive: false };
        }
        logger_1.websocketLogger.info('AI instructions validation passed', { sessionId, instructionLength: sessionConfig.aiInstructions.length });
        const instructionPreview = sessionConfig.aiInstructions.substring(0, 150) + (sessionConfig.aiInstructions.length > 150 ? '...' : '');
        logger_1.websocketLogger.debug('Instructions preview', { sessionId, preview: instructionPreview });
        // Allow override from client for testing - use campaign script as-is
        if (data.aiInstructions) {
            sessionConfig.aiInstructions = data.aiInstructions;
        }
        if (data.voice) {
            sessionConfig.voice = deps.voiceManager.getValidGeminiVoice(data.voice);
        }
        if (data.model) {
            sessionConfig.model = deps.modelManager.getValidGeminiModel(data.model);
        }
        if (data.scriptId) {
            // Load specific script for testing
            try {
                const testConfig = await deps.scriptManager.getScriptConfig(data.scriptId, isIncomingCall);
                if (testConfig) {
                    sessionConfig = {
                        ...testConfig,
                        aiInstructions: `[TESTING MODE] ${testConfig.aiInstructions}`,
                        isTestMode: true
                    };
                }
            }
            catch (error) {
                logger_1.websocketLogger.warn(`Error loading test script ${data.scriptId}`, { error: error instanceof Error ? error.message : String(error) }, sessionId);
            }
        }
        // Store enhanced connection data
        const connectionData = {
            ws: (connection.socket || connection), // Standardized WebSocket property name
            localWs: (connection.socket || connection),
            sessionId,
            isSessionActive: false,
            summaryRequested: false,
            summaryReceived: false,
            summaryText: '',
            conversationLog: [],
            fullTranscript: [],
            speechTranscript: [],
            isIncomingCall,
            sessionType: 'local_test',
            flowType,
            sessionStartTime: Date.now(),
            lastActivity: Date.now(),
            targetName: sessionConfig.targetName || 'Test Contact',
            targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
            originalAIInstructions: sessionConfig.aiInstructions,
            scriptId: sessionConfig.scriptId,
            isTestMode: true,
            // CRITICAL: Add missing turn management properties
            lastAIResponse: Date.now(), // Track AI responsiveness
            responseTimeouts: 0, // Count consecutive timeouts
            connectionQuality: 'good', // Track connection quality
            lastContextSave: Date.now(), // For periodic context saving
            contextSaveInterval: null // For periodic context saving
        };
        activeConnections.set(sessionId, connectionData);
        // Track connection health
        healthMonitor.trackConnection(sessionId, 'connected', {
            flowType,
            isTestMode: true,
            scriptId: sessionConfig.scriptId
        });
        // Optimized parallel session creation
        logger_1.websocketLogger.debug('Starting optimized Gemini session creation', { sessionId });
        const sessionStartTime = Date.now();
        let geminiSession = null;
        let isSessionActive = false;
        try {
            // Use the correct model and voice from environment/managers
            const correctModel = sessionConfig.model || deps.GEMINI_DEFAULT_MODEL;
            const correctVoice = sessionConfig.voice || deps.GEMINI_DEFAULT_VOICE;
            logger_1.websocketLogger.debug('OPTIMIZED: Using configuration', { sessionId, model: correctModel, voice: correctVoice });
            // Create Gemini session first
            logger_1.websocketLogger.debug('Creating Gemini session with optimized timeout', { sessionId });
            geminiSession = await deps.sessionManager.createGeminiSession(sessionId, {
                model: correctModel,
                voice: correctVoice,
                aiInstructions: sessionConfig.aiInstructions,
                scriptType: sessionConfig.scriptType || 'outbound',
                scriptId: sessionConfig.scriptId || '1'
            }, connectionData);
            // Initialize transcription in parallel (non-blocking)
            if (deps.transcriptionManager) {
                logger_1.websocketLogger.debug('Initializing transcription in background', { sessionId });
                deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData)
                    .then((transcriptionResult) => {
                    if (transcriptionResult) {
                        logger_1.websocketLogger.info('Background transcription initialized', { sessionId });
                    }
                })
                    .catch((error) => {
                    logger_1.websocketLogger.warn('Background transcription initialization failed', { error: error instanceof Error ? error.message : String(error) }, sessionId);
                });
            }
            startupOptimizer.markGeminiSessionCreated(sessionId);
            if (geminiSession) {
                isSessionActive = true;
                logger_1.websocketLogger.info('Optimized Gemini session created in parallel', { sessionId });
            }
            else {
                throw new Error('Failed to create Gemini session via optimized parallel initialization');
            }
            // Session is now created and configured by sessionManager
            // No need for additional setup - sessionManager handles Live API configuration
            // Start optimized WebSocket heartbeat monitoring for local testing
            heartbeat_manager_1.globalHeartbeatManager.startHeartbeat(sessionId, ws, deps.config?.websocket?.heartbeatInterval || 30000, deps.config?.websocket?.heartbeatTimeout || 60000, (sessionId, ws) => {
                logger_1.websocketLogger.warn('Optimized heartbeat timeout - ending session', { sessionId });
                qualityMonitor.stopMonitoring(sessionId);
                startupOptimizer.cleanup(sessionId);
                (0, session_utils_1.endSession)(sessionId, { activeConnections, lifecycleManager }, 'heartbeat_timeout');
            });
        }
        catch (error) {
            logger_1.websocketLogger.error('Error creating DIRECT Gemini session', error instanceof Error ? error : new Error(String(error)), sessionId);
            geminiSession = null;
        }
        if (geminiSession) {
            // Initialize session lifecycle for local testing
            lifecycleManager.initializeSession(sessionId, { connectionData, sessionConfig });
            logger_1.websocketLogger.info('Session lifecycle started for testing', { sessionId, flowType });
            // Initialize Deepgram transcription for local testing
            try {
                const dgConnection = await deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData);
                if (dgConnection) {
                    connectionData.deepgramConnection = dgConnection;
                    logger_1.websocketLogger.info('Local Deepgram transcription initialized for testing', { sessionId, flowType });
                }
            }
            catch (error) {
                logger_1.websocketLogger.warn('Failed to initialize local Deepgram transcription', { error: error instanceof Error ? error.message : String(error) }, sessionId);
            }
            // Mark session as ready and get performance metrics
            const performanceMetrics = startupOptimizer.markSessionReady(sessionId);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-started',
                    sessionId: sessionId,
                    flowType: flowType,
                    scriptId: sessionConfig.scriptId,
                    config: {
                        voice: sessionConfig.voice,
                        model: sessionConfig.model,
                        isIncomingCall: isIncomingCall,
                        transcriptionEnabled: !!connectionData.deepgramConnection
                    },
                    performance: performanceMetrics ? {
                        totalStartupTime: performanceMetrics.totalTime,
                        optimized: true
                    } : undefined
                }));
            }
            logger_1.websocketLogger.info('Testing session started successfully', { sessionId, flowType: flowType.toUpperCase() });
        }
        else {
            logger_1.websocketLogger.error('Gemini session creation returned null/undefined', {}, sessionId);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: 'Failed to create Gemini session'
                }));
            }
        }
        return { geminiSession, isSessionActive };
    }
    catch (startSessionError) {
        logger_1.websocketLogger.error(`Critical error in optimized start-session for ${flowType}`, startSessionError instanceof Error ? startSessionError : new Error(String(startSessionError)), sessionId);
        // Cleanup performance monitoring on error
        qualityMonitor.stopMonitoring(sessionId);
        startupOptimizer.cleanup(sessionId);
        try {
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: `Optimized session start failed: ${startSessionError.message}`,
                    optimized: true
                }));
            }
        }
        catch (sendError) {
            logger_1.websocketLogger.error('Failed to send error message', sendError instanceof Error ? sendError : new Error(String(sendError)), sessionId);
        }
        return { geminiSession: null, isSessionActive: false };
    }
}
async function handleAudioData(sessionId, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager, flowType) {
    if (geminiSession && isSessionActive && (data.audioData || data.audio)) {
        try {
            // Update activity for session persistence
            lifecycleManager.transitionState(sessionId, 'active', 'audio_received');
            // Handle browser audio data (support both audioData and audio fields)
            const base64Audio = data.audioData || data.audio || '';
            logger_1.websocketLogger.debug('About to call sendBrowserAudioToGemini', { sessionId, audioSize: base64Audio.length, hasGeminiSession: !!geminiSession });
            await deps.sessionManager.sendBrowserAudioToGemini(sessionId, geminiSession, base64Audio);
            // Send audio to Deepgram for transcription (local testing)
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && connectionData.deepgramConnection && deps.transcriptionManager) {
                try {
                    const audioBuffer = Buffer.from(base64Audio, 'base64');
                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                }
                catch (dgError) {
                    logger_1.websocketLogger.warn('Local Deepgram send error', { error: dgError instanceof Error ? dgError.message : String(dgError) }, sessionId);
                }
            }
        }
        catch (error) {
            logger_1.websocketLogger.error(`Error processing ${flowType} testing audio`, error instanceof Error ? error : new Error(String(error)), sessionId);
            // Check if we need to recover the session
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                logger_1.websocketLogger.info('Audio processing failed in testing, attempting session recovery', { sessionId, flowType });
                await recoveryManager.recoverSession(sessionId, 'audio_processing_error', activeConnections);
            }
        }
    }
}
async function handleTextMessage(sessionId, data, geminiSession, isSessionActive, deps) {
    if (geminiSession && isSessionActive && data.text) {
        try {
            logger_1.websocketLogger.debug('Received text message', { sessionId, text: data.text });
            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
        }
        catch (error) {
            logger_1.websocketLogger.error('Error sending text to Gemini', error instanceof Error ? error : new Error(String(error)), sessionId);
        }
    }
}
async function handleTurnComplete(sessionId, geminiSession, isSessionActive, deps) {
    // REMOVED: Turn complete handling for Live API
    // The Live API handles conversation turns automatically with Voice Activity Detection (VAD)
    // No manual turn management is needed - the model will respond when appropriate
    logger_1.websocketLogger.debug('Turn complete signal received - Live API handles this automatically', { sessionId });
}
async function handleEndSession(sessionId, deps, activeConnections, lifecycleManager) {
    logger_1.websocketLogger.info('Ending testing session - USER INITIATED', { sessionId });
    // This is a user-initiated session end for testing
    const endConnectionData = activeConnections.get(sessionId);
    if (endConnectionData) {
        // Clean up Deepgram transcription
        if (endConnectionData.deepgramConnection) {
            deps.transcriptionManager.closeTranscription(sessionId);
        }
        if (lifecycleManager) {
            await lifecycleManager.endSession(sessionId, endConnectionData, 'user_end_testing');
        }
        else {
            await (0, session_utils_1.endSession)(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
        }
    }
    else {
        await (0, session_utils_1.endSession)(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
    }
}
async function handleRequestSummary(sessionId, _deps, activeConnections, summaryManager, contextManager) {
    logger_1.websocketLogger.info('Manual summary requested for testing', { sessionId });
    const summaryConnectionData = activeConnections.get(sessionId);
    if (summaryConnectionData && summaryManager) {
        await summaryManager.requestSummary(sessionId, summaryConnectionData, contextManager);
    }
}
//# sourceMappingURL=local-testing-handler.js.map