import type { WebSocketDependencies, ConnectionData } from '../types/websocket';
export declare function endSession(sessionId: string, deps: WebSocketDependencies, reason?: string): Promise<void>;
export declare function getConnectionData(sessionId: string, activeConnections: Map<string, ConnectionData>): ConnectionData | undefined;
export declare function updateConnectionActivity(sessionId: string, activeConnections: Map<string, ConnectionData>): void;
export declare function isSessionActive(sessionId: string, activeConnections: Map<string, ConnectionData>): boolean;
export declare function scheduleRecovery(sessionId: string, reason: string, recoveryManager: any, activeConnections: Map<string, ConnectionData>, delay?: number): void;
//# sourceMappingURL=session-utils.d.ts.map