export interface SessionStartupMetrics {
    connectionStart: number;
    authComplete: number;
    configLoaded: number;
    geminiSessionCreated: number;
    sessionReady: number;
    totalTime: number;
}
export interface OptimizedSessionConfig {
    enableFastStart: boolean;
    skipNonEssentialChecks: boolean;
    preloadGeminiSession: boolean;
    parallelInitialization: boolean;
    timeoutMs: number;
}
/**
 * Optimized session startup with performance tracking
 */
export declare class SessionStartupOptimizer {
    private metrics;
    private config;
    constructor(config?: OptimizedSessionConfig);
    /**
     * Start tracking session startup performance
     */
    startTracking(sessionId: string): void;
    /**
     * Mark authentication complete
     */
    markAuthComplete(sessionId: string): void;
    /**
     * Mark configuration loaded
     */
    markConfigLoaded(sessionId: string): void;
    /**
     * Mark Gemini session created
     */
    markGeminiSessionCreated(sessionId: string): void;
    /**
     * Mark session ready and calculate total time
     */
    markSessionReady(sessionId: string): SessionStartupMetrics | null;
    /**
     * Get current metrics for a session
     */
    getMetrics(sessionId: string): SessionStartupMetrics | null;
    /**
     * Clean up metrics for completed session
     */
    cleanup(sessionId: string): void;
    /**
     * Log performance metrics with warnings for slow operations
     */
    private logPerformanceMetrics;
}
/**
 * Optimized WebSocket connection handler with timeout protection
 */
export declare function createOptimizedConnectionHandler(originalHandler: Function, optimizer: SessionStartupOptimizer): Function;
/**
 * Parallel initialization helper for faster startup
 */
export declare function parallelInitialization<T>(tasks: Array<() => Promise<T>>, timeoutMs?: number): Promise<T[]>;
/**
 * Fast session configuration loader with caching
 */
export declare class FastConfigLoader {
    private configCache;
    private cacheTimeout;
    /**
     * Load configuration with caching for better performance
     */
    loadConfig(sessionId: string, configLoader: (callSid?: string) => Promise<any>, useCache?: boolean): Promise<any>;
    /**
     * Clear expired cache entries
     */
    clearExpiredCache(): void;
    /**
     * Clear all cache
     */
    clearCache(): void;
}
/**
 * Connection quality monitor for early detection of issues
 */
export declare class ConnectionQualityMonitor {
    private qualityMetrics;
    /**
     * Start monitoring connection quality
     */
    startMonitoring(sessionId: string, ws: any): void;
    /**
     * Update connection quality based on metrics
     */
    private updateQuality;
    /**
     * Get current connection quality
     */
    getQuality(sessionId: string): string;
    /**
     * Stop monitoring and cleanup
     */
    stopMonitoring(sessionId: string): void;
}
//# sourceMappingURL=performance-optimizations.d.ts.map