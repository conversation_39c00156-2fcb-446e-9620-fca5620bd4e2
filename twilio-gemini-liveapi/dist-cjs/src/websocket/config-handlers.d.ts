import type { WebSocketDependencies, SessionConfig } from '../types/websocket';
export declare function getOutboundCallConfig(deps: WebSocketDependencies, callSid?: string): Promise<SessionConfig>;
export declare function getInboundCallConfig(deps: WebSocketDependencies, callSid?: string): Promise<SessionConfig>;
export declare function getOutboundTestConfig(deps: WebSocketDependencies): Promise<SessionConfig>;
export declare function getInboundTestConfig(deps: WebSocketDependencies): Promise<SessionConfig>;
//# sourceMappingURL=config-handlers.d.ts.map