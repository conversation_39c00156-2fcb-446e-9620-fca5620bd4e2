import type { ConnectionData, WebSocketDependencies, LocalAudioMessage, LocalTextMessage } from '../types/websocket';
import { LifecycleManager } from '../session/lifecycle-manager';
import { RecoveryManager } from '../session/recovery-manager';
export declare function handleAudioData(sessionId: string, data: LocalAudioMessage, geminiSession: any, isSessionActive: boolean, deps: WebSocketDependencies, activeConnections: Map<string, ConnectionData>, lifecycleManager: LifecycleManager, recoveryManager: RecoveryManager, flowType: string): Promise<void>;
export declare function handleTextMessage(sessionId: string, data: LocalTextMessage, geminiSession: any, isSessionActive: boolean, deps: WebSocketDependencies): Promise<void>;
export declare function handleTurnComplete(sessionId: string, geminiSession: any, isSessionActive: boolean, deps: WebSocketDependencies): Promise<void>;
//# sourceMappingURL=audio-handlers.d.ts.map