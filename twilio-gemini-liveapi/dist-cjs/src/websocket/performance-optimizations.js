"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionQualityMonitor = exports.FastConfigLoader = exports.SessionStartupOptimizer = void 0;
exports.createOptimizedConnectionHandler = createOptimizedConnectionHandler;
exports.parallelInitialization = parallelInitialization;
// WebSocket performance optimizations to reduce session startup time
const logger_1 = require("../utils/logger");
/**
 * Optimized session startup with performance tracking
 */
class SessionStartupOptimizer {
    metrics = new Map();
    config;
    constructor(config = {
        enableFastStart: true,
        skipNonEssentialChecks: false,
        preloadGeminiSession: false,
        parallelInitialization: true,
        timeoutMs: 10000 // 10 second timeout instead of 15+
    }) {
        this.config = config;
    }
    /**
     * Start tracking session startup performance
     */
    startTracking(sessionId) {
        this.metrics.set(sessionId, {
            connectionStart: Date.now(),
            authComplete: 0,
            configLoaded: 0,
            geminiSessionCreated: 0,
            sessionReady: 0,
            totalTime: 0
        });
    }
    /**
     * Mark authentication complete
     */
    markAuthComplete(sessionId) {
        const metrics = this.metrics.get(sessionId);
        if (metrics) {
            metrics.authComplete = Date.now();
        }
    }
    /**
     * Mark configuration loaded
     */
    markConfigLoaded(sessionId) {
        const metrics = this.metrics.get(sessionId);
        if (metrics) {
            metrics.configLoaded = Date.now();
        }
    }
    /**
     * Mark Gemini session created
     */
    markGeminiSessionCreated(sessionId) {
        const metrics = this.metrics.get(sessionId);
        if (metrics) {
            metrics.geminiSessionCreated = Date.now();
        }
    }
    /**
     * Mark session ready and calculate total time
     */
    markSessionReady(sessionId) {
        const metrics = this.metrics.get(sessionId);
        if (metrics) {
            metrics.sessionReady = Date.now();
            metrics.totalTime = metrics.sessionReady - metrics.connectionStart;
            // Log performance metrics
            this.logPerformanceMetrics(sessionId, metrics);
            return metrics;
        }
        return null;
    }
    /**
     * Get current metrics for a session
     */
    getMetrics(sessionId) {
        return this.metrics.get(sessionId) || null;
    }
    /**
     * Clean up metrics for completed session
     */
    cleanup(sessionId) {
        this.metrics.delete(sessionId);
    }
    /**
     * Log performance metrics with warnings for slow operations
     */
    logPerformanceMetrics(sessionId, metrics) {
        const authTime = metrics.authComplete - metrics.connectionStart;
        const configTime = metrics.configLoaded - metrics.authComplete;
        const geminiTime = metrics.geminiSessionCreated - metrics.configLoaded;
        const readyTime = metrics.sessionReady - metrics.geminiSessionCreated;
        logger_1.websocketLogger.info(`Session startup performance for ${sessionId}:`, {
            totalTime: metrics.totalTime,
            authTime,
            configTime,
            geminiTime,
            readyTime
        });
        // Warn about slow operations
        if (metrics.totalTime > 10000) {
            logger_1.websocketLogger.warn(`Slow session startup detected: ${metrics.totalTime}ms`, { sessionId });
        }
        if (authTime > 2000) {
            logger_1.websocketLogger.warn(`Slow authentication: ${authTime}ms`, { sessionId });
        }
        if (configTime > 1000) {
            logger_1.websocketLogger.warn(`Slow config loading: ${configTime}ms`, { sessionId });
        }
        if (geminiTime > 5000) {
            logger_1.websocketLogger.warn(`Slow Gemini session creation: ${geminiTime}ms`, { sessionId });
        }
    }
}
exports.SessionStartupOptimizer = SessionStartupOptimizer;
/**
 * Optimized WebSocket connection handler with timeout protection
 */
function createOptimizedConnectionHandler(originalHandler, optimizer) {
    return async function (connection, deps) {
        const sessionId = deps.sessionId || connection.sessionId || `session_${Date.now()}`;
        // Start performance tracking
        optimizer.startTracking(sessionId);
        // Set up timeout protection
        const timeoutId = setTimeout(() => {
            logger_1.websocketLogger.error(`Session startup timeout after ${optimizer['config'].timeoutMs}ms`, { sessionId });
            if (connection.socket && connection.socket.readyState === 1) {
                connection.socket.send(JSON.stringify({
                    type: 'session-error',
                    error: 'Session startup timeout. Please try again.',
                    timeout: true
                }));
                connection.socket.close();
            }
        }, optimizer['config'].timeoutMs);
        try {
            // Call original handler with optimizations
            await originalHandler(connection, {
                ...deps,
                optimizer,
                sessionId
            });
            // Clear timeout if successful
            clearTimeout(timeoutId);
        }
        catch (error) {
            clearTimeout(timeoutId);
            logger_1.websocketLogger.error(`Optimized connection handler error`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    };
}
/**
 * Parallel initialization helper for faster startup
 */
async function parallelInitialization(tasks, timeoutMs = 5000) {
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Parallel initialization timeout')), timeoutMs);
    });
    try {
        const results = await Promise.race([
            Promise.all(tasks.map(task => task())),
            timeoutPromise
        ]);
        return results;
    }
    catch (error) {
        logger_1.websocketLogger.error('Parallel initialization failed', error instanceof Error ? error : new Error(String(error)));
        throw error;
    }
}
/**
 * Fast session configuration loader with caching
 */
class FastConfigLoader {
    configCache = new Map();
    cacheTimeout = 30000; // 30 seconds
    /**
     * Load configuration with caching for better performance
     */
    async loadConfig(sessionId, configLoader, useCache = true) {
        const cacheKey = `config_${sessionId}`;
        if (useCache && this.configCache.has(cacheKey)) {
            const cached = this.configCache.get(cacheKey);
            if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
                logger_1.websocketLogger.debug(`Using cached config for ${sessionId}`);
                return cached.config;
            }
        }
        try {
            const config = await configLoader(sessionId);
            if (useCache) {
                this.configCache.set(cacheKey, {
                    config,
                    timestamp: Date.now()
                });
            }
            return config;
        }
        catch (error) {
            logger_1.websocketLogger.error(`Config loading failed for ${sessionId}`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    /**
     * Clear expired cache entries
     */
    clearExpiredCache() {
        const now = Date.now();
        for (const [key, value] of this.configCache.entries()) {
            if ((now - value.timestamp) > this.cacheTimeout) {
                this.configCache.delete(key);
            }
        }
    }
    /**
     * Clear all cache
     */
    clearCache() {
        this.configCache.clear();
    }
}
exports.FastConfigLoader = FastConfigLoader;
/**
 * Connection quality monitor for early detection of issues
 */
class ConnectionQualityMonitor {
    qualityMetrics = new Map();
    /**
     * Start monitoring connection quality
     */
    startMonitoring(sessionId, ws) {
        const metrics = {
            startTime: Date.now(),
            messageCount: 0,
            errorCount: 0,
            lastActivity: Date.now(),
            quality: 'good'
        };
        this.qualityMetrics.set(sessionId, metrics);
        // Monitor WebSocket events
        if (ws) {
            ws.on('message', () => {
                metrics.messageCount++;
                metrics.lastActivity = Date.now();
            });
            ws.on('error', () => {
                metrics.errorCount++;
                this.updateQuality(sessionId);
            });
        }
    }
    /**
     * Update connection quality based on metrics
     */
    updateQuality(sessionId) {
        const metrics = this.qualityMetrics.get(sessionId);
        if (!metrics) {
            return;
        }
        const now = Date.now();
        const timeSinceStart = now - metrics.startTime;
        const timeSinceActivity = now - metrics.lastActivity;
        if (metrics.errorCount > 3 || timeSinceActivity > 30000) {
            metrics.quality = 'poor';
        }
        else if (metrics.errorCount > 1 || timeSinceActivity > 15000) {
            metrics.quality = 'fair';
        }
        else {
            metrics.quality = 'good';
        }
    }
    /**
     * Get current connection quality
     */
    getQuality(sessionId) {
        const metrics = this.qualityMetrics.get(sessionId);
        if (metrics) {
            this.updateQuality(sessionId);
            return metrics.quality;
        }
        return 'unknown';
    }
    /**
     * Stop monitoring and cleanup
     */
    stopMonitoring(sessionId) {
        this.qualityMetrics.delete(sessionId);
    }
}
exports.ConnectionQualityMonitor = ConnectionQualityMonitor;
//# sourceMappingURL=performance-optimizations.js.map