import { z } from 'zod';
/**
 * Twilio Media Stream - Start message schema
 */
export declare const TwilioStartMessageSchema: z.ZodObject<{
    event: z.ZodLiteral<"start">;
    start: z.ZodObject<{
        streamSid: z.ZodString;
        accountSid: z.ZodString;
        callSid: z.ZodString;
        tracks: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        customParameters: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    }, "strip", z.ZodTypeAny, {
        callSid: string;
        streamSid: string;
        accountSid: string;
        tracks?: string[] | undefined;
        customParameters?: Record<string, any> | undefined;
    }, {
        callSid: string;
        streamSid: string;
        accountSid: string;
        tracks?: string[] | undefined;
        customParameters?: Record<string, any> | undefined;
    }>;
    streamSid: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    start: {
        callSid: string;
        streamSid: string;
        accountSid: string;
        tracks?: string[] | undefined;
        customParameters?: Record<string, any> | undefined;
    };
    event: "start";
    streamSid?: string | undefined;
}, {
    start: {
        callSid: string;
        streamSid: string;
        accountSid: string;
        tracks?: string[] | undefined;
        customParameters?: Record<string, any> | undefined;
    };
    event: "start";
    streamSid?: string | undefined;
}>;
/**
 * Twilio Media Stream - Media message schema
 */
export declare const TwilioMediaMessageSchema: z.ZodObject<{
    event: z.ZodLiteral<"media">;
    media: z.ZodObject<{
        payload: z.ZodString;
        chunk: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodOptional<z.ZodString>;
        track: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        payload: string;
        timestamp?: string | undefined;
        chunk?: string | undefined;
        track?: string | undefined;
    }, {
        payload: string;
        timestamp?: string | undefined;
        chunk?: string | undefined;
        track?: string | undefined;
    }>;
    streamSid: z.ZodOptional<z.ZodString>;
    sequenceNumber: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    media: {
        payload: string;
        timestamp?: string | undefined;
        chunk?: string | undefined;
        track?: string | undefined;
    };
    event: "media";
    sequenceNumber?: string | undefined;
    streamSid?: string | undefined;
}, {
    media: {
        payload: string;
        timestamp?: string | undefined;
        chunk?: string | undefined;
        track?: string | undefined;
    };
    event: "media";
    sequenceNumber?: string | undefined;
    streamSid?: string | undefined;
}>;
/**
 * Twilio Media Stream - Stop message schema
 */
export declare const TwilioStopMessageSchema: z.ZodObject<{
    event: z.ZodLiteral<"stop">;
    streamSid: z.ZodOptional<z.ZodString>;
    stop: z.ZodOptional<z.ZodObject<{
        accountSid: z.ZodOptional<z.ZodString>;
        callSid: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        callSid?: string | undefined;
        accountSid?: string | undefined;
    }, {
        callSid?: string | undefined;
        accountSid?: string | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    event: "stop";
    stop?: {
        callSid?: string | undefined;
        accountSid?: string | undefined;
    } | undefined;
    streamSid?: string | undefined;
}, {
    event: "stop";
    stop?: {
        callSid?: string | undefined;
        accountSid?: string | undefined;
    } | undefined;
    streamSid?: string | undefined;
}>;
/**
 * Twilio Media Stream - Mark message schema
 */
export declare const TwilioMarkMessageSchema: z.ZodObject<{
    event: z.ZodLiteral<"mark">;
    streamSid: z.ZodOptional<z.ZodString>;
    mark: z.ZodOptional<z.ZodObject<{
        name: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        name: string;
    }, {
        name: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    event: "mark";
    mark?: {
        name: string;
    } | undefined;
    streamSid?: string | undefined;
}, {
    event: "mark";
    mark?: {
        name: string;
    } | undefined;
    streamSid?: string | undefined;
}>;
/**
 * Twilio Media Stream - Connected message schema
 */
export declare const TwilioConnectedMessageSchema: z.ZodObject<{
    event: z.ZodLiteral<"connected">;
    protocol: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    event: "connected";
    version?: string | undefined;
    protocol?: string | undefined;
}, {
    event: "connected";
    version?: string | undefined;
    protocol?: string | undefined;
}>;
/**
 * Union of all Twilio message types
 */
export declare const TwilioMessageSchema: z.ZodDiscriminatedUnion<"event", [z.ZodObject<{
    event: z.ZodLiteral<"start">;
    start: z.ZodObject<{
        streamSid: z.ZodString;
        accountSid: z.ZodString;
        callSid: z.ZodString;
        tracks: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        customParameters: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    }, "strip", z.ZodTypeAny, {
        callSid: string;
        streamSid: string;
        accountSid: string;
        tracks?: string[] | undefined;
        customParameters?: Record<string, any> | undefined;
    }, {
        callSid: string;
        streamSid: string;
        accountSid: string;
        tracks?: string[] | undefined;
        customParameters?: Record<string, any> | undefined;
    }>;
    streamSid: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    start: {
        callSid: string;
        streamSid: string;
        accountSid: string;
        tracks?: string[] | undefined;
        customParameters?: Record<string, any> | undefined;
    };
    event: "start";
    streamSid?: string | undefined;
}, {
    start: {
        callSid: string;
        streamSid: string;
        accountSid: string;
        tracks?: string[] | undefined;
        customParameters?: Record<string, any> | undefined;
    };
    event: "start";
    streamSid?: string | undefined;
}>, z.ZodObject<{
    event: z.ZodLiteral<"media">;
    media: z.ZodObject<{
        payload: z.ZodString;
        chunk: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodOptional<z.ZodString>;
        track: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        payload: string;
        timestamp?: string | undefined;
        chunk?: string | undefined;
        track?: string | undefined;
    }, {
        payload: string;
        timestamp?: string | undefined;
        chunk?: string | undefined;
        track?: string | undefined;
    }>;
    streamSid: z.ZodOptional<z.ZodString>;
    sequenceNumber: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    media: {
        payload: string;
        timestamp?: string | undefined;
        chunk?: string | undefined;
        track?: string | undefined;
    };
    event: "media";
    sequenceNumber?: string | undefined;
    streamSid?: string | undefined;
}, {
    media: {
        payload: string;
        timestamp?: string | undefined;
        chunk?: string | undefined;
        track?: string | undefined;
    };
    event: "media";
    sequenceNumber?: string | undefined;
    streamSid?: string | undefined;
}>, z.ZodObject<{
    event: z.ZodLiteral<"stop">;
    streamSid: z.ZodOptional<z.ZodString>;
    stop: z.ZodOptional<z.ZodObject<{
        accountSid: z.ZodOptional<z.ZodString>;
        callSid: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        callSid?: string | undefined;
        accountSid?: string | undefined;
    }, {
        callSid?: string | undefined;
        accountSid?: string | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    event: "stop";
    stop?: {
        callSid?: string | undefined;
        accountSid?: string | undefined;
    } | undefined;
    streamSid?: string | undefined;
}, {
    event: "stop";
    stop?: {
        callSid?: string | undefined;
        accountSid?: string | undefined;
    } | undefined;
    streamSid?: string | undefined;
}>, z.ZodObject<{
    event: z.ZodLiteral<"mark">;
    streamSid: z.ZodOptional<z.ZodString>;
    mark: z.ZodOptional<z.ZodObject<{
        name: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        name: string;
    }, {
        name: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    event: "mark";
    mark?: {
        name: string;
    } | undefined;
    streamSid?: string | undefined;
}, {
    event: "mark";
    mark?: {
        name: string;
    } | undefined;
    streamSid?: string | undefined;
}>, z.ZodObject<{
    event: z.ZodLiteral<"connected">;
    protocol: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    event: "connected";
    version?: string | undefined;
    protocol?: string | undefined;
}, {
    event: "connected";
    version?: string | undefined;
    protocol?: string | undefined;
}>]>;
/**
 * Local browser - Start session message schema
 */
export declare const LocalStartMessageSchema: z.ZodObject<{
    type: z.ZodEnum<["start-session", "start_session"]>;
    aiInstructions: z.ZodOptional<z.ZodString>;
    voice: z.ZodOptional<z.ZodString>;
    model: z.ZodOptional<z.ZodString>;
    scriptId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "start-session" | "start_session";
    voice?: string | undefined;
    model?: string | undefined;
    aiInstructions?: string | undefined;
    scriptId?: string | undefined;
}, {
    type: "start-session" | "start_session";
    voice?: string | undefined;
    model?: string | undefined;
    aiInstructions?: string | undefined;
    scriptId?: string | undefined;
}>;
/**
 * Local browser - Audio data message schema
 */
export declare const LocalAudioMessageSchema: z.ZodObject<{
    type: z.ZodEnum<["audio-data", "audio"]>;
    audio: z.ZodOptional<z.ZodString>;
    audioData: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "audio-data" | "audio";
    audio?: string | undefined;
    audioData?: string | undefined;
}, {
    type: "audio-data" | "audio";
    audio?: string | undefined;
    audioData?: string | undefined;
}>;
/**
 * Local browser - Text message schema
 */
export declare const LocalTextMessageSchema: z.ZodObject<{
    type: z.ZodLiteral<"text-message">;
    text: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "text-message";
    text: string;
}, {
    type: "text-message";
    text: string;
}>;
/**
 * Local browser - End session message schema
 */
export declare const LocalEndSessionMessageSchema: z.ZodObject<{
    type: z.ZodEnum<["end-session", "end_session"]>;
}, "strip", z.ZodTypeAny, {
    type: "end-session" | "end_session";
}, {
    type: "end-session" | "end_session";
}>;
/**
 * Local browser - Heartbeat message schema
 */
export declare const LocalHeartbeatMessageSchema: z.ZodObject<{
    type: z.ZodLiteral<"heartbeat">;
}, "strip", z.ZodTypeAny, {
    type: "heartbeat";
}, {
    type: "heartbeat";
}>;
/**
 * Local browser - Turn complete message schema
 */
export declare const LocalTurnCompleteMessageSchema: z.ZodObject<{
    type: z.ZodLiteral<"turn-complete">;
}, "strip", z.ZodTypeAny, {
    type: "turn-complete";
}, {
    type: "turn-complete";
}>;
/**
 * Local browser - Request summary message schema
 */
export declare const LocalRequestSummaryMessageSchema: z.ZodObject<{
    type: z.ZodLiteral<"request-summary">;
}, "strip", z.ZodTypeAny, {
    type: "request-summary";
}, {
    type: "request-summary";
}>;
/**
 * Local browser - Audio response message schema (from server)
 */
export declare const LocalAudioResponseMessageSchema: z.ZodObject<{
    type: z.ZodLiteral<"audio-response">;
    audio: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "audio-response";
    audio?: string | undefined;
}, {
    type: "audio-response";
    audio?: string | undefined;
}>;
/**
 * Union of all local browser message types
 */
export declare const LocalMessageSchema: z.ZodDiscriminatedUnion<"type", [z.ZodObject<{
    type: z.ZodEnum<["start-session", "start_session"]>;
    aiInstructions: z.ZodOptional<z.ZodString>;
    voice: z.ZodOptional<z.ZodString>;
    model: z.ZodOptional<z.ZodString>;
    scriptId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "start-session" | "start_session";
    voice?: string | undefined;
    model?: string | undefined;
    aiInstructions?: string | undefined;
    scriptId?: string | undefined;
}, {
    type: "start-session" | "start_session";
    voice?: string | undefined;
    model?: string | undefined;
    aiInstructions?: string | undefined;
    scriptId?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodEnum<["audio-data", "audio"]>;
    audio: z.ZodOptional<z.ZodString>;
    audioData: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "audio-data" | "audio";
    audio?: string | undefined;
    audioData?: string | undefined;
}, {
    type: "audio-data" | "audio";
    audio?: string | undefined;
    audioData?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"text-message">;
    text: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "text-message";
    text: string;
}, {
    type: "text-message";
    text: string;
}>, z.ZodObject<{
    type: z.ZodEnum<["end-session", "end_session"]>;
}, "strip", z.ZodTypeAny, {
    type: "end-session" | "end_session";
}, {
    type: "end-session" | "end_session";
}>, z.ZodObject<{
    type: z.ZodLiteral<"heartbeat">;
}, "strip", z.ZodTypeAny, {
    type: "heartbeat";
}, {
    type: "heartbeat";
}>, z.ZodObject<{
    type: z.ZodLiteral<"turn-complete">;
}, "strip", z.ZodTypeAny, {
    type: "turn-complete";
}, {
    type: "turn-complete";
}>, z.ZodObject<{
    type: z.ZodLiteral<"request-summary">;
}, "strip", z.ZodTypeAny, {
    type: "request-summary";
}, {
    type: "request-summary";
}>, z.ZodObject<{
    type: z.ZodLiteral<"audio-response">;
    audio: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "audio-response";
    audio?: string | undefined;
}, {
    type: "audio-response";
    audio?: string | undefined;
}>]>;
export declare const SessionConfigSchema: z.ZodObject<{
    aiInstructions: z.ZodString;
    voice: z.ZodEnum<["Aoede", "Charon", "Fenrir", "Kore", "Puck", "Leda", "Orus", "Zephyr"]>;
    model: z.ZodEnum<["gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro", "gemini-2.5-flash-preview-native-audio-dialog", "gemini-2.0-flash-live-001", "gemini-2.0-flash", "gemini-2.0-flash-live-preview-04-09"]>;
    targetName: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    targetPhoneNumber: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    scriptType: z.ZodEnum<["incoming", "outbound"]>;
    scriptId: z.ZodString;
    isIncomingCall: z.ZodOptional<z.ZodBoolean>;
    isTestMode: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    voice: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr";
    model: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09";
    aiInstructions: string;
    scriptType: "incoming" | "outbound";
    scriptId: string;
    isIncomingCall?: boolean | undefined;
    targetName?: string | null | undefined;
    targetPhoneNumber?: string | null | undefined;
    isTestMode?: boolean | undefined;
}, {
    voice: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr";
    model: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09";
    aiInstructions: string;
    scriptType: "incoming" | "outbound";
    scriptId: string;
    isIncomingCall?: boolean | undefined;
    targetName?: string | null | undefined;
    targetPhoneNumber?: string | null | undefined;
    isTestMode?: boolean | undefined;
}>;
/**
 * Validates a Twilio message and returns parsed data or error
 */
export declare function validateTwilioMessage(data: unknown): {
    success: boolean;
    data?: z.infer<typeof TwilioMessageSchema>;
    error?: z.ZodError;
};
/**
 * Validates a local browser message and returns parsed data or error
 */
export declare function validateLocalMessage(data: unknown): {
    success: boolean;
    data?: z.infer<typeof LocalMessageSchema>;
    error?: z.ZodError;
};
/**
 * Validates session configuration and returns parsed data or error
 */
export declare function validateSessionConfig(data: unknown): {
    success: boolean;
    data?: z.infer<typeof SessionConfigSchema>;
    error?: z.ZodError;
};
/**
 * Generic validation function for any schema
 */
export declare function validateWithSchema<T>(schema: z.ZodSchema<T>, data: unknown): {
    success: boolean;
    data?: T;
    error?: z.ZodError;
};
/**
 * Formats Zod validation errors into a readable string
 */
export declare function formatValidationError(error: z.ZodError): string;
export type TwilioStartMessage = z.infer<typeof TwilioStartMessageSchema>;
export type TwilioMediaMessage = z.infer<typeof TwilioMediaMessageSchema>;
export type TwilioStopMessage = z.infer<typeof TwilioStopMessageSchema>;
export type TwilioMarkMessage = z.infer<typeof TwilioMarkMessageSchema>;
export type TwilioConnectedMessage = z.infer<typeof TwilioConnectedMessageSchema>;
export type TwilioMessage = z.infer<typeof TwilioMessageSchema>;
export type LocalStartMessage = z.infer<typeof LocalStartMessageSchema>;
export type LocalAudioMessage = z.infer<typeof LocalAudioMessageSchema>;
export type LocalTextMessage = z.infer<typeof LocalTextMessageSchema>;
export type LocalEndSessionMessage = z.infer<typeof LocalEndSessionMessageSchema>;
export type LocalHeartbeatMessage = z.infer<typeof LocalHeartbeatMessageSchema>;
export type LocalTurnCompleteMessage = z.infer<typeof LocalTurnCompleteMessageSchema>;
export type LocalRequestSummaryMessage = z.infer<typeof LocalRequestSummaryMessageSchema>;
export type LocalAudioResponseMessage = z.infer<typeof LocalAudioResponseMessageSchema>;
export type LocalMessage = z.infer<typeof LocalMessageSchema>;
export type SessionConfig = z.infer<typeof SessionConfigSchema>;
//# sourceMappingURL=message-schemas.d.ts.map