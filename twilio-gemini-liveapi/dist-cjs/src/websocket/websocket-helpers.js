"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateWebSocketAuth = validateWebSocketAuth;
exports.handleAuthFailure = handleAuthFailure;
exports.createEnhancedDependencies = createEnhancedDependencies;
exports.registerWebSocketEndpoint = registerWebSocketEndpoint;
exports.registerMultipleWebSocketEndpoints = registerMultipleWebSocketEndpoints;
exports.createStandardEndpointConfigs = createStandardEndpointConfigs;
exports.logWebSocketConnection = logWebSocketConnection;
exports.createFlowDependencies = createFlowDependencies;
const logger_1 = require("../utils/logger");
const crypto_1 = require("crypto");
/**
 * Validates WebSocket authentication
 */
function validateWebSocketAuth(req) {
    // Skip auth for Twilio webhooks (they use signature validation)
    const isTwilioWebhook = req.headers['user-agent']?.includes('TwilioProxy') ||
        req.headers['x-twilio-signature'];
    if (isTwilioWebhook) {
        logger_1.authLogger.info('WebSocket: Allowing Twilio webhook connection');
        return true;
    }
    // For non-Twilio connections, require authentication in production only when forced
    if (process.env.NODE_ENV === 'production' && process.env.FORCE_AUTH === 'true') {
        const authHeader = req.headers.authorization;
        if (!authHeader) {
            logger_1.authLogger.error('WebSocket: No authorization header in production', {
                url: req.url,
                ip: req.socket?.remoteAddress
            });
            return false;
        }
        try {
            const token = authHeader.replace('Bearer ', '');
            if (!token || token === 'undefined' || token === 'null') {
                logger_1.authLogger.error('WebSocket: Invalid token format');
                return false;
            }
            // Validate token format
            if (token.length < 32) {
                logger_1.authLogger.error('WebSocket: Token too short');
                return false;
            }
            // Check against configured API key
            const validApiKey = process.env.API_KEY || process.env.SUPABASE_SERVICE_KEY || process.env.GEMINI_API_KEY || process.env.TWILIO_AUTH_TOKEN;
            if (!validApiKey) {
                logger_1.authLogger.error('WebSocket: No API key configured for production');
                return false;
            }
            // Ensure both tokens are the same length to prevent timing attacks
            if (token.length !== validApiKey.length) {
                logger_1.authLogger.error('WebSocket: Invalid API key length');
                return false;
            }
            // Use timing-safe comparison
            if (!(0, crypto_1.timingSafeEqual)(Buffer.from(token), Buffer.from(validApiKey))) {
                logger_1.authLogger.error('WebSocket: Invalid API key');
                return false;
            }
            logger_1.authLogger.info('WebSocket: Authentication successful');
            return true;
        }
        catch (error) {
            logger_1.authLogger.error('WebSocket: Auth validation failed', error instanceof Error ? error : new Error(String(error)));
            return false;
        }
    }
    // Allow in development and test environments with warning
    if (process.env.NODE_ENV === 'test') {
        logger_1.authLogger.debug('WebSocket: No authentication required for test environment');
    }
    else {
        logger_1.authLogger.warn('WebSocket: No authentication required for development environment');
    }
    return true;
}
/**
 * Handles WebSocket authentication failure
 */
function handleAuthFailure(connection, endpoint) {
    logger_1.authLogger.error(`WebSocket: Authentication failed for ${endpoint}`);
    connection.socket.close(1008, 'Authentication required');
}
/**
 * Creates enhanced dependencies for WebSocket handlers
 */
function createEnhancedDependencies(baseDependencies, callType, isTestMode) {
    return {
        ...baseDependencies,
        callType,
        isTestMode
    };
}
/**
 * Registers a WebSocket endpoint with authentication and error handling
 */
function registerWebSocketEndpoint(fastify, config, baseDependencies) {
    fastify.register(async (fastify) => {
        fastify.get(config.path, { websocket: true }, (connection, req) => {
            // Log connection if specified
            if (config.logMessage) {
                logger_1.websocketLogger.info(config.logMessage);
                logger_1.websocketLogger.debug('WebSocket headers', { headers: req.headers });
            }
            // CRITICAL FIX: Extract query parameters from URL
            const url = new URL(req.url, `http://${req.headers.host}`);
            const queryParams = Object.fromEntries(url.searchParams.entries());
            const callSid = queryParams.CallSid || queryParams.callSid;
            if (callSid) {
                logger_1.websocketLogger.info('CallSid extracted from WebSocket URL', { callSid, url: req.url });
            }
            else {
                logger_1.websocketLogger.warn('No CallSid found in WebSocket URL', { url: req.url, queryParams });
            }
            // Validate authentication
            if (!validateWebSocketAuth(req)) {
                handleAuthFailure(connection, config.path);
                return;
            }
            // Create enhanced dependencies with query parameters
            const enhancedDeps = createEnhancedDependencies(baseDependencies, config.callType, config.isTestMode);
            // Attach query parameters and CallSid to connection for handler access
            connection.queryParams = queryParams;
            connection.callSid = callSid;
            // Call the handler
            config.handler(connection, enhancedDeps);
        });
    });
}
/**
 * Registers multiple WebSocket endpoints efficiently
 */
function registerMultipleWebSocketEndpoints(fastify, configs, baseDependencies) {
    configs.forEach(config => {
        registerWebSocketEndpoint(fastify, config, baseDependencies);
    });
}
/**
 * Creates standard WebSocket endpoint configurations
 */
function createStandardEndpointConfigs(handlers) {
    return [
        {
            path: '/media-stream',
            handler: handlers.handleOutboundCall,
            callType: 'outbound',
            isTestMode: false,
            logMessage: '🔌 WebSocket connection received on /media-stream'
        },
        {
            path: '/media-stream-inbound',
            handler: handlers.handleInboundCall,
            callType: 'inbound',
            isTestMode: false,
            logMessage: '🔌 WebSocket connection received on /media-stream-inbound'
        },
        {
            path: '/test-outbound',
            handler: handlers.handleOutboundTesting,
            callType: 'outbound',
            isTestMode: true
        },
        {
            path: '/test-inbound',
            handler: handlers.handleInboundTesting,
            callType: 'inbound',
            isTestMode: true
        },
        {
            path: '/local-audio-session',
            handler: handlers.handleOutboundTesting,
            callType: 'outbound',
            isTestMode: true
        },
        {
            path: '/gemini-live',
            handler: handlers.handleOutboundTesting,
            callType: 'outbound',
            isTestMode: true,
            logMessage: '🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection'
        }
    ];
}
/**
 * Logs WebSocket connection details
 */
function logWebSocketConnection(callType, isTestMode, additionalInfo) {
    const mode = isTestMode ? 'TEST' : 'CALL';
    const type = callType.toUpperCase();
    const info = additionalInfo ? ` - ${additionalInfo}` : '';
    logger_1.websocketLogger.info(`Client connected ${type} ${mode}${info}`);
}
/**
 * Creates flow dependencies with proper typing
 */
function createFlowDependencies(baseDeps, overrides) {
    return {
        ...baseDeps,
        ...overrides
    };
}
//# sourceMappingURL=websocket-helpers.js.map