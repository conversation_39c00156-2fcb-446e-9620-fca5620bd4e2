"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleAudioData = handleAudioData;
exports.handleTextMessage = handleTextMessage;
exports.handleTurnComplete = handleTurnComplete;
const logger_1 = require("../utils/logger");
async function handleAudioData(sessionId, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager, flowType) {
    if (geminiSession && isSessionActive && (data.audio || data.audioData)) {
        try {
            // Update activity by transitioning to active state if not already
            lifecycleManager.transitionState(sessionId, 'active', 'audio_received');
            // Standardize on 'audio' field name - prefer 'audio' over legacy 'audioData'
            const base64Audio = data.audio || data.audioData || '';
            await deps.sessionManager.sendBrowserAudioToGemini(sessionId, geminiSession, base64Audio);
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && connectionData.deepgramConnection && deps.transcriptionManager) {
                try {
                    const audioBuffer = Buffer.from(base64Audio, 'base64');
                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                }
                catch (transcriptionError) {
                    logger_1.sessionLogger.error('Error sending audio to transcription', transcriptionError, sessionId);
                }
            }
        }
        catch (error) {
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                await recoveryManager.recoverSession(sessionId, 'audio_processing_error', activeConnections);
            }
        }
    }
}
async function handleTextMessage(sessionId, data, geminiSession, isSessionActive, deps) {
    if (geminiSession && isSessionActive && data.text) {
        try {
            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
        }
        catch (textError) {
            logger_1.sessionLogger.error('Error sending text to Gemini', textError, sessionId);
        }
    }
}
async function handleTurnComplete(sessionId, geminiSession, isSessionActive, deps) {
    if (geminiSession && isSessionActive) {
        try {
            await deps.sessionManager.sendTurnComplete(sessionId, geminiSession);
        }
        catch (turnCompleteError) {
            logger_1.sessionLogger.error('Error sending turn complete to Gemini', turnCompleteError, sessionId);
        }
    }
}
//# sourceMappingURL=audio-handlers.js.map