import { CampaignScript, ScriptInfo } from './campaign-types';
export declare function loadCampai<PERSON>Script(campaignId: number, type?: 'incoming' | 'outbound', useCache?: boolean): CampaignScript | null;
export declare function getAllCampaigns(): CampaignScript[];
export declare function formatCampaignScript(script: CampaignScript): string;
export declare function getIncoming<PERSON>allScript(scriptId: string): CampaignScript | null;
export declare function listIncomingCallScripts(): ScriptInfo[];
export declare function setIncomingCallScript(scriptId: string): boolean;
export declare function getCurrentIncomingScript(): string;
export declare function getCurrentIncomingScriptId(): number;
export declare function createCustomIncomingScript(scriptData: any): boolean;
export declare function getIncomingScenario(scenarioId: string): CampaignScript | null;
export declare function listIncomingScenarios(): ScriptInfo[];
export declare function setActiveIncomingScenario(scenarioId: string): boolean;
export declare function getCurrentIncomingScenario(): string;
//# sourceMappingURL=campaign-loader.d.ts.map