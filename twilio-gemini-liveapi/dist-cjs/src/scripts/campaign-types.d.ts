export interface AgentPersona {
    name: string;
    voice?: string;
    model?: string;
    tone?: string;
    humanEmulation?: boolean;
}
export interface ScriptStep {
    type: 'statement' | 'question' | 'response';
    content: string;
}
export interface CampaignScript {
    id: number | string;
    type: 'incoming' | 'outbound';
    language: string;
    category: string;
    title: string;
    campaign: string;
    campaign_title?: string;
    agentPersona?: AgentPersona;
    objectives?: string[];
    keyPoints?: string[];
    script?: {
        start?: ScriptStep[];
        [key: string]: ScriptStep[] | undefined;
    };
}
export interface ScriptConfig {
    aiInstructions: string;
    voice: string;
    model: string;
    isIncomingCall: boolean;
    scriptType: 'incoming' | 'outbound';
    scriptId: string | number;
    campaignId: number;
}
export interface ScriptInfo {
    id: string;
    name: string;
    description: string;
    type: 'incoming' | 'outbound';
    category?: string;
}
export interface CustomScriptData {
    id: string;
    name: string;
    description: string;
    instructions?: string;
    voice?: string;
    model?: string;
}
//# sourceMappingURL=campaign-types.d.ts.map