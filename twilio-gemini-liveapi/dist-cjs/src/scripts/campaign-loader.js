"use strict";
// Enhanced Campaign Script Loader
// Provides robust loading, caching, and validation of campaign scripts
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadCampaignScript = loadCampaignScript;
exports.getAllCampaigns = getAllCampaigns;
exports.formatCampaignScript = formatCampaignScript;
exports.getIncomingCallScript = getIncomingCallScript;
exports.listIncomingCallScripts = listIncomingCallScripts;
exports.setIncomingCallScript = setIncomingCallScript;
exports.getCurrentIncomingScript = getCurrentIncomingScript;
exports.getCurrentIncomingScriptId = getCurrentIncomingScriptId;
exports.createCustomIncomingScript = createCustomIncomingScript;
exports.getIncomingScenario = getIncomingScenario;
exports.listIncomingScenarios = listIncomingScenarios;
exports.setActiveIncomingScenario = setActiveIncomingScenario;
exports.getCurrentIncomingScenario = getCurrentIncomingScenario;
const logger_1 = require("../utils/logger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
// Script cache for performance optimization
const scriptCache = new Map();
const CACHE_TIMEOUT = 5 * 60 * 1000; // 5 minutes
// Default campaign scripts for fallback
const DEFAULT_CAMPAIGNS = [
    {
        id: 1,
        type: 'outbound',
        language: 'en',
        category: 'sales',
        title: 'Default Outbound Campaign',
        campaign: 'Sales Campaign',
        agentPersona: {
            name: 'Sales Agent',
            voice: 'Kore',
            model: 'gemini-2.5-flash-preview-native-audio-dialog',
            tone: 'professional',
            humanEmulation: true
        }
    },
    {
        id: 1,
        type: 'incoming',
        language: 'en',
        category: 'support',
        title: 'Default Incoming Campaign',
        campaign: 'Customer Support',
        agentPersona: {
            name: 'Support Agent',
            voice: 'Kore',
            model: 'gemini-2.5-flash-preview-native-audio-dialog',
            tone: 'helpful',
            humanEmulation: true
        }
    }
];
function loadCampaignScript(campaignId, type = 'outbound', useCache = true) {
    try {
        // PERMANENT FIX: Use correct file paths for campaign scripts
        let scriptPath;
        if (type === 'incoming') {
            scriptPath = path.join(process.cwd(), 'call-center-frontend', 'public', `incoming-campaign${campaignId}.json`);
        }
        else {
            scriptPath = path.join(process.cwd(), 'call-center-frontend', 'public', `campaign${campaignId}.json`);
        }
        logger_1.logger.info(`🔍 [SCRIPT-LOADER] Attempting to load: ${scriptPath}`);
        logger_1.logger.info(`🔍 [SCRIPT-LOADER] File exists: ${fs.existsSync(scriptPath)}`);
        if (fs.existsSync(scriptPath)) {
            try {
                const scriptContent = fs.readFileSync(scriptPath, 'utf-8');
                const script = JSON.parse(scriptContent);
                logger_1.logger.info(`✅ [SCRIPT-LOADER] Successfully loaded campaign script from: ${scriptPath}`);
                return script;
            }
            catch (fileError) {
                logger_1.logger.error(`❌ [SCRIPT-LOADER] Error reading/parsing file ${scriptPath}:`, fileError instanceof Error ? fileError : new Error(String(fileError)));
                return DEFAULT_CAMPAIGNS.find(c => c.type === type) || null;
            }
        }
        // Fallback to default
        logger_1.logger.warn(`⚠️ [SCRIPT-LOADER] Campaign script not found: ${scriptPath}, using default`);
        return DEFAULT_CAMPAIGNS.find(c => c.type === type) || null;
    }
    catch (error) {
        logger_1.logger.error('❌ [SCRIPT-LOADER] Error loading campaign script:', error instanceof Error ? error : new Error(String(error)));
        return DEFAULT_CAMPAIGNS.find(c => c.type === type) || null;
    }
}
function getAllCampaigns() {
    const campaigns = [];
    try {
        // Load all campaigns from 1-6 for both types
        for (let i = 1; i <= 6; i++) {
            const outbound = loadCampaignScript(i, 'outbound');
            if (outbound) {
                campaigns.push(outbound);
            }
            const incoming = loadCampaignScript(i, 'incoming');
            if (incoming) {
                campaigns.push(incoming);
            }
        }
    }
    catch (error) {
        logger_1.logger.error('Error loading all campaigns:', error instanceof Error ? error : new Error(String(error)));
    }
    return campaigns.length > 0 ? campaigns : DEFAULT_CAMPAIGNS;
}
function formatCampaignScript(script) {
    if (!script) {
        return '';
    }
    let formatted = '';
    if (script.title) {
        formatted += `Campaign: ${script.title}\n`;
    }
    if (script.campaign) {
        formatted += `\n${script.campaign}\n`;
    }
    if (script.objectives) {
        formatted += `\nObjectives:\n${script.objectives.join('\n')}\n`;
    }
    return formatted;
}
// Placeholder functions for compatibility
function getIncomingCallScript(scriptId) {
    const id = parseInt(scriptId) || 1;
    return loadCampaignScript(id, 'incoming');
}
function listIncomingCallScripts() {
    const scripts = [];
    for (let i = 1; i <= 6; i++) {
        scripts.push({
            id: `incoming-${i}`,
            name: `Incoming Campaign ${i}`,
            description: `Customer support campaign ${i}`,
            type: 'incoming',
            category: 'support'
        });
    }
    return scripts;
}
function setIncomingCallScript(scriptId) {
    const id = parseInt(scriptId.replace(/\D/g, '')) || 1;
    if (id < 1 || id > 6) {
        logger_1.logger.error(`❌ [SCRIPT-LOADER] Invalid script ID for setting: ${scriptId}`);
        return false;
    }
    // Validate that the script exists before setting it
    const script = loadCampaignScript(id, 'incoming');
    if (!script) {
        logger_1.logger.error(`❌ [SCRIPT-LOADER] Script ${scriptId} not found, cannot set as current`);
        return false;
    }
    logger_1.logger.info(`✅ [SCRIPT-LOADER] Setting incoming call script to: ${scriptId}`);
    return true;
}
function getCurrentIncomingScript() {
    // Return numeric ID 7 as string which maps to incoming campaign 1 (7-6=1)
    // This fixes the "Non-numeric ID 'incoming-1', defaulting to campaign 1" error
    logger_1.logger.debug('📋 [SCRIPT-LOADER] Returning current incoming script: 7 (incoming campaign 1)');
    return '7';
}
function getCurrentIncomingScriptId() {
    // Return the actual numeric ID for proper type consistency in validation
    return 7;
}
function createCustomIncomingScript(scriptData) {
    logger_1.logger.warn('⚠️ [SCRIPT-LOADER] Custom incoming scripts not supported - use campaigns 1-6');
    logger_1.logger.debug('📋 [SCRIPT-LOADER] Received script data:', {
        hasData: !!scriptData,
        keys: scriptData ? Object.keys(scriptData) : []
    });
    return false;
}
// Incoming scenario functions
function getIncomingScenario(scenarioId) {
    const id = parseInt(scenarioId.replace(/\D/g, '')) || 1;
    return loadCampaignScript(id, 'incoming');
}
function listIncomingScenarios() {
    return listIncomingCallScripts();
}
function setActiveIncomingScenario(scenarioId) {
    return setIncomingCallScript(scenarioId);
}
function getCurrentIncomingScenario() {
    return getCurrentIncomingScript();
}
//# sourceMappingURL=campaign-loader.js.map