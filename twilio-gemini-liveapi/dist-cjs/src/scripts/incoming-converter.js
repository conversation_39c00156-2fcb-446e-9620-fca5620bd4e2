"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertIncomingScenarioToCampaignScript = convertIncomingScenarioToCampaignScript;
const campaign_loader_1 = require("./campaign-loader");
function convertIncomingScenarioToCampaignScript(incomingScenario) {
    let campaignId = 1;
    if (incomingScenario.id && typeof incomingScenario.id === 'string') {
        const match = incomingScenario.id.match(/(\d+)/);
        if (match) {
            campaignId = parseInt(match[1]);
        }
    }
    if (campaignId < 1 || campaignId > 6) {
        campaignId = 1;
    }
    console.log(`🔄 [Gemini] Loading real incoming campaign ${campaignId} for scenario: ${incomingScenario.name}`);
    const realCampaignScript = (0, campaign_loader_1.loadCampaignScript)(campaignId, 'incoming', false);
    if (realCampaignScript) {
        console.log(`✅ [Gemini] Using real incoming campaign script: ${realCampaignScript.title}`);
        return realCampaignScript;
    }
    console.warn(`⚠️ [Gemini] Failed to load real campaign ${campaignId}, using minimal fallback`);
    return {
        id: campaignId,
        type: 'incoming',
        language: 'en',
        category: 'support',
        title: `Campaign ${campaignId} (Incoming)`,
        campaign: 'Customer Support',
        agentPersona: {
            name: 'Support Agent',
            tone: '',
            humanEmulation: true
        },
        script: {
            start: [
                { type: 'statement', content: '' }
            ]
        }
    };
}
//# sourceMappingURL=incoming-converter.js.map