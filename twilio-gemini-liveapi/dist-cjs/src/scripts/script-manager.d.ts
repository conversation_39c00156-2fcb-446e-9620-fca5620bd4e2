import { CampaignScript, ScriptConfig, ScriptInfo, CustomScriptData } from './campaign-types';
export declare class ScriptManager {
    private currentIncomingScript;
    private currentOutboundScript;
    private customScripts;
    private scriptCache;
    private isPreloading;
    private loadingScripts;
    private fallbackInstructions;
    constructor();
    /**
     * Get fallback AI instructions used when a script is missing or invalid
     */
    getFallbackInstructions(): string;
    preloadScripts(): Promise<void>;
    getScriptFromCache(id: string | number, type?: 'outbound' | 'incoming'): CampaignScript | null;
    /**
     * Get all available incoming call scripts
     */
    getIncomingScripts(): ScriptInfo[];
    /**
     * Get current active incoming script
     */
    getCurrentIncomingScript(): string | null;
    /**
     * Set active incoming script
     */
    setIncomingScript(scriptId: string): boolean;
    /**
     * Create custom incoming script
     */
    createCustomIncomingScript(scriptData: CustomScriptData): boolean;
    /**
     * Get all available outbound call scripts
     */
    getOutboundScripts(): ScriptInfo[];
    /**
     * Get current active outbound script
     */
    getCurrentOutboundScript(): string | null;
    /**
     * Set active outbound script
     */
    setOutboundScript(scriptId: string): boolean;
    /**
     * Create custom outbound script
     */
    createCustomOutboundScript(scriptData: CustomScriptData): boolean;
    /**
     * Get script configuration for session
     */
    getScriptConfig(scriptId: string | number, isIncoming?: boolean): Promise<ScriptConfig | null>;
    /**
     * Format campaign script into AI instructions
     */
    private formatCampaignInstructions;
    /**
     * Format script flow for better readability
     */
    private formatScriptFlow;
    /**
     * Validate script formatting and content quality
     */
    validateScriptContent(instructions: string): {
        isValid: boolean;
        issues: string[];
    };
    /**
     * Validate script data
     */
    private validateScript;
    /**
     * Create fallback configuration when script loading fails
     * @param scriptId - Script ID that failed to load
     * @param isIncoming - Whether this is an incoming call
     * @returns Fallback script configuration
     */
    private createFallbackConfig;
}
export declare const scriptManager: ScriptManager;
//# sourceMappingURL=script-manager.d.ts.map