"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScriptCache = void 0;
class ScriptCache {
    cache;
    timestamps;
    timeout;
    constructor(timeout = 300000) {
        this.cache = new Map();
        this.timestamps = new Map();
        this.timeout = timeout;
    }
    set(key, value) {
        this.cache.set(key, value);
        this.timestamps.set(key, Date.now());
    }
    get(key) {
        const ts = this.timestamps.get(key);
        if (!ts) {
            return null;
        }
        if (Date.now() - ts > this.timeout) {
            this.cache.delete(key);
            this.timestamps.delete(key);
            return null;
        }
        return this.cache.get(key) || null;
    }
    has(key) {
        const value = this.get(key);
        return value !== null;
    }
    delete(key) {
        this.timestamps.delete(key);
        return this.cache.delete(key);
    }
    clear() {
        this.cache.clear();
        this.timestamps.clear();
    }
    size() {
        return this.cache.size;
    }
}
exports.ScriptCache = ScriptCache;
//# sourceMappingURL=script-cache.js.map