interface AudioFormatConfig {
    input: {
        format: string;
        sampleRate: number;
        channels: number;
        encoding: string;
    };
    output: {
        format: string;
        sampleRate: number;
        channels: number;
        encoding: string;
    };
    gemini: {
        outputRate: number;
        inputFormat: string;
        outputFormat: string;
    };
}
interface AudioProcessingConfig {
    enableNoiseReduction: boolean;
    enableEchoCancellation: boolean;
    enableAutoGainControl: boolean;
    volumeNormalization: boolean;
    silenceDetection: {
        enabled: boolean;
        threshold: number;
        duration: number;
    };
}
interface AudioConfigUpdates {
    inputFormat?: string;
    outputFormat?: string;
    sampleRate?: number;
    twilioSampleRate?: number;
}
/**
 * Audio Configuration Manager
 * Handles audio formats, sample rates, file paths, and media URLs
 */
export declare class AudioConfigManager {
    private inputFormat;
    private outputFormat;
    private sampleRate;
    private twilioSampleRate;
    private geminiOutputRate;
    private greetingAudioUrl?;
    private publicUrl;
    private audioFiles;
    private supportedFormats;
    constructor();
    /**
     * Initialize audio file configurations
     */
    private initializeAudioFiles;
    /**
     * Initialize supported audio formats
     */
    private initializeSupportedFormats;
    /**
     * Get audio file URL for specific type and language
     */
    getAudioFileUrl(type: string, language?: string): string | null;
    /**
     * Get greeting audio URL for language
     */
    getGreetingAudioUrl(language?: string): string | null;
    /**
     * Get hold music URL for language
     */
    getHoldMusicUrl(language?: string): string | null;
    /**
     * Get transfer audio URL for language
     */
    getTransferAudioUrl(language?: string): string | null;
    /**
     * Get unavailable message audio URL for language
     */
    getUnavailableAudioUrl(language?: string): string | null;
    /**
     * Get audio format configuration
     */
    getAudioFormatConfig(): AudioFormatConfig;
    /**
     * Get encoding for audio format
     */
    private getFormatEncoding;
    /**
     * Validate audio format
     */
    isFormatSupported(format: string, type?: 'input' | 'output'): boolean;
    /**
     * Get optimal sample rate for format
     */
    getOptimalSampleRate(format: string, type?: 'input' | 'output'): number;
    /**
     * Get audio processing configuration
     */
    getAudioProcessingConfig(): AudioProcessingConfig;
    /**
     * Get media file path for local files
     */
    getLocalMediaPath(filename: string): string;
    /**
     * Update audio configuration at runtime
     */
    updateAudioConfig(updates: AudioConfigUpdates): AudioFormatConfig;
    /**
     * Get configuration summary
     */
    getConfigSummary(): {
        inputFormat: string;
        outputFormat: string;
        sampleRate: number;
        twilioSampleRate: number;
        geminiOutputRate: number;
        supportedInputFormats: string[];
        supportedOutputFormats: string[];
        audioFiles: string[];
    };
}
export declare const audioConfigManager: AudioConfigManager;
export declare function getAudioFileUrl(type: string, language?: string): string | null;
export declare function getGreetingAudioUrl(language?: string): string | null;
export declare function getAudioFormatConfig(): AudioFormatConfig;
export declare function isFormatSupported(format: string, type?: 'input' | 'output'): boolean;
export {};
//# sourceMappingURL=audio-config.d.ts.map