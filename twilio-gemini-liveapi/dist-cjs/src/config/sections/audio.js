"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.audio = void 0;
const validator_1 = require("../validator");
exports.audio = {
    inputFormat: process.env.INPUT_AUDIO_FORMAT || 'g711_ulaw',
    outputFormat: process.env.OUTPUT_AUDIO_FORMAT || 'g711_ulaw',
    sampleRate: validator_1.ConfigValidator.validateNumber(process.env.SAMPLE_RATE, 'SAMPLE_RATE', 8000, 48000, 16000),
    twilioSampleRate: validator_1.ConfigValidator.validateNumber(process.env.TWILIO_SAMPLE_RATE, 'TWILIO_SAMPLE_RATE', 8000, 48000, 8000),
    greetingAudioUrl: process.env.GREETING_AUDIO_URL
};
//# sourceMappingURL=audio.js.map