"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.websocket = void 0;
const validator_1 = require("../validator");
exports.websocket = {
    protocol: process.env.WS_PROTOCOL || 'wss',
    url: process.env.WEBSOCKET_URL,
    heartbeatInterval: validator_1.ConfigValidator.validateNumber(process.env.HEARTBEAT_INTERVAL, 'HEARTBEAT_INTERVAL', 1000, 120000, 30000),
    heartbeatTimeout: validator_1.ConfigValidator.validateNumber(process.env.HEARTBEAT_TIMEOUT, 'HEARTBEAT_TIMEOUT', 1000, 60000, 10000)
};
//# sourceMappingURL=websocket.js.map