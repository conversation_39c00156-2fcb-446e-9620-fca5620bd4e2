"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.server = void 0;
const validator_1 = require("../validator");
exports.server = {
    port: validator_1.ConfigValidator.validatePort(process.env.PORT, 'PORT', 3101),
    host: process.env.HOST || '0.0.0.0',
    environment: validator_1.ConfigValidator.validateEnum(process.env.NODE_ENV, ['development', 'production', 'test'], 'NODE_ENV', 'development'),
    publicUrl: validator_1.ConfigValidator.validateUrl(process.env.PUBLIC_URL ||
        `http://localhost:${process.env.TWILIO_GEMINI_BACKEND_PORT || process.env.PORT || 3101}`, 'PUBLIC_URL', true) ||
        `http://localhost:${process.env.TWILIO_GEMINI_BACKEND_PORT || process.env.PORT || 3101}`,
    corsOrigin: process.env.CORS_ORIGIN || process.env.PUBLIC_URL || '*',
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000'
};
//# sourceMappingURL=server.js.map