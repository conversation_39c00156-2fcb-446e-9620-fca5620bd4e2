"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ai = void 0;
const validator_1 = require("../validator");
exports.ai = {
    gemini: {
        defaultModel: process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog',
        availableModels: (process.env.GEMINI_AVAILABLE_MODELS || 'gemini-2.5-flash-preview-native-audio-dialog,gemini-2.0-flash-live-001').split(',').map(m => m.trim()),
        modelSelectionEnabled: process.env.GEMINI_MODEL_SELECTION_ENABLED === 'true',
        defaultVoice: process.env.GEMINI_DEFAULT_VOICE || 'Kore',
        voiceSelectionEnabled: process.env.GEMINI_VOICE_SELECTION_ENABLED === 'true',
        outputRate: validator_1.ConfigValidator.validateNumber(process.env.GEMINI_OUTPUT_RATE, 'GEMINI_OUTPUT_RATE', 8000, 48000, 24000),
        maxTokens: validator_1.ConfigValidator.validateNumber(process.env.GEMINI_MAX_TOKENS, 'GEMINI_MAX_TOKENS', 1, 32768, 8192),
        temperature: validator_1.ConfigValidator.validateNumber(process.env.GEMINI_TEMPERATURE, 'GEMINI_TEMPERATURE', 0, 2, 0.7),
        topP: validator_1.ConfigValidator.validateNumber(process.env.GEMINI_TOP_P, 'GEMINI_TOP_P', 0, 1, 0.9),
        topK: validator_1.ConfigValidator.validateNumber(process.env.GEMINI_TOP_K, 'GEMINI_TOP_K', 1, 100, 40),
        voices: {
            Aoede: process.env.VOICE_AOEDE || 'Aoede, female, bright neutral narrator',
            Puck: process.env.VOICE_PUCK || 'Puck, male, lively higher tenor',
            Charon: process.env.VOICE_CHARON || 'Charon, male, deep warm baritone',
            Kore: process.env.VOICE_KORE || 'Kore, female, soft alto empathetic',
            Fenrir: process.env.VOICE_FENRIR || 'Fenrir, male, assertive mid-range',
            Leda: process.env.VOICE_LEDA || 'Leda, female, clear RP-style announcer',
            Orus: process.env.VOICE_ORUS || 'Orus, male, relaxed breathy tenor',
            Zephyr: process.env.VOICE_ZEPHYR || 'Zephyr, female, airy youthful soprano'
        }
    },
    openai: {
        model: process.env.OPENAI_MODEL || 'gpt-4o-mini-realtime-preview',
        chatModel: process.env.OPENAI_CHAT_MODEL || 'gpt-4o-mini',
        voice: process.env.OPENAI_VOICE || 'shimmer',
        temperature: validator_1.ConfigValidator.validateNumber(process.env.OPENAI_TEMPERATURE, 'OPENAI_TEMPERATURE', 0, 2, 1.1)
    }
};
//# sourceMappingURL=ai.js.map