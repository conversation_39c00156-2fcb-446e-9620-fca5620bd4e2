export declare const ai: {
    gemini: {
        defaultModel: string;
        availableModels: string[];
        modelSelectionEnabled: boolean;
        defaultVoice: string;
        voiceSelectionEnabled: boolean;
        outputRate: number;
        maxTokens: number;
        temperature: number;
        topP: number;
        topK: number;
        voices: {
            Aoede: string;
            Puck: string;
            Charon: string;
            Kore: string;
            Fenrir: string;
            Leda: string;
            Orus: string;
            Zephyr: string;
        };
    };
    openai: {
        model: string;
        chatModel: string;
        voice: string;
        temperature: number;
    };
};
//# sourceMappingURL=ai.d.ts.map