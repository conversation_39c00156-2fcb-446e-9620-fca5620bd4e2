"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.timeouts = void 0;
// Timeout configuration section
const validator_js_1 = require("../validator.js");
exports.timeouts = {
    // Session timeouts
    sessionCreation: validator_js_1.ConfigValidator.validateNumber(process.env.SESSION_CREATION_TIMEOUT, 'SESSION_CREATION_TIMEOUT', 1, // min value
    null, // no max
    30000 // 30 seconds default
    ),
    sessionRecovery: validator_js_1.ConfigValidator.validateNumber(process.env.SESSION_RECOVERY_TIMEOUT, 'SESSION_RECOVERY_TIMEOUT', 1, null, 30000 // 30 seconds
    ),
    sessionSummary: validator_js_1.ConfigValidator.validateNumber(process.env.SESSION_SUMMARY_TIMEOUT, 'SESSION_SUMMARY_TIMEOUT', 1, null, 30000 // 30 seconds
    ),
    sessionSummaryWait: validator_js_1.ConfigValidator.validateNumber(process.env.SESSION_SUMMARY_WAIT, 'SESSION_SUMMARY_WAIT', 1, null, 10000 // 10 seconds
    ),
    // Recovery timeouts
    recoveryLock: validator_js_1.ConfigValidator.validateNumber(process.env.RECOVERY_LOCK_TIMEOUT, 'RECOVERY_LOCK_TIMEOUT', 1, null, 30000 // 30 seconds
    ),
    recoveryBaseDelay: validator_js_1.ConfigValidator.validateNumber(process.env.RECOVERY_BASE_DELAY, 'RECOVERY_BASE_DELAY', 1, null, 1000 // 1 second
    ),
    recoveryMaxDelay: validator_js_1.ConfigValidator.validateNumber(process.env.RECOVERY_MAX_DELAY, 'RECOVERY_MAX_DELAY', 1, null, 30000 // 30 seconds
    ),
    recoveryErrorDelay: validator_js_1.ConfigValidator.validateNumber(process.env.RECOVERY_ERROR_DELAY, 'RECOVERY_ERROR_DELAY', 1, null, 1000 // 1 second
    ),
    recoveryErrorMaxDelay: validator_js_1.ConfigValidator.validateNumber(process.env.RECOVERY_ERROR_MAX_DELAY, 'RECOVERY_ERROR_MAX_DELAY', 1, null, 8000 // 8 seconds
    ),
    // Health check intervals
    healthCheckInterval: validator_js_1.ConfigValidator.validateNumber(process.env.HEALTH_CHECK_INTERVAL, 'HEALTH_CHECK_INTERVAL', 1, null, 30000 // 30 seconds
    ),
    maxConnectionAge: validator_js_1.ConfigValidator.validateNumber(process.env.MAX_CONNECTION_AGE, 'MAX_CONNECTION_AGE', 1, null, 3600000 // 1 hour
    ),
    staleConnectionThreshold: validator_js_1.ConfigValidator.validateNumber(process.env.STALE_CONNECTION_THRESHOLD, 'STALE_CONNECTION_THRESHOLD', 1, null, 300000 // 5 minutes
    ),
    // Cache timeouts
    scriptCacheTimeout: validator_js_1.ConfigValidator.validateNumber(process.env.SCRIPT_CACHE_TIMEOUT, 'SCRIPT_CACHE_TIMEOUT', 1, null, 300000 // 5 minutes
    ),
    configCacheTimeout: validator_js_1.ConfigValidator.validateNumber(process.env.CONFIG_CACHE_TIMEOUT, 'CONFIG_CACHE_TIMEOUT', 1, null, 30000 // 30 seconds
    ),
    // Test timeouts
    testCallDuration: validator_js_1.ConfigValidator.validateNumber(process.env.TEST_CALL_DURATION, 'TEST_CALL_DURATION', 1, null, 30000 // 30 seconds
    ),
    // Security timeouts
    nonceMaxAge: validator_js_1.ConfigValidator.validateNumber(process.env.NONCE_MAX_AGE, 'NONCE_MAX_AGE', 1, null, 300000 // 5 minutes
    ),
    rateLimitWindow: validator_js_1.ConfigValidator.validateNumber(process.env.RATE_LIMIT_WINDOW, 'RATE_LIMIT_WINDOW', 1, null, 300000 // 5 minutes
    ),
    rateLimitCleanupInterval: validator_js_1.ConfigValidator.validateNumber(process.env.RATE_LIMIT_CLEANUP_INTERVAL, 'RATE_LIMIT_CLEANUP_INTERVAL', 1, null, 300000 // 5 minutes
    ),
    // Performance timeouts
    parallelInitTimeout: validator_js_1.ConfigValidator.validateNumber(process.env.PARALLEL_INIT_TIMEOUT, 'PARALLEL_INIT_TIMEOUT', 1, null, 10000 // 10 seconds
    ),
    slowOperationThreshold: validator_js_1.ConfigValidator.validateNumber(process.env.SLOW_OPERATION_THRESHOLD, 'SLOW_OPERATION_THRESHOLD', 1, null, 10000 // 10 seconds
    ),
    performanceMonitorInterval: validator_js_1.ConfigValidator.validateNumber(process.env.PERFORMANCE_MONITOR_INTERVAL, 'PERFORMANCE_MONITOR_INTERVAL', 1, null, 30000 // 30 seconds
    )
};
//# sourceMappingURL=timeouts.js.map