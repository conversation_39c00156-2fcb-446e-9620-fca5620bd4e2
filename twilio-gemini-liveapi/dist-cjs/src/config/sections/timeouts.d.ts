export declare const timeouts: {
    sessionCreation: number;
    sessionRecovery: number;
    sessionSummary: number;
    sessionSummaryWait: number;
    recoveryLock: number;
    recoveryBaseDelay: number;
    recoveryMaxDelay: number;
    recoveryErrorDelay: number;
    recoveryErrorMaxDelay: number;
    healthCheckInterval: number;
    maxConnectionAge: number;
    staleConnectionThreshold: number;
    scriptCacheTimeout: number;
    configCacheTimeout: number;
    testCallDuration: number;
    nonceMaxAge: number;
    rateLimitWindow: number;
    rateLimitCleanupInterval: number;
    parallelInitTimeout: number;
    slowOperationThreshold: number;
    performanceMonitorInterval: number;
};
//# sourceMappingURL=timeouts.d.ts.map