"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.limits = void 0;
// Limits configuration section
const validator_js_1 = require("../validator.js");
exports.limits = {
    // Audio limits
    audioClippingThreshold: validator_js_1.ConfigValidator.validateNumber(process.env.AUDIO_CLIPPING_THRESHOLD, 'AUDIO_CLIPPING_THRESHOLD', 1, null, 30000),
    audioSilenceThreshold: validator_js_1.ConfigValidator.validateNumber(process.env.AUDIO_SILENCE_THRESHOLD, 'AUDIO_SILENCE_THRESHOLD', 1, null, 10000),
    // Security limits
    maxPayloadSize: validator_js_1.ConfigValidator.validateNumber(process.env.MAX_PAYLOAD_SIZE, 'MAX_PAYLOAD_SIZE', 1, null, 10485760 // 10MB
    ),
    maxRateLimitRequests: validator_js_1.ConfigValidator.validateNumber(process.env.MAX_RATE_LIMIT_REQUESTS, 'MAX_RATE_LIMIT_REQUESTS', 1, null, 5),
    // Script limits
    maxScriptLength: validator_js_1.ConfigValidator.validateNumber(process.env.MAX_SCRIPT_LENGTH, 'MAX_SCRIPT_LENGTH', 1, null, 10000),
    // Retry limits
    maxRetryDelay: validator_js_1.ConfigValidator.validateNumber(process.env.MAX_RETRY_DELAY, 'MAX_RETRY_DELAY', 1, null, 30000),
    // Recovery limits
    maxRecoveryAttempts: validator_js_1.ConfigValidator.validateNumber(process.env.MAX_RECOVERY_ATTEMPTS, 'MAX_RECOVERY_ATTEMPTS', 1, null, 3),
    recoveryJitterPercent: parseFloat(process.env.RECOVERY_JITTER_PERCENT || '0.1'), // 10% jitter
    // Performance limits
    performanceErrorThreshold: validator_js_1.ConfigValidator.validateNumber(process.env.PERFORMANCE_ERROR_THRESHOLD, 'PERFORMANCE_ERROR_THRESHOLD', 1, null, 3)
};
//# sourceMappingURL=limits.js.map