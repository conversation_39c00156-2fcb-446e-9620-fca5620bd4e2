"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.auth = void 0;
const validator_1 = require("../validator");
exports.auth = {
    gemini: {
        apiKey: validator_1.ConfigValidator.validateRequired(process.env.GEMINI_API_KEY, 'GEMINI_API_KEY')
    },
    openai: {
        apiKey: process.env.OPENAI_API_KEY,
        apiUrl: process.env.OPENAI_API_URL || 'https://api.openai.com'
    },
    twilio: {
        accountSid: validator_1.ConfigValidator.validateRequired(process.env.TWILIO_ACCOUNT_SID, 'TWILIO_ACCOUNT_SID'),
        authToken: validator_1.ConfigValidator.validateRequired(process.env.TWILIO_AUTH_TOKEN, 'TWILIO_AUTH_TOKEN')
    },
    deepgram: {
        apiKey: process.env.DEEPGRAM_API_KEY || ''
    },
    supabase: {
        url: process.env.SUPABASE_URL || '',
        anonKey: process.env.SUPABASE_ANON_KEY || ''
    },
    ngrok: {
        authToken: process.env.NGROK_AUTHTOKEN
    }
};
//# sourceMappingURL=auth.js.map