{"version": 3, "file": "timeouts.js", "sourceRoot": "", "sources": ["../../../../src/config/sections/timeouts.ts"], "names": [], "mappings": ";;;AAAA,gCAAgC;AAChC,kDAAkD;AAErC,QAAA,QAAQ,GAAG;IACpB,mBAAmB;IACnB,eAAe,EAAE,8BAAe,CAAC,cAAc,CAC3C,OAAO,CAAC,GAAG,CAAC,wBAAwB,EACpC,0BAA0B,EAC1B,CAAC,EAAE,YAAY;IACf,IAAI,EAAE,SAAS;IACf,KAAK,CAAC,qBAAqB;KAC9B;IACD,eAAe,EAAE,8BAAe,CAAC,cAAc,CAC3C,OAAO,CAAC,GAAG,CAAC,wBAAwB,EACpC,0BAA0B,EAC1B,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IACD,cAAc,EAAE,8BAAe,CAAC,cAAc,CAC1C,OAAO,CAAC,GAAG,CAAC,uBAAuB,EACnC,yBAAyB,EACzB,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IACD,kBAAkB,EAAE,8BAAe,CAAC,cAAc,CAC9C,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAChC,sBAAsB,EACtB,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IAED,oBAAoB;IACpB,YAAY,EAAE,8BAAe,CAAC,cAAc,CACxC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EACjC,uBAAuB,EACvB,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IACD,iBAAiB,EAAE,8BAAe,CAAC,cAAc,CAC7C,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAC/B,qBAAqB,EACrB,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,WAAW;KACnB;IACD,gBAAgB,EAAE,8BAAe,CAAC,cAAc,CAC5C,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAC9B,oBAAoB,EACpB,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IACD,kBAAkB,EAAE,8BAAe,CAAC,cAAc,CAC9C,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAChC,sBAAsB,EACtB,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,WAAW;KACnB;IACD,qBAAqB,EAAE,8BAAe,CAAC,cAAc,CACjD,OAAO,CAAC,GAAG,CAAC,wBAAwB,EACpC,0BAA0B,EAC1B,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY;KACpB;IAED,yBAAyB;IACzB,mBAAmB,EAAE,8BAAe,CAAC,cAAc,CAC/C,OAAO,CAAC,GAAG,CAAC,qBAAqB,EACjC,uBAAuB,EACvB,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IACD,gBAAgB,EAAE,8BAAe,CAAC,cAAc,CAC5C,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAC9B,oBAAoB,EACpB,CAAC,EACD,IAAI,EACJ,OAAO,CAAC,SAAS;KACpB;IACD,wBAAwB,EAAE,8BAAe,CAAC,cAAc,CACpD,OAAO,CAAC,GAAG,CAAC,0BAA0B,EACtC,4BAA4B,EAC5B,CAAC,EACD,IAAI,EACJ,MAAM,CAAC,YAAY;KACtB;IAED,iBAAiB;IACjB,kBAAkB,EAAE,8BAAe,CAAC,cAAc,CAC9C,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAChC,sBAAsB,EACtB,CAAC,EACD,IAAI,EACJ,MAAM,CAAC,YAAY;KACtB;IACD,kBAAkB,EAAE,8BAAe,CAAC,cAAc,CAC9C,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAChC,sBAAsB,EACtB,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IAED,gBAAgB;IAChB,gBAAgB,EAAE,8BAAe,CAAC,cAAc,CAC5C,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAC9B,oBAAoB,EACpB,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IAED,oBAAoB;IACpB,WAAW,EAAE,8BAAe,CAAC,cAAc,CACvC,OAAO,CAAC,GAAG,CAAC,aAAa,EACzB,eAAe,EACf,CAAC,EACD,IAAI,EACJ,MAAM,CAAC,YAAY;KACtB;IACD,eAAe,EAAE,8BAAe,CAAC,cAAc,CAC3C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAC7B,mBAAmB,EACnB,CAAC,EACD,IAAI,EACJ,MAAM,CAAC,YAAY;KACtB;IACD,wBAAwB,EAAE,8BAAe,CAAC,cAAc,CACpD,OAAO,CAAC,GAAG,CAAC,2BAA2B,EACvC,6BAA6B,EAC7B,CAAC,EACD,IAAI,EACJ,MAAM,CAAC,YAAY;KACtB;IAED,uBAAuB;IACvB,mBAAmB,EAAE,8BAAe,CAAC,cAAc,CAC/C,OAAO,CAAC,GAAG,CAAC,qBAAqB,EACjC,uBAAuB,EACvB,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IACD,sBAAsB,EAAE,8BAAe,CAAC,cAAc,CAClD,OAAO,CAAC,GAAG,CAAC,wBAAwB,EACpC,0BAA0B,EAC1B,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;IACD,0BAA0B,EAAE,8BAAe,CAAC,cAAc,CACtD,OAAO,CAAC,GAAG,CAAC,4BAA4B,EACxC,8BAA8B,EAC9B,CAAC,EACD,IAAI,EACJ,KAAK,CAAC,aAAa;KACtB;CACJ,CAAC"}