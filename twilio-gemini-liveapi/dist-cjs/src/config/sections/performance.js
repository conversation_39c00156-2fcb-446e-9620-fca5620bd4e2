"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.performance = void 0;
const validator_1 = require("../validator");
exports.performance = {
    enableCaching: process.env.ENABLE_CACHING === 'true',
    cacheTimeout: validator_1.ConfigValidator.validateNumber(process.env.CACHE_TIMEOUT, 'CACHE_TIMEOUT', 1, 86400, 300),
    maxConcurrentCalls: validator_1.ConfigValidator.validateNumber(process.env.MAX_CONCURRENT_CALLS, 'MAX_CONCURRENT_CALLS', 1, 1000, 100),
    enableMetrics: process.env.ENABLE_METRICS === 'true'
};
//# sourceMappingURL=performance.js.map