"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.campaigns = void 0;
const path_1 = __importDefault(require("path"));
const url_1 = require("url");
const validator_1 = require("../validator");
const __filename = (0, url_1.fileURLToPath)(import.meta.url);
const __dirname = path_1.default.dirname(__filename);
exports.campaigns = {
    scriptsPath: process.env.CAMPAIGN_SCRIPTS_PATH || path_1.default.join(__dirname, '../../../call-center-frontend/public'),
    totalCampaigns: validator_1.ConfigValidator.validateNumber(process.env.TOTAL_CAMPAIGNS, 'TOTAL_CAMPAIGNS', 1, 100, 6),
    defaultCampaignId: validator_1.ConfigValidator.validateNumber(process.env.DEFAULT_CAMPAIGN_ID, 'DEFAULT_CAMPAIGN_ID', 1, 100, 1),
    enableCustomScripts: process.env.ENABLE_CUSTOM_SCRIPTS === 'true',
    scriptCacheTimeout: validator_1.ConfigValidator.validateNumber(process.env.SCRIPT_CACHE_TIMEOUT, 'SCRIPT_CACHE_TIMEOUT', 0, 86400, 300)
};
//# sourceMappingURL=campaigns.js.map