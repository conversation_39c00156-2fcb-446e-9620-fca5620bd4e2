"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.security = void 0;
exports.security = {
    vocabularyRestrictions: (process.env.VOCABULARY_RESTRICTIONS || 'AI,artificial intelligence,machine learning,algorithm,model,training data,neural network,politics,religion,sensitive topics,controversial topics').split(',').map(w => w.trim()),
    enableRecordingConfirmation: process.env.ENABLE_RECORDING_CONFIRMATION === 'true',
    recordingConfirmationMessage: process.env.RECORDING_CONFIRMATION_MESSAGE || 'This call is recorded for quality assurance. Is it okay to continue?'
};
//# sourceMappingURL=security.js.map