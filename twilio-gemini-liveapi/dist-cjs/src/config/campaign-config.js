"use strict";
// Campaign Configuration System
// Replaces hardcoded campaign scripts with configurable system
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.campaignConfigManager = exports.CampaignConfigManager = void 0;
exports.getCampaignScript = getCampaignScript;
exports.getAllCampaigns = getAllCampaigns;
exports.validateCampaignScript = validateCampaignScript;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const config_1 = require("./config");
const logger_1 = require("../utils/logger");
const supabase_js_1 = require("@supabase/supabase-js");
/**
 * Campaign Configuration Manager
 * Handles loading and managing campaign scripts from various sources
 */
class CampaignConfigManager {
    scriptsPath;
    totalCampaigns;
    defaultCampaignId;
    enableCustomScripts;
    scriptCache;
    cacheTimeout;
    constructor() {
        this.scriptsPath = (0, config_1.getConfigValue)('campaigns.scriptsPath');
        this.totalCampaigns = (0, config_1.getConfigValue)('campaigns.totalCampaigns', 6);
        this.defaultCampaignId = (0, config_1.getConfigValue)('campaigns.defaultCampaignId', 1);
        this.enableCustomScripts = (0, config_1.getConfigValue)('campaigns.enableCustomScripts', false);
        this.scriptCache = new Map();
        this.cacheTimeout = (0, config_1.getConfigValue)('campaigns.scriptCacheTimeout', 300) * 1000; // Convert to ms
    }
    /**
     * Load campaign script from file system
     */
    loadCampaignFromFile(campaignId, type = 'outbound') {
        try {
            console.log(`📂 [CAMPAIGN-CONFIG] Loading campaign from file:`, {
                campaignId,
                type,
                scriptsPath: this.scriptsPath
            });
            // PERMANENT FIX: Use correct file paths and names
            let fileName;
            let filePath;
            if (type === 'outbound') {
                fileName = `campaign${campaignId}.json`;
                filePath = path_1.default.join(process.cwd(), 'call-center-frontend', 'public', fileName);
            }
            else {
                fileName = `incoming-campaign${campaignId}.json`;
                filePath = path_1.default.join(process.cwd(), 'call-center-frontend', 'public', fileName);
            }
            console.log(`📂 [CAMPAIGN-CONFIG] Looking for file: ${fileName} at path: ${filePath}`);
            // Validate and sanitize the file name to prevent path traversal
            if (!fileName.match(/^[a-zA-Z0-9-]+\.json$/)) {
                logger_1.configLogger.error(`Invalid campaign file name format: ${fileName}`);
                return null;
            }
            // Ensure the file name doesn't contain path traversal sequences
            const sanitizedFileName = path_1.default.basename(fileName);
            if (sanitizedFileName !== fileName) {
                logger_1.configLogger.error(`Potential path traversal attempt detected: ${fileName}`);
                return null;
            }
            // Additional security check: ensure no directory traversal characters
            if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
                logger_1.configLogger.error(`Path traversal attempt detected in filename: ${fileName}`);
                return null;
            }
            // filePath is already set above with correct path
            if (!(0, fs_1.existsSync)(filePath)) {
                console.warn(`❌ [CAMPAIGN-CONFIG] Campaign file not found: ${filePath}`);
                logger_1.configLogger.warn(`Campaign file not found: ${filePath}`);
                return null;
            }
            console.log(`✅ [CAMPAIGN-CONFIG] File exists: ${filePath}`);
            const readStartTime = Date.now();
            let fileContent;
            try {
                fileContent = (0, fs_1.readFileSync)(filePath, 'utf8');
            }
            catch (readError) {
                logger_1.configLogger.error(`❌ [CAMPAIGN-CONFIG] Failed to read file ${filePath}:`, readError instanceof Error ? readError : new Error(String(readError)));
                return null;
            }
            const readTime = Date.now() - readStartTime;
            const parseStartTime = Date.now();
            let campaignScript;
            try {
                campaignScript = JSON.parse(fileContent);
            }
            catch (parseError) {
                logger_1.configLogger.error(`❌ [CAMPAIGN-CONFIG] Failed to parse JSON from ${filePath}:`, parseError instanceof Error ? parseError : new Error(String(parseError)));
                return null;
            }
            const parseTime = Date.now() - parseStartTime;
            logger_1.configLogger.info(`⏱️ Campaign ${campaignId} (${type}) load times: read=${readTime}ms, parse=${parseTime}ms`);
            console.log(`✅ [CAMPAIGN-CONFIG] Campaign loaded successfully:`, {
                campaignId,
                type,
                title: campaignScript.campaign_title || campaignScript.title || 'Untitled',
                hasAgentPersona: !!campaignScript.agentPersona,
                hasCampaign: !!campaignScript.campaign,
                hasObjectives: !!campaignScript.objectives
            });
            // Apply configuration overrides
            this.applyCampaignConfigOverrides(campaignScript);
            return campaignScript;
        }
        catch (error) {
            logger_1.configLogger.error(`Error loading campaign ${campaignId} (${type})`, error instanceof Error ? error : new Error(String(error)));
            return null;
        }
    }
    /**
     * Apply configuration overrides to campaign script
     */
    applyCampaignConfigOverrides(campaignScript) {
        // Override transfer data with configuration
        if (campaignScript.transferData) {
            campaignScript.transferData.transferNumber =
                process.env[`TRANSFER_NUMBER_CAMPAIGN_${campaignScript.id}`] ||
                    (0, config_1.getConfigValue)('business.transfer.defaultTransferNumber');
            campaignScript.transferData.agentName =
                process.env[`AGENT_NAME_CAMPAIGN_${campaignScript.id}`] ||
                    (0, config_1.getConfigValue)('business.transfer.defaultAgentName');
        }
        // Override validation rules with configuration
        if (campaignScript.customerData?.optionalFieldsPreTransfer) {
            campaignScript.customerData.optionalFieldsPreTransfer.forEach(field => {
                if (field.field === 'vehicleCount' && field.disqualificationRule) {
                    const maxVehicles = (0, config_1.getConfigValue)('business.validation.maxVehicles', 9);
                    field.disqualificationRule.condition = `value <= 0 || value >= ${maxVehicles + 1}`;
                }
                if (field.field === 'claimsCount3yrs' && field.disqualificationRule) {
                    const maxClaims = (0, config_1.getConfigValue)('business.validation.maxClaims', 3);
                    field.disqualificationRule.condition = `value >= ${maxClaims}`;
                }
                if (field.field === 'vehicleYear1' && field.invalidResponseHandlers && field.invalidResponseHandlers.length > 0) {
                    const minYear = (0, config_1.getConfigValue)('business.validation.minVehicleYear', 1900);
                    const maxYear = (0, config_1.getConfigValue)('business.validation.maxVehicleYear', 2027);
                    if (field.invalidResponseHandlers[0]) {
                        field.invalidResponseHandlers[0].condition =
                            `response.type != 'integer' || response.value < ${minYear} || response.value > ${maxYear}`;
                    }
                }
            });
        }
        // Override vocabulary restrictions
        if (campaignScript.agentPersona) {
            campaignScript.agentPersona.vocabularyRestrictions =
                (0, config_1.getConfigValue)('security.vocabularyRestrictions', []);
            campaignScript.agentPersona.recordedMessageConfirmation =
                (0, config_1.getConfigValue)('security.recordingConfirmationMessage');
        }
    }
    /**
     * Get campaign script with caching
     */
    async getCampaignScript(campaignId, type = 'outbound', useCache = true) {
        const cacheKey = `${type}_${campaignId}`;
        console.log(`📁 [CAMPAIGN-CONFIG] Getting campaign script:`, {
            campaignId,
            type,
            useCache,
            cacheKey
        });
        // Check cache first
        if (useCache && this.scriptCache.has(cacheKey)) {
            const cached = this.scriptCache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                console.log(`✅ [CAMPAIGN-CONFIG] Found in cache: ${cacheKey}`);
                return cached.script;
            }
            // Cache expired, remove it
            console.log(`⚠️ [CAMPAIGN-CONFIG] Cache expired for: ${cacheKey}`);
            this.scriptCache.delete(cacheKey);
        }
        // Load from source
        let script = null;
        // Try loading from file system first
        console.log(`📁 [CAMPAIGN-CONFIG] Loading from file system: ${type} campaign ${campaignId}`);
        script = this.loadCampaignFromFile(campaignId, type);
        // If custom scripts are enabled, try loading from database/API
        if (!script && this.enableCustomScripts) {
            console.log(`📁 [CAMPAIGN-CONFIG] No file found, trying database...`);
            script = await this.loadCampaignFromDatabase(campaignId, type);
        }
        // Fallback to default template
        if (!script) {
            console.log(`⚠️ [CAMPAIGN-CONFIG] No script found, using default template`);
            script = this.createDefaultCampaignTemplate(campaignId, type);
        }
        // Convert to external type
        const externalScript = script ? this.convertToExternalType(script) : null;
        // Cache the result
        if (useCache && externalScript) {
            this.scriptCache.set(cacheKey, {
                script: externalScript,
                timestamp: Date.now()
            });
        }
        return externalScript;
    }
    /**
     * Convert internal campaign script to external type
     */
    convertToExternalType(script) {
        return {
            id: script.id.toString(),
            name: script.title,
            content: script.campaign,
            language: script.language || 'en',
            voice: script.agentPersona?.voice,
            model: undefined, // Add model if available in your campaign scripts
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };
    }
    /**
     * Load campaign from database using Supabase
     */
    async loadCampaignFromDatabase(campaignId, type) {
        const url = (0, config_1.getConfigValue)('auth.supabase.url');
        const key = (0, config_1.getConfigValue)('auth.supabase.anonKey');
        if (!url || !key) {
            logger_1.configLogger.warn('Supabase configuration missing, skipping DB lookup');
            return null;
        }
        try {
            const client = (0, supabase_js_1.createClient)(url, key);
            const { data, error } = await client
                .from('campaign_scripts')
                .select('*')
                .eq('id', campaignId)
                .eq('type', type)
                .single();
            if (error) {
                logger_1.configLogger.error(`Error loading campaign ${campaignId} (${type}) from DB`, error);
                return null;
            }
            if (!data) {
                return null;
            }
            const script = data;
            this.applyCampaignConfigOverrides(script);
            return script;
        }
        catch (error) {
            logger_1.configLogger.error(`Database load failed for campaign ${campaignId} (${type})`, error instanceof Error ? error : new Error(String(error)));
            return null;
        }
    }
    /**
     * Create default campaign template
     */
    createDefaultCampaignTemplate(campaignId, type) {
        const language = (0, config_1.getConfigValue)('localization.defaultLanguage', 'en');
        const defaultVoice = (0, config_1.getConfigValue)(`voices.defaultVoiceMapping.${language}.${type}`, 'Kore');
        return {
            id: campaignId,
            type: type,
            language: language,
            category: 'general',
            title: `Campaign ${campaignId} (${type.charAt(0).toUpperCase() + type.slice(1)})`,
            campaign: `Default ${type} campaign`,
            agentPersona: {
                name: 'Agent',
                tone: 'Professional, helpful',
                humanEmulation: true,
                voice: defaultVoice,
                vocabularyRestrictions: (0, config_1.getConfigValue)('security.vocabularyRestrictions', []),
                recordedMessageConfirmation: (0, config_1.getConfigValue)('security.recordingConfirmationMessage')
            },
            customerData: {
                optionalFieldsPreTransfer: []
            },
            transferData: {
                transferNumber: (0, config_1.getConfigValue)('business.transfer.defaultTransferNumber'),
                agentName: (0, config_1.getConfigValue)('business.transfer.defaultAgentName'),
                warmTransferIntroductionAgent: 'Transferring customer. Ready?',
                warmTransferIntroductionCustomer: 'I will now transfer you to an agent. Please hold.',
                specialistNotAvailableMessage: 'All agents are busy. We will call you back.'
            },
            script: {
                start: [
                    {
                        type: 'statement',
                        content: 'Hello, how can I help you today?'
                    }
                ]
            }
        };
    }
    /**
     * Get all available campaigns
     */
    async getAllCampaigns(type = 'outbound') {
        const campaigns = [];
        for (let i = 1; i <= this.totalCampaigns; i++) {
            const campaign = await this.getCampaignScript(i, type);
            if (campaign) {
                campaigns.push(campaign);
            }
        }
        return campaigns;
    }
    /**
     * Validate campaign script structure
     */
    validateCampaignScript(script) {
        const required = ['id', 'type', 'title', 'agentPersona', 'script'];
        for (const field of required) {
            if (!script[field]) {
                throw new Error(`Missing required field in campaign script: ${field}`);
            }
        }
        if (!script.agentPersona.name) {
            throw new Error('Missing agent persona name');
        }
        if (!script.script.start || !Array.isArray(script.script.start)) {
            throw new Error('Invalid script structure: missing or invalid start section');
        }
        return true;
    }
    /**
     * Clear cache
     */
    clearCache() {
        this.scriptCache.clear();
        logger_1.configLogger.info('Campaign script cache cleared');
    }
    /**
     * Invalidate specific cache entry
     */
    invalidateCache(campaignId, type = 'outbound') {
        const cacheKey = `${type}_${campaignId}`;
        const deleted = this.scriptCache.delete(cacheKey);
        if (deleted) {
            logger_1.configLogger.info(`Cache invalidated for ${cacheKey}`);
        }
        return deleted;
    }
    /**
     * Invalidate all cache entries for a specific campaign ID
     */
    invalidateCampaignCache(campaignId) {
        const outboundKey = `outbound_${campaignId}`;
        const incomingKey = `incoming_${campaignId}`;
        const deletedOutbound = this.scriptCache.delete(outboundKey);
        const deletedIncoming = this.scriptCache.delete(incomingKey);
        if (deletedOutbound || deletedIncoming) {
            logger_1.configLogger.info(`Cache invalidated for campaign ${campaignId} (outbound: ${deletedOutbound}, incoming: ${deletedIncoming})`);
        }
        return deletedOutbound || deletedIncoming;
    }
    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            size: this.scriptCache.size,
            keys: Array.from(this.scriptCache.keys()),
            timeout: this.cacheTimeout,
            maxSize: 1000 // Add max size info
        };
    }
}
exports.CampaignConfigManager = CampaignConfigManager;
// Export singleton instance
exports.campaignConfigManager = new CampaignConfigManager();
// Export utility functions
async function getCampaignScript(campaignId, type = 'outbound') {
    return exports.campaignConfigManager.getCampaignScript(campaignId, type);
}
async function getAllCampaigns(type = 'outbound') {
    return exports.campaignConfigManager.getAllCampaigns(type);
}
function validateCampaignScript(script) {
    return exports.campaignConfigManager.validateCampaignScript(script);
}
//# sourceMappingURL=campaign-config.js.map