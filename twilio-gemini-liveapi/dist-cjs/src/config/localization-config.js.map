{"version": 3, "file": "localization-config.js", "sourceRoot": "", "sources": ["../../../src/config/localization-config.ts"], "names": [], "mappings": ";AAAA,oCAAoC;AACpC,wDAAwD;;;AAmSxD,8CAEC;AAED,kDAEC;AAED,4CAEC;AAED,sDAEC;AAED,kDAEC;AAnTD,qCAAkD;AA2ClD;;;GAGG;AACH,MAAa,yBAAyB;IAC1B,eAAe,CAAS;IACxB,kBAAkB,CAAW;IAC7B,mBAAmB,CAAU;IAC7B,gBAAgB,CAAS;IACzB,gBAAgB,CAAmB;IACnC,YAAY,CAAe;IAEnC;QACI,IAAI,CAAC,eAAe,GAAG,IAAA,uBAAc,EAAC,8BAA8B,EAAE,IAAI,CAAW,CAAC;QACtF,IAAI,CAAC,kBAAkB,GAAG,IAAA,uBAAc,EAAC,iCAAiC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAa,CAAC;QAC5G,IAAI,CAAC,mBAAmB,GAAG,IAAA,uBAAc,EAAC,kCAAkC,EAAE,IAAI,CAAY,CAAC;QAC/F,IAAI,CAAC,gBAAgB,GAAG,IAAA,uBAAc,EAAC,+BAA+B,EAAE,IAAI,CAAW,CAAC;QAExF,oBAAoB;QACpB,IAAI,CAAC,gBAAgB,GAAG;YACpB,IAAI,EAAE;gBACF,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,OAAO;gBACf,KAAK,EAAE;oBACH,QAAQ,EAAE,IAAA,uBAAc,EAAC,wCAAwC,EAAE,YAAY,CAAW;oBAC1F,QAAQ,EAAE,IAAA,uBAAc,EAAC,wCAAwC,EAAE,SAAS,CAAW;iBAC1F;gBACD,aAAa,EAAE,IAAI;aACtB;YACD,IAAI,EAAE;gBACF,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,OAAO;gBACf,KAAK,EAAE;oBACH,QAAQ,EAAE,IAAA,uBAAc,EAAC,wCAAwC,EAAE,YAAY,CAAW;oBAC1F,QAAQ,EAAE,IAAA,uBAAc,EAAC,wCAAwC,EAAE,WAAW,CAAW;iBAC5F;gBACD,aAAa,EAAE,IAAI;aACtB;YACD,IAAI,EAAE;gBACF,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,OAAO;gBACf,KAAK,EAAE;oBACH,QAAQ,EAAE,IAAA,uBAAc,EAAC,wCAAwC,EAAE,cAAc,CAAW;oBAC5F,QAAQ,EAAE,IAAA,uBAAc,EAAC,wCAAwC,EAAE,eAAe,CAAW;iBAChG;gBACD,aAAa,EAAE,IAAI;aACtB;SACJ,CAAC;QAEF,sCAAsC;QACtC,IAAI,CAAC,YAAY,GAAG;YAChB,EAAE,EAAE;gBACA,qBAAqB,EAAE,IAAA,uBAAc,EAAC,uCAAuC,EACzE,sEAAsE,CAAW;gBACrF,eAAe,EAAE,mDAAmD;gBACpE,gBAAgB,EAAE,6CAA6C;gBAC/D,eAAe,EAAE,kCAAkC;gBACnD,kBAAkB,EAAE;oBAChB,WAAW,EAAE,4DAA4D;oBACzE,WAAW,EAAE,eAAe;oBAC5B,YAAY,EAAE,gBAAgB;oBAC9B,YAAY,EAAE,8DAA8D;oBAC5E,WAAW,EAAE,0CAA0C;iBAC1D;aACJ;YACD,EAAE,EAAE;gBACA,qBAAqB,EAAE,uEAAuE;gBAC9F,eAAe,EAAE,qDAAqD;gBACtE,gBAAgB,EAAE,+DAA+D;gBACjF,eAAe,EAAE,iCAAiC;gBAClD,kBAAkB,EAAE;oBAChB,WAAW,EAAE,+DAA+D;oBAC5E,WAAW,EAAE,cAAc;oBAC3B,YAAY,EAAE,eAAe;oBAC7B,YAAY,EAAE,0DAA0D;oBACxE,WAAW,EAAE,gDAAgD;iBAChE;aACJ;YACD,EAAE,EAAE;gBACA,qBAAqB,EAAE,yEAAyE;gBAChG,eAAe,EAAE,8CAA8C;gBAC/D,gBAAgB,EAAE,uDAAuD;gBACzE,eAAe,EAAE,sCAAsC;gBACvD,kBAAkB,EAAE;oBAChB,WAAW,EAAE,uDAAuD;oBACpE,WAAW,EAAE,WAAW;oBACxB,YAAY,EAAE,UAAU;oBACxB,YAAY,EAAE,kDAAkD;oBAChE,WAAW,EAAE,iDAAiD;iBACjE;aACJ;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,YAAoB;QACzC,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACjG,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,YAAoB,EAAE,WAAoC,UAAU;QAC3F,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACxD,OAAO,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC;IACnE,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,YAAoB,EAAE,OAAe,EAAE,WAAmB,EAAE;QAChF,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAChE,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE5F,4DAA4D;QAC5D,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,KAAK,GAAQ,KAAK,CAAC;QAEvB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;gBACrD,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACJ,OAAO,QAAQ,CAAC;YACpB,CAAC;QACL,CAAC;QAED,OAAO,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,YAAgC;QACzD,IAAI,CAAC,YAAY,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC,eAAe,CAAC;QAAA,CAAC;QAEjD,wDAAwD;QACxD,MAAM,UAAU,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5D,qBAAqB;QACrB,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/C,OAAO,UAAU,CAAC;QACtB,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,qBAAqB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxC,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;SACjC,CAAC,CAAC,CAAC;IACR,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,YAAoB;QAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,YAAoB;QAChD,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACxD,OAAO,UAAU,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,YAAoB;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACxD,OAAO,UAAU,CAAC,MAAM,IAAI,OAAO,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,YAAoB,EAAE,MAAgC;QAC9E,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAChE,IAAI,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG;gBACpC,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;gBACxC,GAAG,MAAM;aACZ,CAAC;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,YAAoB,EAAE,OAAe,EAAE,KAAa;QAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAChE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,GAAG,EAAoB,CAAC;QAC7D,CAAC;QAED,sBAAsB;QACtB,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,MAAM,GAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAClD,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YACrB,CAAC;YACD,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,gBAAgB;QAOnB,OAAO;YACH,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;SACjD,CAAC;IACN,CAAC;CACJ;AA5OD,8DA4OC;AAED,4BAA4B;AACf,QAAA,yBAAyB,GAAG,IAAI,yBAAyB,EAAE,CAAC;AAEzE,2BAA2B;AAC3B,SAAgB,iBAAiB,CAAC,YAAoB;IAClD,OAAO,iCAAyB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AACrE,CAAC;AAED,SAAgB,mBAAmB,CAAC,YAAoB,EAAE,WAAoC,UAAU;IACpG,OAAO,iCAAyB,CAAC,mBAAmB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;AACjF,CAAC;AAED,SAAgB,gBAAgB,CAAC,YAAoB,EAAE,OAAe,EAAE,WAAmB,EAAE;IACzF,OAAO,iCAAyB,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AACvF,CAAC;AAED,SAAgB,qBAAqB,CAAC,YAAgC;IAClE,OAAO,iCAAyB,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;AACzE,CAAC;AAED,SAAgB,mBAAmB,CAAC,YAAoB;IACpD,OAAO,iCAAyB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;AACvE,CAAC"}