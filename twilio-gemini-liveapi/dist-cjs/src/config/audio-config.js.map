{"version": 3, "file": "audio-config.js", "sourceRoot": "", "sources": ["../../../src/config/audio-config.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,iEAAiE;;;;;;AAkXjE,0CAEC;AAED,kDAEC;AAED,oDAEC;AAED,8CAEC;AA9XD,qCAAkD;AAClD,gDAAwB;AACxB,4CAAyD;AAsEzD;;;GAGG;AACH,MAAa,kBAAkB;IACnB,WAAW,CAAS;IACpB,YAAY,CAAS;IACrB,UAAU,CAAS;IACnB,gBAAgB,CAAS;IACzB,gBAAgB,CAAS;IACzB,gBAAgB,CAAU;IAC1B,SAAS,CAAS;IAClB,UAAU,CAAa;IACvB,gBAAgB,CAAmB;IAE3C;QACI,wBAAwB;QACxB,IAAI,CAAC,WAAW,GAAG,IAAA,uBAAc,EAAC,mBAAmB,EAAE,WAAW,CAAW,CAAC;QAC9E,IAAI,CAAC,YAAY,GAAG,IAAA,uBAAc,EAAC,oBAAoB,EAAE,WAAW,CAAW,CAAC;QAChF,IAAI,CAAC,UAAU,GAAG,IAAA,uBAAc,EAAC,kBAAkB,EAAE,KAAK,CAAW,CAAC;QACtE,IAAI,CAAC,gBAAgB,GAAG,IAAA,uBAAc,EAAC,wBAAwB,EAAE,IAAI,CAAW,CAAC;QACjF,IAAI,CAAC,gBAAgB,GAAG,IAAA,uBAAc,EAAC,sBAAsB,EAAE,KAAK,CAAW,CAAC;QAEhF,uBAAuB;QACvB,IAAI,CAAC,gBAAgB,GAAG,IAAA,uBAAc,EAAC,wBAAwB,CAAuB,CAAC;QACvF,IAAI,CAAC,SAAS,GAAG,IAAA,uBAAc,EAAC,kBAAkB,CAAW,CAAC;QAE9D,4BAA4B;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE9C,oBAAoB;QACpB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,oBAAoB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;QAE/B,OAAO;YACH,QAAQ,EAAE;gBACN,OAAO,EAAE,IAAI,CAAC,gBAAgB,IAAI,GAAG,OAAO,YAAY;gBACxD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,OAAO,eAAe;gBAClE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,OAAO,eAAe;gBAClE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,OAAO,eAAe;aACrE;YACD,IAAI,EAAE;gBACF,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,GAAG,OAAO,iBAAiB;gBAClE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,GAAG,OAAO,oBAAoB;gBACnE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,GAAG,OAAO,oBAAoB;gBACnE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,GAAG,OAAO,oBAAoB;aACtE;YACD,QAAQ,EAAE;gBACN,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,GAAG,OAAO,eAAe;gBACpE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,OAAO,kBAAkB;gBACrE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,OAAO,kBAAkB;gBACrE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,OAAO,kBAAkB;aACxE;YACD,WAAW,EAAE;gBACT,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,OAAO,kBAAkB;gBAC1E,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,GAAG,OAAO,qBAAqB;gBAC3E,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,GAAG,OAAO,qBAAqB;gBAC3E,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,GAAG,OAAO,qBAAqB;aAC9E;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IACK,0BAA0B;QAC9B,OAAO;YACH,KAAK,EAAE;gBACH,WAAW,EAAE;oBACT,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,2BAA2B;oBACxC,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,CAAC;iBACd;gBACD,WAAW,EAAE;oBACT,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,2BAA2B;oBACxC,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,CAAC;iBACd;gBACD,KAAK,EAAE;oBACH,IAAI,EAAE,KAAK;oBACX,WAAW,EAAE,oBAAoB;oBACjC,UAAU,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;oBACvC,OAAO,EAAE,UAAU;oBACnB,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;iBACnB;aACJ;YACD,MAAM,EAAE;gBACJ,WAAW,EAAE;oBACT,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,2BAA2B;oBACxC,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,CAAC;iBACd;gBACD,WAAW,EAAE;oBACT,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,2BAA2B;oBACxC,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,CAAC;iBACd;gBACD,KAAK,EAAE;oBACH,IAAI,EAAE,KAAK;oBACX,WAAW,EAAE,yBAAyB;oBACtC,UAAU,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;oBAC9C,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;oBAChD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;iBACnB;aACJ;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAY,EAAE,WAAmB,SAAS;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,qBAAM,CAAC,IAAI,CAAC,uBAAuB,IAAI,EAAE,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,WAAmB,SAAS;QAC5C,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,WAAmB,SAAS;QACxC,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,WAAmB,SAAS;QAC5C,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,WAAmB,SAAS;QAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAChB,OAAO;YACH,KAAK,EAAE;gBACH,MAAM,EAAE,IAAI,CAAC,WAAW;gBACxB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC;aACrD;YACD,MAAM,EAAE;gBACJ,MAAM,EAAE,IAAI,CAAC,YAAY;gBACzB,UAAU,EAAE,IAAI,CAAC,gBAAgB;gBACjC,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;aACtD;YACD,MAAM,EAAE;gBACJ,UAAU,EAAE,IAAI,CAAC,gBAAgB;gBACjC,WAAW,EAAE,WAAW;gBACxB,YAAY,EAAE,WAAW;aAC5B;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAc;QACpC,MAAM,SAAS,GAA2B;YACtC,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,MAAM;YACnB,KAAK,EAAE,UAAU;YACjB,KAAK,EAAE,KAAK;SACf,CAAC;QAEF,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAc,EAAE,OAA2B,OAAO;QAChE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,MAAc,EAAE,OAA2B,OAAO;QACnE,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC,UAAU,CAAC;QAAA,CAAC;QAE5C,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,yDAAyD;YACzD,MAAM,UAAU,GAAG,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAC9E,OAAO,YAAY,CAAC,UAAU;iBACzB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,UAAU,CAAC;iBAClC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,YAAY,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,wBAAwB;QACpB,OAAO;YACH,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,MAAM;YACnE,sBAAsB,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM;YACvE,qBAAqB,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM;YACtE,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM;YACvE,gBAAgB,EAAE;gBACd,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM;gBACxD,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,KAAK,CAAC;gBAC7D,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM,EAAE,EAAE,CAAC;aACjE;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAgB;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;QACnF,OAAO,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,OAA2B;QACzC,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC;YAC9E,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAC3C,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC;YACjF,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAC7C,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACzC,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC3D,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACZ,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,qBAAqB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAC/D,sBAAsB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACjE,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;SAC3C,CAAC;IACN,CAAC;CACJ;AA9RD,gDA8RC;AAED,4BAA4B;AACf,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC;AAE3D,2BAA2B;AAC3B,SAAgB,eAAe,CAAC,IAAY,EAAE,WAAmB,SAAS;IACtE,OAAO,0BAAkB,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC9D,CAAC;AAED,SAAgB,mBAAmB,CAAC,WAAmB,SAAS;IAC5D,OAAO,0BAAkB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AAC5D,CAAC;AAED,SAAgB,oBAAoB;IAChC,OAAO,0BAAkB,CAAC,oBAAoB,EAAE,CAAC;AACrD,CAAC;AAED,SAAgB,iBAAiB,CAAC,MAAc,EAAE,OAA2B,OAAO;IAChF,OAAO,0BAAkB,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC9D,CAAC"}