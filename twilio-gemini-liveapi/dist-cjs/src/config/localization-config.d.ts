interface LocalizationVoiceConfig {
    incoming: string;
    outbound: string;
}
interface LanguageMapping {
    code: string;
    name: string;
    locale: string;
    voice: LocalizationVoiceConfig;
    transcription: string;
}
interface SupportedLanguage extends LanguageMapping {
    code: string;
}
/**
 * Localization Configuration Manager
 * Handles language settings, locale mappings, and text content
 */
export declare class LocalizationConfigManager {
    private defaultLanguage;
    private supportedLanguages;
    private enableMultiLanguage;
    private fallbackLanguage;
    private languageMappings;
    private defaultTexts;
    constructor();
    /**
     * Get language configuration
     */
    getLanguageConfig(languageCode: string): LanguageMapping;
    /**
     * Get voice for language and call type
     */
    getVoiceForLanguage(languageCode: string, callType?: 'incoming' | 'outbound'): string;
    /**
     * Get localized text
     */
    getLocalizedText(languageCode: string, textKey: string, fallback?: string): string;
    /**
     * Normalize language code
     */
    normalizeLanguageCode(languageCode: string | undefined): string;
    /**
     * Get all supported languages
     */
    getSupportedLanguages(): SupportedLanguage[];
    /**
     * Check if language is supported
     */
    isLanguageSupported(languageCode: string): boolean;
    /**
     * Get transcription language code
     */
    getTranscriptionLanguage(languageCode: string): string;
    /**
     * Get locale string for formatting
     */
    getLocale(languageCode: string): string;
    /**
     * Update language configuration at runtime
     */
    updateLanguageConfig(languageCode: string, config: Partial<LanguageMapping>): void;
    /**
     * Update localized text at runtime
     */
    updateLocalizedText(languageCode: string, textKey: string, value: string): void;
    /**
     * Get configuration summary
     */
    getConfigSummary(): {
        defaultLanguage: string;
        supportedLanguages: string[];
        enableMultiLanguage: boolean;
        fallbackLanguage: string;
        availableTexts: string[];
    };
}
export declare const localizationConfigManager: LocalizationConfigManager;
export declare function getLanguageConfig(languageCode: string): LanguageMapping;
export declare function getVoiceForLanguage(languageCode: string, callType?: 'incoming' | 'outbound'): string;
export declare function getLocalizedText(languageCode: string, textKey: string, fallback?: string): string;
export declare function normalizeLanguageCode(languageCode: string | undefined): string;
export declare function isLanguageSupported(languageCode: string): boolean;
export {};
//# sourceMappingURL=localization-config.d.ts.map