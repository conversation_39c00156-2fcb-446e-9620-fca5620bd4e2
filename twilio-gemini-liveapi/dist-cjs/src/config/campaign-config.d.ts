import type { CampaignScript } from '../types/global';
interface TransferData {
    transferNumber?: string;
    agentName?: string;
    warmTransferIntroductionAgent?: string;
    warmTransferIntroductionCustomer?: string;
    specialistNotAvailableMessage?: string;
}
interface OptionalField {
    field: string;
    disqualificationRule?: {
        condition: string;
    };
    invalidResponseHandlers?: Array<{
        condition: string;
    }>;
}
interface CustomerData {
    optionalFieldsPreTransfer?: OptionalField[];
}
interface AgentPersona {
    name: string;
    tone?: string;
    humanEmulation?: boolean;
    voice?: string;
    vocabularyRestrictions?: string[];
    recordedMessageConfirmation?: string;
}
interface ScriptSection {
    type: string;
    content: string;
}
interface Script {
    start: ScriptSection[];
}
interface CampaignScriptInternal {
    id: number | string;
    type: string;
    language?: string;
    category?: string;
    title: string;
    campaign: string;
    campaign_title?: string;
    agentPersona: AgentPersona;
    customerData?: CustomerData;
    transferData?: TransferData;
    script: Script;
    objectives?: any;
}
/**
 * Campaign Configuration Manager
 * Handles loading and managing campaign scripts from various sources
 */
export declare class CampaignConfigManager {
    private scriptsPath;
    private totalCampaigns;
    private defaultCampaignId;
    private enableCustomScripts;
    private scriptCache;
    private cacheTimeout;
    constructor();
    /**
     * Load campaign script from file system
     */
    private loadCampaignFromFile;
    /**
     * Apply configuration overrides to campaign script
     */
    private applyCampaignConfigOverrides;
    /**
     * Get campaign script with caching
     */
    getCampaignScript(campaignId: number | string, type?: 'outbound' | 'inbound', useCache?: boolean): Promise<CampaignScript | null>;
    /**
     * Convert internal campaign script to external type
     */
    private convertToExternalType;
    /**
     * Load campaign from database using Supabase
     */
    private loadCampaignFromDatabase;
    /**
     * Create default campaign template
     */
    private createDefaultCampaignTemplate;
    /**
     * Get all available campaigns
     */
    getAllCampaigns(type?: 'outbound' | 'inbound'): Promise<CampaignScript[]>;
    /**
     * Validate campaign script structure
     */
    validateCampaignScript(script: CampaignScriptInternal): boolean;
    /**
     * Clear cache
     */
    clearCache(): void;
    /**
     * Invalidate specific cache entry
     */
    invalidateCache(campaignId: number | string, type?: 'outbound' | 'inbound'): boolean;
    /**
     * Invalidate all cache entries for a specific campaign ID
     */
    invalidateCampaignCache(campaignId: number | string): boolean;
    /**
     * Get cache statistics
     */
    getCacheStats(): {
        size: number;
        keys: string[];
        timeout: number;
        maxSize: number;
    };
}
export declare const campaignConfigManager: CampaignConfigManager;
export declare function getCampaignScript(campaignId: number | string, type?: 'outbound' | 'inbound'): Promise<CampaignScript | null>;
export declare function getAllCampaigns(type?: 'outbound' | 'inbound'): Promise<CampaignScript[]>;
export declare function validateCampaignScript(script: CampaignScriptInternal): boolean;
export {};
//# sourceMappingURL=campaign-config.d.ts.map