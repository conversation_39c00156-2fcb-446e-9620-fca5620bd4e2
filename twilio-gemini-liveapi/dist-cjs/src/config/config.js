"use strict";
// Comprehensive Configuration System for Twilio Gemini Project
// Centralizes all configuration with environment variable support and validation
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
exports.validateConfig = validateConfig;
exports.getConfigValue = getConfigValue;
exports.getSafeConfig = getSafeConfig;
exports.getConfigSummary = getConfigSummary;
exports.validateConfigSection = validateConfigSection;
const dotenv_stub_1 = __importDefault(require("../utils/dotenv-stub"));
const logger_1 = require("../utils/logger");
const validator_1 = require("./validator");
const environment_1 = require("./sections/environment");
const server_1 = require("./sections/server");
const auth_1 = require("./sections/auth");
const twilio_1 = require("./sections/twilio");
const ai_1 = require("./sections/ai");
const audio_1 = require("./sections/audio");
const websocket_1 = require("./sections/websocket");
const transcription_1 = require("./sections/transcription");
const campaigns_1 = require("./sections/campaigns");
const localization_1 = require("./sections/localization");
const voices_1 = require("./sections/voices");
const business_1 = require("./sections/business");
const prompts_1 = require("./sections/prompts");
const security_1 = require("./sections/security");
const performance_1 = require("./sections/performance");
const timeouts_1 = require("./sections/timeouts");
const limits_1 = require("./sections/limits");
dotenv_stub_1.default.config();
if (process.env.NODE_ENV === 'test') {
    process.env.GEMINI_API_KEY ??= 'test-api-key';
    process.env.TWILIO_ACCOUNT_SID ??= 'ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX';
    process.env.TWILIO_AUTH_TOKEN ??= 'test-auth-token';
    process.env.PUBLIC_URL ??= 'http://localhost:3101';
}
exports.config = {
    environment: environment_1.environment,
    server: server_1.server,
    auth: auth_1.auth,
    twilio: twilio_1.twilio,
    ai: ai_1.ai,
    audio: audio_1.audio,
    websocket: websocket_1.websocket,
    transcription: transcription_1.transcription,
    campaigns: campaigns_1.campaigns,
    localization: localization_1.localization,
    voices: voices_1.voices,
    business: business_1.business,
    prompts: prompts_1.prompts,
    security: security_1.security,
    performance: performance_1.performance,
    timeouts: timeouts_1.timeouts,
    limits: limits_1.limits
};
function validateConfig() {
    const errors = [];
    const warnings = [];
    try {
        try {
            validator_1.ConfigValidator.validateRequired(exports.config.auth.gemini.apiKey, 'GEMINI_API_KEY');
        }
        catch (e) {
            errors.push(e instanceof Error ? e.message : String(e));
        }
        try {
            validator_1.ConfigValidator.validateRequired(exports.config.auth.twilio.accountSid, 'TWILIO_ACCOUNT_SID');
        }
        catch (e) {
            errors.push(e instanceof Error ? e.message : String(e));
        }
        try {
            validator_1.ConfigValidator.validateRequired(exports.config.auth.twilio.authToken, 'TWILIO_AUTH_TOKEN');
        }
        catch (e) {
            errors.push(e instanceof Error ? e.message : String(e));
        }
        try {
            validator_1.ConfigValidator.validateUrl(exports.config.server.publicUrl, 'PUBLIC_URL', true);
        }
        catch (e) {
            errors.push(e instanceof Error ? e.message : String(e));
        }
        if (!exports.config.auth.deepgram.apiKey) {
            warnings.push('DEEPGRAM_API_KEY not set - transcription features will be limited');
        }
        if (!exports.config.auth.openai.apiKey) {
            warnings.push('OPENAI_API_KEY not set - OpenAI features will be unavailable');
        }
        if (exports.config.server.port < 1 || exports.config.server.port > 65535) {
            errors.push(`Invalid PORT: ${exports.config.server.port} (must be 1-65535)`);
        }
        if (exports.config.ai.gemini.temperature < 0 || exports.config.ai.gemini.temperature > 2) {
            errors.push(`Invalid GEMINI_TEMPERATURE: ${exports.config.ai.gemini.temperature} (must be 0-2)`);
        }
        if (exports.config.ai.gemini.topP < 0 || exports.config.ai.gemini.topP > 1) {
            errors.push(`Invalid GEMINI_TOP_P: ${exports.config.ai.gemini.topP} (must be 0-1)`);
        }
        if (exports.config.audio.sampleRate < 8000 || exports.config.audio.sampleRate > 48000) {
            warnings.push(`Unusual SAMPLE_RATE: ${exports.config.audio.sampleRate} (recommended: 8000-48000)`);
        }
        const supportedLangs = exports.config.localization.supportedLanguages;
        if (!supportedLangs.includes(exports.config.localization.defaultLanguage)) {
            errors.push(`DEFAULT_LANGUAGE '${exports.config.localization.defaultLanguage}' not in SUPPORTED_LANGUAGES`);
        }
        if (exports.config.business.validation.maxVehicles < 1 || exports.config.business.validation.maxVehicles > 50) {
            warnings.push(`Unusual MAX_VEHICLES: ${exports.config.business.validation.maxVehicles} (recommended: 1-50)`);
        }
        if (exports.config.websocket.heartbeatInterval < 1000 || exports.config.websocket.heartbeatInterval > 120000) {
            errors.push(`Invalid HEARTBEAT_INTERVAL: ${exports.config.websocket.heartbeatInterval} (must be 1000-120000)`);
        }
        if (exports.config.websocket.heartbeatTimeout < 1000 || exports.config.websocket.heartbeatTimeout > 60000) {
            errors.push(`Invalid HEARTBEAT_TIMEOUT: ${exports.config.websocket.heartbeatTimeout} (must be 1000-60000)`);
        }
        if (errors.length > 0) {
            logger_1.configLogger.error('Configuration validation failed', {});
            errors.forEach(error => logger_1.configLogger.error(error));
            throw new Error(`Configuration validation failed with ${errors.length} error(s)`);
        }
        if (warnings.length > 0) {
            logger_1.configLogger.warn('Configuration warnings', {});
            warnings.forEach(warning => logger_1.configLogger.warn(warning));
        }
        logger_1.configLogger.info('Configuration validation passed');
        if (warnings.length > 0) {
            logger_1.configLogger.info(`(${warnings.length} warning(s) - see above)`);
        }
        return true;
    }
    catch (error) {
        if (errors.length === 0) {
            logger_1.configLogger.error('Configuration validation failed', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
        throw error;
    }
}
function getConfigValue(path, fallback = null) {
    const keys = path.split('.');
    let value = exports.config;
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        }
        else {
            return fallback;
        }
    }
    return value;
}
function getSafeConfig() {
    const safeConfig = JSON.parse(JSON.stringify(exports.config));
    if (safeConfig.auth) {
        Object.keys(safeConfig.auth).forEach(provider => {
            if (safeConfig.auth[provider].apiKey) {
                safeConfig.auth[provider].apiKey = '***REDACTED***';
            }
            if (safeConfig.auth[provider].authToken) {
                safeConfig.auth[provider].authToken = '***REDACTED***';
            }
        });
    }
    return safeConfig;
}
function getConfigSummary() {
    return {
        environment: exports.config.environment.nodeEnv,
        server: {
            port: exports.config.server.port,
            publicUrl: exports.config.server.publicUrl ? 'SET' : 'NOT SET'
        },
        auth: {
            gemini: exports.config.auth.gemini.apiKey ? 'SET' : 'NOT SET',
            twilio: exports.config.auth.twilio.accountSid ? 'SET' : 'NOT SET',
            openai: exports.config.auth.openai.apiKey ? 'SET' : 'NOT SET',
            deepgram: exports.config.auth.deepgram.apiKey ? 'SET' : 'NOT SET'
        },
        ai: {
            defaultModel: exports.config.ai.gemini.defaultModel,
            defaultVoice: exports.config.ai.gemini.defaultVoice,
            availableModels: exports.config.ai.gemini.availableModels.length
        },
        campaigns: {
            totalCampaigns: exports.config.campaigns.totalCampaigns,
            scriptsPath: exports.config.campaigns.scriptsPath ? 'SET' : 'NOT SET',
            enableCustomScripts: exports.config.campaigns.enableCustomScripts
        },
        localization: {
            defaultLanguage: exports.config.localization.defaultLanguage,
            supportedLanguages: exports.config.localization.supportedLanguages,
            enableMultiLanguage: exports.config.localization.enableMultiLanguage
        },
        performance: {
            enableCaching: exports.config.performance.enableCaching,
            maxConcurrentCalls: exports.config.performance.maxConcurrentCalls,
            enableMetrics: exports.config.performance.enableMetrics
        }
    };
}
function validateConfigSection(section) {
    const validators = {
        auth: () => {
            validator_1.ConfigValidator.validateRequired(exports.config.auth.gemini.apiKey, 'GEMINI_API_KEY');
            validator_1.ConfigValidator.validateRequired(exports.config.auth.twilio.accountSid, 'TWILIO_ACCOUNT_SID');
            validator_1.ConfigValidator.validateRequired(exports.config.auth.twilio.authToken, 'TWILIO_AUTH_TOKEN');
        },
        server: () => {
            validator_1.ConfigValidator.validateUrl(exports.config.server.publicUrl, 'PUBLIC_URL', true);
            validator_1.ConfigValidator.validatePort(String(exports.config.server.port), 'PORT');
        },
        ai: () => {
            validator_1.ConfigValidator.validateNumber(String(exports.config.ai.gemini.temperature), 'GEMINI_TEMPERATURE', 0, 2);
            validator_1.ConfigValidator.validateNumber(String(exports.config.ai.gemini.topP), 'GEMINI_TOP_P', 0, 1);
            validator_1.ConfigValidator.validateNumber(String(exports.config.ai.gemini.topK), 'GEMINI_TOP_K', 1, 100);
        },
        audio: () => {
            validator_1.ConfigValidator.validateNumber(String(exports.config.audio.sampleRate), 'SAMPLE_RATE', 8000, 48000);
            validator_1.ConfigValidator.validateNumber(String(exports.config.audio.twilioSampleRate), 'TWILIO_SAMPLE_RATE', 8000, 48000);
        },
        websocket: () => {
            validator_1.ConfigValidator.validateNumber(String(exports.config.websocket.heartbeatInterval), 'HEARTBEAT_INTERVAL', 1000, 120000);
            validator_1.ConfigValidator.validateNumber(String(exports.config.websocket.heartbeatTimeout), 'HEARTBEAT_TIMEOUT', 1000, 60000);
        }
    };
    if (validators[section]) {
        try {
            validators[section]();
            return { valid: true };
        }
        catch (error) {
            return { valid: false, error: error instanceof Error ? error.message : String(error) };
        }
    }
    return { valid: false, error: `Unknown configuration section: ${section}` };
}
exports.default = exports.config;
//# sourceMappingURL=config.js.map