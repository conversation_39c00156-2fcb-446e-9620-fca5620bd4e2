interface CallTimeouts {
    default: number;
    intro: number;
    response: number;
    transfer: number;
    recording: number;
    silence: number;
}
interface TransferConfig {
    defaultNumber: string;
    defaultAgentName: string;
    timeout: number;
    maxAttempts: number;
    retryDelay: number;
    warmTransferEnabled: boolean;
    coldTransferEnabled: boolean;
}
interface PerformanceConfig {
    maxConcurrentCalls: number;
    callQueueSize: number;
    maxCallDuration: number;
    enableCallRecording: boolean;
    enableCallTranscription: boolean;
    enableMetrics: boolean;
}
interface RetryConfig {
    maxRetries: number;
    retryDelay: number;
    backoffMultiplier: number;
    maxRetryDelay: number;
    enableExponentialBackoff: boolean;
}
import type { ValidationResult } from '../types/shared-types';
interface CallHistoryEntry {
    phoneNumber: string;
    timestamp: number;
}
type ConfigSection = 'timeouts' | 'validation' | 'transfer' | 'campaign' | 'performance' | 'retry' | 'security';
/**
 * Business Configuration Manager
 * Handles business logic constants, validation rules, and operational parameters
 */
export declare class BusinessConfigManager {
    private callTimeouts;
    private validationRules;
    private transferConfig;
    private campaignConfig;
    private performanceConfig;
    private retryConfig;
    private securityConfig;
    constructor();
    /**
     * Get call timeout for specific type
     */
    getCallTimeout(type?: keyof CallTimeouts): number;
    /**
     * Validate vehicle count
     */
    validateVehicleCount(count: string | number): ValidationResult;
    /**
     * Validate claims count
     */
    validateClaimsCount(count: string | number): ValidationResult;
    /**
     * Validate vehicle year
     */
    validateVehicleYear(year: string | number): ValidationResult;
    /**
     * Validate phone number
     */
    validatePhoneNumber(phoneNumber: string): ValidationResult;
    /**
     * Validate name
     */
    validateName(name: string): ValidationResult;
    /**
     * Get transfer configuration
     */
    getTransferConfig(campaignId?: number | null): TransferConfig;
    /**
     * Check if call should be rate limited
     */
    shouldRateLimit(phoneNumber: string, callHistory?: CallHistoryEntry[]): boolean;
    /**
     * Get retry configuration for operation
     */
    getRetryConfig(operation?: 'default' | 'api_call' | 'database' | 'transfer'): RetryConfig;
    /**
     * Get performance limits
     */
    getPerformanceLimits(): PerformanceConfig;
    /**
     * Update business configuration at runtime
     */
    updateBusinessConfig(section: ConfigSection, updates: any): boolean;
    /**
     * Get configuration summary
     */
    getConfigSummary(): {
        callTimeouts: string[];
        validationRules: string[];
        transferConfig: {
            defaultNumber: string;
            defaultAgentName: string;
            warmTransferEnabled: boolean;
        };
        performanceLimits: {
            maxConcurrentCalls: number;
            maxCallDuration: number;
        };
        securityFeatures: {
            recordingConfirmation: boolean;
            contentFiltering: boolean;
            rateLimiting: boolean;
        };
    };
}
export declare const businessConfigManager: BusinessConfigManager;
export declare function getCallTimeout(type?: keyof CallTimeouts): number;
export declare function validateVehicleCount(count: string | number): ValidationResult;
export declare function validateClaimsCount(count: string | number): ValidationResult;
export declare function validateVehicleYear(year: string | number): ValidationResult;
export declare function getTransferConfig(campaignId?: number | null): TransferConfig;
export declare function shouldRateLimit(phoneNumber: string, callHistory?: CallHistoryEntry[]): boolean;
export {};
//# sourceMappingURL=business-config.d.ts.map