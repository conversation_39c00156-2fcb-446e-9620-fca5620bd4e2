"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigValidator = void 0;
class ConfigValidator {
    static validateRequired(value, name) {
        if (!value) {
            throw new Error(`Required configuration missing: ${name}`);
        }
        return value;
    }
    static validateUrl(value, name, required = false) {
        if (!value && required) {
            throw new Error(`Required URL configuration missing: ${name}`);
        }
        if (value && !value.match(/^https?:\/\/.+/)) {
            throw new Error(`Invalid URL format for ${name}: ${value}`);
        }
        return value;
    }
    static validatePort(value, name, defaultValue = 3000) {
        const port = parseInt(value || '') || defaultValue;
        if (port < 1 || port > 65535) {
            throw new Error(`Invalid port for ${name}: ${port}`);
        }
        return port;
    }
    static validateEnum(value, validValues, name, defaultValue = null) {
        if (!value && defaultValue) {
            return defaultValue;
        }
        if (!value || !validValues.includes(value)) {
            throw new Error(`Invalid value for ${name}: ${value}. Valid values: ${validValues.join(', ')}`);
        }
        return value;
    }
    static validateNumber(value, name, min = null, max = null, defaultValue = null) {
        if (!value && defaultValue !== null) {
            return defaultValue;
        }
        const num = parseFloat(value || '');
        if (isNaN(num)) {
            throw new Error(`Invalid number for ${name}: ${value}`);
        }
        if (min !== null && num < min) {
            throw new Error(`${name} must be >= ${min}, got ${num}`);
        }
        if (max !== null && num > max) {
            throw new Error(`${name} must be <= ${max}, got ${num}`);
        }
        return num;
    }
}
exports.ConfigValidator = ConfigValidator;
//# sourceMappingURL=validator.js.map