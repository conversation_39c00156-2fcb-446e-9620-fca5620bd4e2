{"version": 3, "file": "validator.js", "sourceRoot": "", "sources": ["../../../src/config/validator.ts"], "names": [], "mappings": ";;;AAAA,MAAa,eAAe;IACxB,MAAM,CAAC,gBAAgB,CAAC,KAAyB,EAAE,IAAY;QAC3D,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,EAAE,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,KAAyB,EAAE,IAAY,EAAE,WAAoB,KAAK;QACjF,IAAI,CAAC,KAAK,IAAI,QAAQ,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,uCAAuC,IAAI,EAAE,CAAC,CAAC;QACnE,CAAC;QACD,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,KAAyB,EAAE,IAAY,EAAE,eAAuB,IAAI;QACpF,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,YAAY,CAAC;QACnD,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,KAAyB,EAAE,WAAqB,EAAE,IAAY,EAAE,eAA8B,IAAI;QAClH,IAAI,CAAC,KAAK,IAAI,YAAY,EAAE,CAAC;YACzB,OAAO,YAAY,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,KAAK,KAAK,mBAAmB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpG,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,KAAyB,EAAE,IAAY,EAAE,MAAqB,IAAI,EAAE,MAAqB,IAAI,EAAE,eAA8B,IAAI;QACnJ,IAAI,CAAC,KAAK,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;YAClC,OAAO,YAAY,CAAC;QACxB,CAAC;QACD,MAAM,GAAG,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;QACpC,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,eAAe,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,eAAe,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC;QAC7D,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;CACJ;AApDD,0CAoDC"}