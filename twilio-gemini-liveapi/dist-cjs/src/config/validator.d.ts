export declare class ConfigValidator {
    static validateRequired(value: string | undefined, name: string): string;
    static validateUrl(value: string | undefined, name: string, required?: boolean): string | undefined;
    static validatePort(value: string | undefined, name: string, defaultValue?: number): number;
    static validateEnum(value: string | undefined, validValues: string[], name: string, defaultValue?: string | null): string;
    static validateNumber(value: string | undefined, name: string, min?: number | null, max?: number | null, defaultValue?: number | null): number;
}
//# sourceMappingURL=validator.d.ts.map