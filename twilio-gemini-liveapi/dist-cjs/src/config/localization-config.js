"use strict";
// Localization Configuration System
// Replaces hardcoded language codes and locale settings
Object.defineProperty(exports, "__esModule", { value: true });
exports.localizationConfigManager = exports.LocalizationConfigManager = void 0;
exports.getLanguageConfig = getLanguageConfig;
exports.getVoiceForLanguage = getVoiceForLanguage;
exports.getLocalizedText = getLocalizedText;
exports.normalizeLanguageCode = normalizeLanguageCode;
exports.isLanguageSupported = isLanguageSupported;
const config_1 = require("./config");
/**
 * Localization Configuration Manager
 * Handles language settings, locale mappings, and text content
 */
class LocalizationConfigManager {
    defaultLanguage;
    supportedLanguages;
    enableMultiLanguage;
    fallbackLanguage;
    languageMappings;
    defaultTexts;
    constructor() {
        this.defaultLanguage = (0, config_1.getConfigValue)('localization.defaultLanguage', 'en');
        this.supportedLanguages = (0, config_1.getConfigValue)('localization.supportedLanguages', ['en', 'es', 'cz']);
        this.enableMultiLanguage = (0, config_1.getConfigValue)('localization.enableMultiLanguage', true);
        this.fallbackLanguage = (0, config_1.getConfigValue)('localization.fallbackLanguage', 'en');
        // Language mappings
        this.languageMappings = {
            'en': {
                code: 'en',
                name: 'English',
                locale: 'en-US',
                voice: {
                    incoming: (0, config_1.getConfigValue)('voices.defaultVoiceMapping.en.incoming', 'empathetic'),
                    outbound: (0, config_1.getConfigValue)('voices.defaultVoiceMapping.en.outbound', 'relaxed')
                },
                transcription: 'en'
            },
            'es': {
                code: 'es',
                name: 'Spanish',
                locale: 'es-ES',
                voice: {
                    incoming: (0, config_1.getConfigValue)('voices.defaultVoiceMapping.es.incoming', 'empathetic'),
                    outbound: (0, config_1.getConfigValue)('voices.defaultVoiceMapping.es.outbound', 'energetic')
                },
                transcription: 'es'
            },
            'cz': {
                code: 'cz',
                name: 'Czech',
                locale: 'cs-CZ',
                voice: {
                    incoming: (0, config_1.getConfigValue)('voices.defaultVoiceMapping.cz.incoming', 'professional'),
                    outbound: (0, config_1.getConfigValue)('voices.defaultVoiceMapping.cz.outbound', 'authoritative')
                },
                transcription: 'cs'
            }
        };
        // Default text content (configurable)
        this.defaultTexts = {
            en: {
                recordingConfirmation: (0, config_1.getConfigValue)('security.recordingConfirmationMessage', 'This call is recorded for quality assurance. Is it okay to continue?'),
                transferMessage: 'I will now transfer you to an agent. Please hold.',
                agentBusyMessage: 'All agents are busy. We will call you back.',
                defaultGreeting: 'Hello, how can I help you today?',
                validationMessages: {
                    vehicleYear: 'What year was that vehicle? Please provide a 4-digit year.',
                    vehicleMake: 'And the make?',
                    vehicleModel: 'And the model?',
                    vehicleCount: 'Please provide a valid number of vehicles (between 1 and 9).',
                    claimsCount: 'Please provide a valid number of claims.'
                }
            },
            es: {
                recordingConfirmation: 'Esta llamada se graba para garantía de calidad. ¿Está bien continuar?',
                transferMessage: 'Ahora lo transferiré a un agente. Por favor espere.',
                agentBusyMessage: 'Todos los agentes están ocupados. Le devolveremos la llamada.',
                defaultGreeting: 'Hola, ¿cómo puedo ayudarle hoy?',
                validationMessages: {
                    vehicleYear: '¿De qué año es ese vehículo? Proporcione un año de 4 dígitos.',
                    vehicleMake: '¿Y la marca?',
                    vehicleModel: '¿Y el modelo?',
                    vehicleCount: 'Proporcione un número válido de vehículos (entre 1 y 9).',
                    claimsCount: 'Proporcione un número válido de reclamaciones.'
                }
            },
            cz: {
                recordingConfirmation: 'Tento hovor je nahráván pro zajištění kvality. Je v pořádku pokračovat?',
                transferMessage: 'Nyní vás přepojím na agenta. Prosím čekejte.',
                agentBusyMessage: 'Všichni agenti jsou zaneprázdněni. Zavoláme vám zpět.',
                defaultGreeting: 'Dobrý den, jak vám mohu dnes pomoci?',
                validationMessages: {
                    vehicleYear: 'Jakého roku je to vozidlo? Uveďte prosím 4místný rok.',
                    vehicleMake: 'A značka?',
                    vehicleModel: 'A model?',
                    vehicleCount: 'Uveďte prosím platný počet vozidel (mezi 1 a 9).',
                    claimsCount: 'Uveďte prosím platný počet pojistných událostí.'
                }
            }
        };
    }
    /**
     * Get language configuration
     */
    getLanguageConfig(languageCode) {
        const normalizedCode = this.normalizeLanguageCode(languageCode);
        return this.languageMappings[normalizedCode] || this.languageMappings[this.fallbackLanguage];
    }
    /**
     * Get voice for language and call type
     */
    getVoiceForLanguage(languageCode, callType = 'outbound') {
        const langConfig = this.getLanguageConfig(languageCode);
        return langConfig.voice[callType] || langConfig.voice.outbound;
    }
    /**
     * Get localized text
     */
    getLocalizedText(languageCode, textKey, fallback = '') {
        const normalizedCode = this.normalizeLanguageCode(languageCode);
        const texts = this.defaultTexts[normalizedCode] || this.defaultTexts[this.fallbackLanguage];
        // Support nested keys like 'validationMessages.vehicleYear'
        const keys = textKey.split('.');
        let value = texts;
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            }
            else {
                return fallback;
            }
        }
        return (typeof value === 'string' ? value : fallback);
    }
    /**
     * Normalize language code
     */
    normalizeLanguageCode(languageCode) {
        if (!languageCode) {
            return this.defaultLanguage;
        }
        // Handle various formats: 'en-US' -> 'en', 'EN' -> 'en'
        const normalized = languageCode.toLowerCase().split('-')[0];
        // Check if supported
        if (this.supportedLanguages.includes(normalized)) {
            return normalized;
        }
        return this.fallbackLanguage;
    }
    /**
     * Get all supported languages
     */
    getSupportedLanguages() {
        return this.supportedLanguages.map(code => ({
            ...this.languageMappings[code]
        }));
    }
    /**
     * Check if language is supported
     */
    isLanguageSupported(languageCode) {
        const normalized = this.normalizeLanguageCode(languageCode);
        return this.supportedLanguages.includes(normalized);
    }
    /**
     * Get transcription language code
     */
    getTranscriptionLanguage(languageCode) {
        const langConfig = this.getLanguageConfig(languageCode);
        return langConfig.transcription || this.fallbackLanguage;
    }
    /**
     * Get locale string for formatting
     */
    getLocale(languageCode) {
        const langConfig = this.getLanguageConfig(languageCode);
        return langConfig.locale || 'en-US';
    }
    /**
     * Update language configuration at runtime
     */
    updateLanguageConfig(languageCode, config) {
        const normalizedCode = this.normalizeLanguageCode(languageCode);
        if (this.languageMappings[normalizedCode]) {
            this.languageMappings[normalizedCode] = {
                ...this.languageMappings[normalizedCode],
                ...config
            };
        }
    }
    /**
     * Update localized text at runtime
     */
    updateLocalizedText(languageCode, textKey, value) {
        const normalizedCode = this.normalizeLanguageCode(languageCode);
        if (!this.defaultTexts[normalizedCode]) {
            this.defaultTexts[normalizedCode] = {};
        }
        // Support nested keys
        const keys = textKey.split('.');
        let target = this.defaultTexts[normalizedCode];
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!target[key] || typeof target[key] !== 'object') {
                target[key] = {};
            }
            target = target[key];
        }
        target[keys[keys.length - 1]] = value;
    }
    /**
     * Get configuration summary
     */
    getConfigSummary() {
        return {
            defaultLanguage: this.defaultLanguage,
            supportedLanguages: this.supportedLanguages,
            enableMultiLanguage: this.enableMultiLanguage,
            fallbackLanguage: this.fallbackLanguage,
            availableTexts: Object.keys(this.defaultTexts)
        };
    }
}
exports.LocalizationConfigManager = LocalizationConfigManager;
// Export singleton instance
exports.localizationConfigManager = new LocalizationConfigManager();
// Export utility functions
function getLanguageConfig(languageCode) {
    return exports.localizationConfigManager.getLanguageConfig(languageCode);
}
function getVoiceForLanguage(languageCode, callType = 'outbound') {
    return exports.localizationConfigManager.getVoiceForLanguage(languageCode, callType);
}
function getLocalizedText(languageCode, textKey, fallback = '') {
    return exports.localizationConfigManager.getLocalizedText(languageCode, textKey, fallback);
}
function normalizeLanguageCode(languageCode) {
    return exports.localizationConfigManager.normalizeLanguageCode(languageCode);
}
function isLanguageSupported(languageCode) {
    return exports.localizationConfigManager.isLanguageSupported(languageCode);
}
//# sourceMappingURL=localization-config.js.map