import type { AppConfig } from '../types/global';
export declare const config: AppConfig;
export declare function validateConfig(): boolean;
export declare function getConfigValue<T = unknown>(path: string, fallback?: T | null): T | null;
export declare function getSafeConfig(): Partial<AppConfig>;
export declare function getConfigSummary(): any;
export declare function validateConfigSection(section: string): {
    valid: boolean;
    error?: string;
};
export default config;
//# sourceMappingURL=config.d.ts.map