{"version": 3, "file": "session-lifecycle-routes.js", "sourceRoot": "", "sources": ["../../../src/api/session-lifecycle-routes.ts"], "names": [], "mappings": ";;;;;AAqBA,wEA0PC;AA7QD,iEAA6D;AAC7D,oDAA4B;AAC5B,6CAA0C;AAiB1C,SAAgB,8BAA8B,CAAC,OAAwB,EAAE,IAAkB;IACvF,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,MAAM,EACF,cAAc,EACd,cAAc,EACd,iBAAiB,EACjB,gBAAgB,EAChB,eAAe,EACf,cAAc,EACd,yBAAyB,EAC5B,GAAG,IAAI,CAAC;IAET,yBAAyB;IACzB,OAAO,CAAC,IAAI,CAA+B,uBAAuB,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;QAC1F,IAAI,CAAC;YACD,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YAEnC,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,cAAc,EAAE,CAAC;gBACjB,0CAA0C;gBAC1C,IAAI,cAAc,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5C,MAAM,cAAc,CAAC,eAAe,CAAC,OAAO,EAAE,cAAc,EAAE,yBAAyB,CAAC,CAAC;oBACzF,gEAAgE;oBAChE,UAAU,CAAC,GAAG,EAAE;wBACZ,gBAAgB,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,CAAC,CAAC;oBACrF,CAAC,EAAE,KAAK,CAAC,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACJ,gBAAgB,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,CAAC,CAAC;gBACrF,CAAC;gBAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACJ,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;YAC5D,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,yBAAyB;IACzB,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,KAAK,EAAE,QAAwB,EAAE,MAAoB,EAAE,EAAE;QAC5F,MAAM,OAAO,GAAG;YACZ,iBAAiB,EAAE,iBAAiB,CAAC,IAAI;YACzC,iBAAiB,EAAE,EAAW;YAC9B,YAAY,EAAE,cAAc,CAAC,eAAe,EAAE;SACjD,CAAC;QAEF,yCAAyC;QACzC,KAAK,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YAChF,MAAM,cAAc,GAAG,cAAc,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACnE,MAAM,cAAc,GAAG,cAAc,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEnE,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBAC3B,SAAS;gBACT,WAAW,EAAE,cAAc,CAAC,WAAW,IAAI,SAAS;gBACpD,QAAQ,EAAE,cAAc,CAAC,eAAe;gBACxC,SAAS,EAAE,cAAc,EAAE,SAAS;gBACpC,YAAY,EAAE,cAAc,EAAE,YAAY;gBAC1C,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,IAAI,CAAC;gBACvD,YAAY,EAAE,cAAc,EAAE,YAAY,IAAI,CAAC;gBAC/C,aAAa,EAAE,cAAc,EAAE,aAAa,IAAI,CAAC;gBACjD,cAAc;aACjB,CAAC,CAAC;QACP,CAAC;QAED,OAAO;YACH,OAAO,EAAE,IAAI;YACb,OAAO;SACV,CAAC;IACN,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,OAAO,CAAC,IAAI,CAAoC,gCAAgC,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;QACxG,IAAI,CAAC;YACD,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YACnC,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAEtD,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;YAC5D,CAAC;YAED,2CAA2C;YAC3C,cAAc,CAAC,sBAAsB,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YAElE,mBAAmB;YACnB,UAAU,CAAC,GAAG,EAAE;gBACZ,cAAc,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YAC9D,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,oBAAoB;gBAC7B,OAAO;aACV,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAG,KAAe,CAAC,OAAO;aAClC,CAAC;QACN,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,yCAAyC;IACzC,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACvF,IAAI,CAAC;YACD,MAAM,cAAc,GAAG,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACpF,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,cAAc;gBACxB,KAAK,EAAE,cAAc,CAAC,MAAM;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAA8B,iCAAiC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QACjG,IAAI,CAAC;YACD,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YAErC,IAAI,CAAC,8BAAa,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/E,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;YAClE,CAAC;YAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC5F,MAAM,cAAc,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC7F,MAAM,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAEzF,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,SAAS;gBACT,OAAO,EAAE,aAAa;gBACtB,QAAQ,EAAE,cAAc;gBACxB,OAAO,EAAE,aAAa;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,IAAI,CACR,8BAA8B,EAC9B,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QACzB,IAAI,CAAC;YACD,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YACrC,MAAM,EAAE,MAAM,GAAG,aAAa,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;YAEhD,IAAI,CAAC,8BAAa,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/E,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;YAClE,CAAC;YAED,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACxD,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC;YAC1D,CAAC;YAED,IAAI,gBAAgB,EAAE,CAAC;gBACnB,IAAI,CAAC;oBACD,kDAAkD;oBAClD,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC7B,IAAI,CAAC;4BACD,MAAM,kBAAkB,GAAG,eAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;4BACzD,MAAM,iBAAiB,GAAG,eAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;4BAEvD,IAAI,kBAAkB,IAAI,iBAAiB,EAAE,CAAC;gCAC1C,MAAM,MAAM,GAAG,IAAA,gBAAM,EAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;gCAC7D,wEAAwE;gCACxE,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;gCAC9D,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,uCAAuC,CAAC,CAAC;4BACzE,CAAC;wBACL,CAAC;wBAAC,OAAO,WAAW,EAAE,CAAC;4BACnB,OAAO,CAAC,KAAK,CAAC,MAAM,SAAS,oCAAoC,EAAE,WAAW,CAAC,CAAC;4BAChF,iEAAiE;wBACrE,CAAC;oBACL,CAAC;oBAED,kBAAkB;oBAClB,MAAM,gBAAgB,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;oBACrE,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAEpC,OAAO;wBACH,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,oCAAoC;wBAC7C,SAAS;wBACT,MAAM;wBACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACtC,CAAC;gBACN,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,MAAM,SAAS,yBAAyB,EAAE,KAAK,CAAC,CAAC;oBAC/D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;gBAC9D,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,iCAAiC,EAAE,CAAC;YACxE,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,IAAI,CAA8B,kCAAkC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QACnG,IAAI,CAAC;YACD,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YAErC,IAAI,CAAC,8BAAa,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/E,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;YAClE,CAAC;YAED,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACxD,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC;YAC1D,CAAC;YAED,IAAI,cAAc,EAAE,CAAC;gBACjB,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;gBAE/F,OAAO;oBACH,OAAO;oBACP,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,gCAAgC,CAAC,CAAC,CAAC,2BAA2B;oBACjF,SAAS;oBACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;YACtE,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;AACvE,CAAC;AAED,kCAAkC;AAClC,SAAS,gBAAgB,CACrB,SAAiB,EACjB,IAIC;IAED,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC;IAEnE,IAAI,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,0BAA0B,CAAC,CAAC;QAExD,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,cAAc,EAAE,CAAC;YACjB,uBAAuB;YACvB,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;gBAC/B,IAAI,CAAC;oBACD,cAAc,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;gBACzC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,IAAI,CAAC,OAAO,SAAS,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBAC3E,CAAC;YACL,CAAC;YAED,2BAA2B;YAC3B,IAAI,cAAc,CAAC,WAAW,EAAE,CAAC;gBAC7B,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,oBAAoB,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;YAClF,CAAC;YAED,iCAAiC;YACjC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC;QAED,uCAAuC;QACvC,cAAc,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC9C,cAAc,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAEzC,OAAO,CAAC,GAAG,CAAC,MAAM,SAAS,yBAAyB,CAAC,CAAC;IAE1D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,MAAM,SAAS,iCAAiC,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC;AACL,CAAC"}