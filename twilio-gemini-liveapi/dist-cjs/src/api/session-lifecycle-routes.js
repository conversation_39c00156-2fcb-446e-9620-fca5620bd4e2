"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerSessionLifecycleRoutes = registerSessionLifecycleRoutes;
const security_utils_1 = require("../middleware/security-utils");
const twilio_1 = __importDefault(require("twilio"));
const config_1 = require("../config/config");
function registerSessionLifecycleRoutes(fastify, deps) {
    console.log('🔧 Registering session lifecycle routes...');
    const { sessionManager, contextManager, activeConnections, lifecycleManager, recoveryManager, summaryManager, SUMMARY_GENERATION_PROMPT } = deps;
    // Manually end a session
    fastify.post('/end-session/:callSid', async (request, _reply) => {
        try {
            const { callSid } = request.params;
            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                // Generate summary if conversation exists
                if (connectionData.conversationLog.length > 0) {
                    await sessionManager.generateSummary(callSid, connectionData, SUMMARY_GENERATION_PROMPT);
                    // End session after summary timeout (increased from 30s to 45s)
                    setTimeout(() => {
                        endSessionHelper(callSid, { sessionManager, contextManager, activeConnections });
                    }, 45000);
                }
                else {
                    endSessionHelper(callSid, { sessionManager, contextManager, activeConnections });
                }
                return { success: true, message: 'Session ending initiated' };
            }
            else {
                return { success: false, message: 'Session not found' };
            }
        }
        catch (error) {
            console.error('❌ Error ending session:', error);
            return { success: false, error: error.message };
        }
    });
    // Get connection metrics
    fastify.get('/api/connection-metrics', async (_request, _reply) => {
        const metrics = {
            activeConnections: activeConnections.size,
            connectionDetails: [],
            contextStats: contextManager.getContextStats()
        };
        // Get details for each active connection
        for (const [sessionId, connectionData] of Array.from(activeConnections.entries())) {
            const sessionMetrics = sessionManager.getSessionMetrics(sessionId);
            const recoveryStatus = contextManager.getRecoveryStatus(sessionId);
            metrics.connectionDetails.push({
                sessionId,
                sessionType: connectionData.sessionType || 'unknown',
                isActive: connectionData.isSessionActive,
                startTime: sessionMetrics?.startTime,
                lastActivity: sessionMetrics?.lastActivity,
                messagesReceived: sessionMetrics?.messagesReceived || 0,
                messagesSent: sessionMetrics?.messagesSent || 0,
                recoveryCount: sessionMetrics?.recoveryCount || 0,
                recoveryStatus
            });
        }
        return {
            success: true,
            metrics
        };
    });
    // Manual recovery trigger
    fastify.post('/api/trigger-recovery/:callSid', async (request, _reply) => {
        try {
            const { callSid } = request.params;
            const connectionData = activeConnections.get(callSid);
            if (!connectionData) {
                return { success: false, message: 'Session not found' };
            }
            // Mark as interrupted and trigger recovery
            contextManager.markSessionInterrupted(callSid, 'manual_recovery');
            // Trigger recovery
            setTimeout(() => {
                sessionManager.recoverSession(callSid, 'manual_recovery');
            }, 1000);
            return {
                success: true,
                message: 'Recovery triggered',
                callSid
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    });
    // Session lifecycle management endpoints
    fastify.get('/api/sessions/active', async (request, reply) => {
        try {
            const activeSessions = lifecycleManager ? lifecycleManager.getActiveSessions() : [];
            return {
                success: true,
                sessions: activeSessions,
                count: activeSessions.length,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Error getting active sessions:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    fastify.get('/api/sessions/:sessionId/status', async (request, reply) => {
        try {
            const { sessionId } = request.params;
            if (!security_utils_1.SecurityUtils.sanitizeCallSid(sessionId) && !sessionId.startsWith('local-')) {
                reply.status(400);
                return { success: false, error: 'Invalid session ID format' };
            }
            const sessionStatus = lifecycleManager ? lifecycleManager.getCurrentState(sessionId) : null;
            const recoveryStatus = recoveryManager ? recoveryManager.getRecoveryStatus(sessionId) : null;
            const summaryStatus = summaryManager ? summaryManager.getSummaryStatus(sessionId) : null;
            return {
                success: true,
                sessionId,
                session: sessionStatus,
                recovery: recoveryStatus,
                summary: summaryStatus,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Error getting session status:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    fastify.post('/api/sessions/:sessionId/end', async (request, reply) => {
        try {
            const { sessionId } = request.params;
            const { reason = 'api_request' } = request.body;
            if (!security_utils_1.SecurityUtils.sanitizeCallSid(sessionId) && !sessionId.startsWith('local-')) {
                reply.status(400);
                return { success: false, error: 'Invalid session ID format' };
            }
            const connectionData = activeConnections.get(sessionId);
            if (!connectionData) {
                reply.status(404);
                return { success: false, error: 'Session not found' };
            }
            if (lifecycleManager) {
                try {
                    // End the call in Twilio if it's a Twilio session
                    if (sessionId.startsWith('CA')) {
                        try {
                            const TWILIO_ACCOUNT_SID = config_1.config.auth.twilio.accountSid;
                            const TWILIO_AUTH_TOKEN = config_1.config.auth.twilio.authToken;
                            if (TWILIO_ACCOUNT_SID && TWILIO_AUTH_TOKEN) {
                                const client = (0, twilio_1.default)(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
                                // CRITICAL FIX: Actually hang up the call, don't just mark as completed
                                await client.calls(sessionId).update({ status: 'completed' });
                                console.log(`🔚 [${sessionId}] Twilio call terminated successfully`);
                            }
                        }
                        catch (twilioError) {
                            console.error(`❌ [${sessionId}] Failed to terminate Twilio call:`, twilioError);
                            // Continue with session cleanup even if Twilio termination fails
                        }
                    }
                    // End the session
                    await lifecycleManager.endSession(sessionId, connectionData, reason);
                    activeConnections.delete(sessionId);
                    return {
                        success: true,
                        message: 'Session end requested successfully',
                        sessionId,
                        reason,
                        timestamp: new Date().toISOString()
                    };
                }
                catch (error) {
                    console.error(`❌ [${sessionId}] Error ending session:`, error);
                    reply.status(500);
                    return { success: false, error: 'Failed to end session' };
                }
            }
            else {
                reply.status(500);
                return { success: false, error: 'Lifecycle manager not available' };
            }
        }
        catch (error) {
            console.error('❌ Error ending session:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    fastify.post('/api/sessions/:sessionId/summary', async (request, reply) => {
        try {
            const { sessionId } = request.params;
            if (!security_utils_1.SecurityUtils.sanitizeCallSid(sessionId) && !sessionId.startsWith('local-')) {
                reply.status(400);
                return { success: false, error: 'Invalid session ID format' };
            }
            const connectionData = activeConnections.get(sessionId);
            if (!connectionData) {
                reply.status(404);
                return { success: false, error: 'Session not found' };
            }
            if (summaryManager) {
                const success = await summaryManager.requestSummary(sessionId, connectionData, contextManager);
                return {
                    success,
                    message: success ? 'Summary requested successfully' : 'Failed to request summary',
                    sessionId,
                    timestamp: new Date().toISOString()
                };
            }
            else {
                reply.status(500);
                return { success: false, error: 'Summary manager not available' };
            }
        }
        catch (error) {
            console.error('❌ Error requesting summary:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    console.log('✅ Session lifecycle routes registered successfully!');
}
// Helper function to end sessions
function endSessionHelper(sessionId, deps) {
    const { sessionManager, contextManager, activeConnections } = deps;
    try {
        console.log(`🔚 [${sessionId}] Ending session via API`);
        const connectionData = activeConnections.get(sessionId);
        if (connectionData) {
            // Close Gemini session
            if (connectionData.geminiSession) {
                try {
                    connectionData.geminiSession.close();
                }
                catch (error) {
                    console.warn(`⚠️ [${sessionId}] Error closing Gemini session:`, error);
                }
            }
            // Log summary if available
            if (connectionData.summaryText) {
                console.log(`📋 [${sessionId}] Final Summary: ${connectionData.summaryText}`);
            }
            // Remove from active connections
            activeConnections.delete(sessionId);
        }
        // Clean up context and session manager
        contextManager.clearSessionContext(sessionId);
        sessionManager.cleanupSession(sessionId);
        console.log(`✅ [${sessionId}] Session ended via API`);
    }
    catch (error) {
        console.error(`❌ [${sessionId}] Error ending session via API:`, error);
    }
}
//# sourceMappingURL=session-lifecycle-routes.js.map