{"version": 3, "file": "analytics-routes.js", "sourceRoot": "", "sources": ["../../../src/api/analytics-routes.ts"], "names": [], "mappings": ";;AAMA,0DA4QC;AA/QD,8DAA0D;AAC1D,0EAAsE;AAEtE,SAAgB,uBAAuB,CAAC,OAAwB,EAAE,YAA0B;IACxF,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAElD,MAAM,EACF,cAAc,EACd,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACf,GAAG,YAAY,CAAC;IAEjB,uBAAuB;IACvB,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACxF,IAAI,CAAC;YACD,MAAM,SAAS,GAAG;gBACd,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;gBACxB,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE;gBAC7B,iBAAiB,EAAE,iBAAiB,CAAC,IAAI;gBACzC,YAAY,EAAE;oBACV,KAAK,EAAE,cAAc,CAAC,eAAe,EAAE,CAAC,aAAa,IAAI,CAAC;oBAC1D,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC1E,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;iBAC7F;gBACD,UAAU,EAAE;oBACR,iDAAiD;oBACjD,cAAc,EAAG,gCAAsB,CAAC,mBAAmB;wBACvD,CAAC,CAAE,gCAAsB,CAAC,mBAAmB,CAAC,UAAU,EAAE;wBAC1D,CAAC,CAAC,IAAI;iBACb;gBACD,eAAe,EAAE;oBACb,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,MAAM;oBACtE,YAAY,EAAE,YAAY,CAAC,eAAe,EAAE;oBAC5C,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,MAAM;oBACtE,YAAY,EAAE,YAAY,CAAC,eAAe,EAAE;iBAC/C;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;YAEF,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,+BAA+B;IAC/B,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACvF,IAAI,CAAC;YACD,MAAM,cAAc,GAAG;gBACnB,cAAc,EAAG,gCAAsB,CAAC,mBAAmB;oBACvD,CAAC,CAAE,gCAAsB,CAAC,mBAAmB,CAAC,UAAU,EAAE;oBAC1D,CAAC,CAAC,IAAI;gBACV,aAAa,EAAG,gCAAsB,CAAC,aAAa;oBAChD,CAAC,CAAE,gCAAsB,CAAC,aAAa,CAAC,kBAAkB,EAAE;oBAC5D,CAAC,CAAC,IAAI;gBACV,mBAAmB,EAAE,IAAW;aACnC,CAAC;YAEF,wCAAwC;YACxC,IAAI,CAAC;gBACD,MAAM,oBAAoB,GAAG,IAAI,4CAAoB,EAAE,CAAC;gBACxD,cAAc,CAAC,mBAAmB,GAAG,MAAM,oBAAoB,CAAC,WAAW,EAAE,CAAC;YAClF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;YACrF,CAAC;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,cAAc;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QAC1F,IAAI,CAAC;YACD,MAAM,gBAAgB,GAAG;gBACrB,YAAY,EAAE,cAAc,CAAC,eAAe,EAAE;gBAC9C,cAAc,EAAE,gBAAgB,CAAC,CAAC,CAAC;oBAC/B,cAAc,EAAE,gBAAgB,CAAC,iBAAiB,EAAE;iBACvD,CAAC,CAAC,CAAC,IAAI;gBACR,aAAa,EAAE,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,IAAI;gBAC5E,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,GAAG;gBACjE,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;aAC1D,CAAC;YAEF,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,gBAAgB;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QAC9F,IAAI,CAAC;YACD,8BAA8B;YAC9B,IAAK,gCAAsB,CAAC,mBAAmB,IAAK,gCAAsB,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;gBAClG,gCAAsB,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;YACxD,CAAC;YAED,6BAA6B;YAC7B,IAAK,gCAAsB,CAAC,aAAa,IAAK,gCAAsB,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;gBACrG,gCAAsB,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;YACjE,CAAC;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,mCAAmC;gBAC5C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,6DAA6D;IAC7D,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QAC/E,IAAI,CAAC;YACD,MAAM,OAAO,GAAG;gBACZ,MAAM,EAAE;oBACJ,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;oBACxB,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE;oBAC7B,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE;oBACvB,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;iBAC7B;gBACD,WAAW,EAAE;oBACT,iBAAiB,EAAE,iBAAiB,CAAC,IAAI;oBACzC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,GAAG;oBACjE,YAAY,EAAE,cAAc,CAAC,eAAe,EAAE;oBAC9C,cAAc,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;wBAC9B,MAAM,MAAM,GAAG,MAAO,OAAe,CAAC,iBAAiB,EAAE,CAAC;wBAC1D,OAAO,MAAM,CAAC,CAAC,CAAC;4BACZ,KAAK,EAAE,MAAM,CAAC,KAAK;4BACnB,KAAK,EAAE,MAAM,CAAC,KAAK;4BACnB,eAAe,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc;4BACxC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,iBAAiB;yBACxC,CAAC,CAAC,CAAC,IAAI,CAAC;oBACb,CAAC,CAAC,EAAE;iBACP;gBACD,QAAQ,EAAE;oBACN,cAAc,EAAE,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa;oBAC5D,cAAc,EAAE,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa;oBAC5D,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa;oBAC1D,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa;oBAC9D,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa;iBACnE;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;YAEF,OAAO,OAAO,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,KAAK,EAAE,8BAA8B,EAAE,OAAO,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QACxF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,iCAAiC;IACjC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACrF,IAAI,CAAC;YACD,IAAI,cAAc,CAAC;YACnB,IAAI,CAAC;gBACD,cAAc,GAAI,gCAAsB,CAAC,mBAAmB,EAAE,UAAU,EAAE,CAAC;YAC/E,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,cAAc,GAAG,IAAI,CAAC;YAC1B,CAAC;YAED,MAAM,YAAY,GAAG,cAAc,IAAI;gBACnC,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,KAAK;gBACb,cAAc,EAAE,EAAE;gBAClB,gBAAgB,EAAE,CAAC;gBACnB,MAAM,EAAE,CAAC;aACZ,CAAC;YAEF,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,YAAY,EAAE,YAAY;gBAC1B,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM;gBAC7C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC/D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,uCAAuC;IACvC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QAC5F,IAAI,CAAC;YACD,2DAA2D;YAC3D,MAAM,mBAAmB,GAAI,gCAAsB,CAAC,mBAAmB,CAAC;YACxE,IAAI,mBAAmB,IAAI,OAAO,mBAAmB,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;gBACzE,mBAAmB,CAAC,KAAK,EAAE,CAAC;YAChC,CAAC;YACD,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,0CAA0C;gBACnD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,EAAE,QAAwB,EAAE,MAAoB,EAAE,EAAE;QACzF,gEAAgE;QAChE,OAAO;YACH,OAAO,EAAE,IAAI;YACb,SAAS,EAAE;gBACP,MAAM,EAAE;oBACJ,MAAM,EAAE,SAAS;oBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC;gBACD,MAAM,EAAE;oBACJ,MAAM,EAAE,SAAS;oBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC;aACJ;SACJ,CAAC;IACN,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QAC7E,IAAI,CAAC;YACD,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,kBAAkB,EAAE,CAAC;gBACrB,iBAAiB,EAAE,CAAC;gBACpB,WAAW,EAAE,EAAW;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;AAC9D,CAAC"}