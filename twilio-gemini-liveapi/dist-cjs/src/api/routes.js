"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerApiRoutes = registerApiRoutes;
const config_1 = require("../config/config");
const webhook_routes_1 = require("./webhook-routes");
const session_config_routes_1 = require("./session-config-routes");
const session_lifecycle_routes_1 = require("./session-lifecycle-routes");
const script_routes_1 = require("./script-routes");
const analytics_routes_1 = require("./analytics-routes");
const call_management_routes_1 = require("./call-management-routes");
const logger_1 = require("../utils/logger");
const verduona_auth_service_1 = require("../auth/verduona-auth-service");
// Type definitions for routes not yet moved to shared types
function registerApiRoutes(fastify, dependencies) {
    console.log('🚀 Starting API route registration...');
    console.log('🔍 [DEBUG] Dependencies received:', Object.keys(dependencies || {}));
    const { sessionManager, contextManager, activeConnections, healthMonitor, recoveryManager, lifecycleManager, summaryManager, scriptManager, voiceManager, modelManager, GEMINI_DEFAULT_VOICE, GEMINI_DEFAULT_MODEL, SUMMARY_GENERATION_PROMPT } = dependencies;
    console.log('✅ Dependencies extracted successfully');
    console.log('🔍 [DEBUG] scriptManager extracted:', !!scriptManager, typeof scriptManager);
    // Register webhook routes
    (0, webhook_routes_1.registerWebhookRoutes)(fastify, dependencies);
    // Register session configuration routes
    (0, session_config_routes_1.registerSessionConfigRoutes)(fastify, dependencies);
    // Register session lifecycle routes
    (0, session_lifecycle_routes_1.registerSessionLifecycleRoutes)(fastify, dependencies);
    // Register script management routes
    (0, script_routes_1.registerScriptRoutes)(fastify, dependencies);
    // Register analytics and metrics routes
    (0, analytics_routes_1.registerAnalyticsRoutes)(fastify, dependencies);
    // Register call management routes
    (0, call_management_routes_1.registerCallManagementRoutes)(fastify, dependencies);
    // Root route - API information
    fastify.get('/', async (_request, _reply) => {
        return {
            service: 'Twilio Gemini Live API',
            status: 'running',
            version: '2.0.0',
            activeConnections: activeConnections.size,
            endpoints: {
                health: '/health',
                websocket: '/media-stream',
                localAudio: '/local-audio-session',
                voices: '/available-voices',
                models: '/available-models',
                providerHealth: '/api/provider-health',
                incomingScenarios: '/api/incoming-scenarios',
                configureIncomingScenario: '/api/configure-incoming-scenario'
            }
        };
    });
    // Health check route is already registered in index.ts - skip duplicate registration
    console.log('✅ Health check endpoints (skipped - already registered in index.ts)');
    // Get available Gemini voices
    fastify.get('/available-voices', async (_request, _reply) => {
        try {
            const voiceConfig = voiceManager.getVoiceConfig();
            return {
                success: true,
                voices: voiceConfig.availableVoices,
                currentDefault: voiceConfig.defaultVoice,
                voiceMapping: voiceConfig.voiceMapping,
                voiceSelectionEnabled: voiceConfig.voiceSelectionEnabled,
                totalVoices: voiceConfig.totalVoices
            };
        }
        catch (error) {
            console.error('❌ Error getting available voices:', error);
            return {
                success: false,
                error: 'Failed to get available voices',
                message: error.message
            };
        }
    });
    // Voice Configuration Endpoint
    fastify.get('/api/voice-config', {
        schema: {
            response: {
                200: {
                    type: 'object',
                    properties: {
                        defaultVoice: { type: 'string' },
                        availableVoices: { type: 'object' },
                        voiceMapping: { type: 'object' },
                        voiceSelectionEnabled: { type: 'boolean' },
                        totalVoices: { type: 'number' },
                        voiceDescriptions: { type: 'object' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const voiceConfig = voiceManager.getVoiceConfig();
            const envVoices = config_1.config?.ai?.gemini?.voices || {};
            // Parse voice descriptions from env
            const voiceDescriptions = {};
            if (envVoices && typeof envVoices === 'object') {
                Object.keys(envVoices).forEach(voiceName => {
                    const description = envVoices[voiceName];
                    voiceDescriptions[voiceName] = description;
                });
            }
            const response = {
                defaultVoice: voiceConfig.defaultVoice || 'Kore',
                availableVoices: voiceConfig.availableVoices || {},
                voiceMapping: voiceConfig.voiceMapping || {},
                voiceSelectionEnabled: voiceConfig.voiceSelectionEnabled || false,
                totalVoices: voiceConfig.totalVoices || 8,
                voiceDescriptions
            };
            return reply.code(200).send(response);
        }
        catch (error) {
            console.error('❌ Error getting voice config:', error);
            return reply.code(500).send({
                error: 'Failed to get voice configuration',
                details: error.message
            });
        }
    });
    // Get available Gemini models
    fastify.get('/available-models', async (_request, _reply) => {
        try {
            const modelConfig = modelManager.getModelConfig();
            const availableModels = modelConfig.availableModels || {
                'gemini-2.5-flash-preview-native-audio-dialog': {
                    name: 'Gemini 2.5 Flash Preview Native Audio Dialog',
                    audioSupport: true
                },
                'gemini-2.0-flash-exp': { name: 'Gemini 2.0 Flash Experimental', audioSupport: true }
            };
            return {
                success: true,
                availableModels: availableModels,
                defaultModel: modelConfig.defaultModel,
                currentModel: modelConfig.currentModel,
                modelSelectionEnabled: modelConfig.modelSelectionEnabled,
                totalModels: modelConfig.totalModels || Object.keys(availableModels).length,
                configurationSource: modelConfig.configurationSource
            };
        }
        catch (error) {
            console.error('❌ Error getting available models:', error);
            return {
                success: false,
                error: 'Failed to get available models',
                message: error.message
            };
        }
    });
    // Initialize Verduona Auth Service
    const authService = new verduona_auth_service_1.VerduonaAuthService();
    // Token validation endpoint for production authentication
    fastify.post('/api/validate-token', async (request, reply) => {
        try {
            const body = request.body;
            const authHeader = request.headers.authorization;
            // Extract token from body or Authorization header
            const token = body?.token || (authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : null);
            if (!token) {
                logger_1.apiLogger.warn('🔐 Token validation failed: No token provided');
                return reply.code(401).send({
                    success: false,
                    error: 'No token provided'
                });
            }
            // Validate token with proper JWT verification
            const validationResult = await authService.validateToken(token);
            if (!validationResult.isValid) {
                logger_1.apiLogger.warn('🔐 Token validation failed', {
                    error: validationResult.error,
                    tokenLength: token.length
                });
                return reply.code(401).send({
                    success: false,
                    error: validationResult.error || 'Invalid token'
                });
            }
            // Token is valid - extract user info
            const userInfo = validationResult.payload ?
                authService.getUserInfo(validationResult.payload) : null;
            logger_1.apiLogger.info('🔐 Token validation successful', {
                user: userInfo?.id,
                email: userInfo?.email,
                roles: userInfo?.roles?.length || 0,
                tokenLength: token.length
            });
            return reply.code(200).send({
                success: true,
                message: 'Token is valid',
                user: userInfo,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            logger_1.apiLogger.error('❌ Token validation error:', error);
            return reply.code(500).send({
                success: false,
                error: 'Token validation failed',
                message: error.message
            });
        }
    });
    // Test route to verify route registration is working
    logger_1.apiLogger.info('Registering test route...');
    try {
        fastify.get('/api/test', async (request, reply) => {
            logger_1.apiLogger.debug('Test route called');
            return { success: true, message: 'Test route working', timestamp: new Date().toISOString() };
        });
        logger_1.apiLogger.info('Test route registered successfully');
    }
    catch (error) {
        logger_1.apiLogger.error('Error registering test route', error);
    }
    logger_1.apiLogger.debug('Reached end of route registration function');
    logger_1.apiLogger.info('All API routes registered successfully!');
}
//# sourceMappingURL=routes.js.map