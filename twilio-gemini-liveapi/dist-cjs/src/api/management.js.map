{"version": 3, "file": "management.js", "sourceRoot": "", "sources": ["../../../src/api/management.ts"], "names": [], "mappings": ";;AAkBA,4DA6TC;AA7TD,SAAgB,wBAAwB,CAAC,OAAwB,EAAE,YAA0B;IACzF,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,iBAAiB,EAAE,cAAc,EAAE,GAAG,YAAY,CAAC;IAEtG,qCAAqC;IAErC,uBAAuB;IACvB,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACvF,IAAI,CAAC;YACD,MAAM,WAAW,GAAG,YAAY,CAAC,cAAc,EAAE,CAAC;YAClD,KAAK,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,GAAG,WAAW;gBACd,WAAW,EAAE,0EAA0E;aAC1F,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,YAAY;IACZ,OAAO,CAAC,IAAI,CAAyB,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QAC5E,IAAI,CAAC;YACD,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;YAE/B,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,CAAC,CAAC;YAC5E,CAAC;YAED,MAAM,UAAU,GAAG,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACtB,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;oBAC/D,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;oBACzD,UAAU,EAAE,UAAU,CAAC,UAAU;iBACpC,CAAC,CAAC;YACP,CAAC;YAED,MAAM,aAAa,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC;YACrD,MAAM,OAAO,GAAG,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC,KAAM,CAAC,CAAC;YAEhE,IAAI,OAAO,EAAE,CAAC;gBACV,KAAK,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,IAAI;oBACb,aAAa;oBACb,YAAY,EAAE,UAAU,CAAC,KAAK;oBAC9B,oBAAoB,EAAE,UAAU,CAAC,IAAI;oBACrC,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,KAAK;oBAClC,aAAa,EAAE,UAAU,CAAC,aAAa,IAAI,UAAU,CAAC,KAAK;oBAC3D,OAAO,EAAE,iCAAiC,UAAU,CAAC,KAAK,EAAE;oBAC5D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,qBAAqB;iBAC/B,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,qCAAqC;IAErC,uBAAuB;IACvB,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACvF,IAAI,CAAC;YACD,MAAM,WAAW,GAAG,YAAY,CAAC,cAAc,EAAE,CAAC;YAClD,KAAK,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,GAAG,WAAW;gBACd,WAAW,EAAE,sCAAsC;aACtD,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,YAAY;IACZ,OAAO,CAAC,IAAI,CAAyB,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QAC5E,IAAI,CAAC;YACD,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;YAE/B,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,CAAC,CAAC;YAC5E,CAAC;YAED,MAAM,UAAU,GAAG,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACtB,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;oBAC/D,UAAU,EAAE,UAAU,CAAC,UAAU;iBACpC,CAAC,CAAC;YACP,CAAC;YAED,MAAM,aAAa,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC;YACrD,MAAM,OAAO,GAAG,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC,KAAM,CAAC,CAAC;YAEhE,IAAI,OAAO,EAAE,CAAC;gBACV,KAAK,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,IAAI;oBACb,aAAa;oBACb,YAAY,EAAE,UAAU,CAAC,KAAK;oBAC9B,SAAS,EAAE,UAAU,CAAC,IAAI;oBAC1B,OAAO,EAAE,iCAAiC,UAAU,CAAC,KAAK,EAAE;oBAC5D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,qBAAqB;iBAC/B,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACnF,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,YAAY,CAAC,aAAa,EAAE,CAAC;YAC3C,KAAK,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,KAAK;gBACL,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,0CAA0C;IAC1C,gEAAgE;IAEhE,oDAAoD;IACpD,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QAClG,IAAI,CAAC;YACD,qEAAqE;YACrE,MAAM,SAAS,GAAG;gBACd,UAAU,EAAE,CAAC;gBACb,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,IAAI;gBACjB,UAAU,EAAE,EAAW;gBACvB,WAAW,EAAE,EAAE;gBACf,cAAc,EAAE,IAAI;gBACpB,cAAc,EAAE,IAAI;gBACpB,iBAAiB,EAAE,GAAG;aACzB,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,gDAAgD;IAChD,OAAO,CAAC,GAAG,CAAsC,+BAA+B,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QACvG,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC;YAEpD,wEAAwE;YACxE,MAAM,OAAO,GAAU,EAAE,CAAC;YAE1B,KAAK,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,OAAO;gBACP,KAAK;gBACL,KAAK,EAAE,OAAO,CAAC,MAAM;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,sCAAsC;IACtC,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QAC1F,IAAI,CAAC;YACD,4CAA4C;YAC5C,MAAM,SAAS,GAAG;gBACd,kBAAkB,EAAE,CAAC;gBACrB,iBAAiB,EAAE,CAAC;gBACpB,mBAAmB,EAAE,IAAI;gBACzB,oBAAoB,EAAE,GAAG;gBACzB,oBAAoB,EAAE,EAAE;gBACxB,SAAS,EAAE,EAAc;gBACzB,gBAAgB,EAAE,EAAE;aACvB,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,yCAAyC;IACzC,OAAO,CAAC,GAAG,CAAsC,uBAAuB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QAC/F,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC;YAEpD,yCAAyC;YACzC,MAAM,OAAO,GAAU,EAAE,CAAC;YAE1B,KAAK,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,OAAO;gBACP,KAAK;gBACL,KAAK,EAAE,OAAO,CAAC,MAAM;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QAC1F,IAAI,CAAC;YACD,MAAM,WAAW,GAAU,EAAE,CAAC;YAE9B,IAAI,iBAAiB,EAAE,CAAC;gBACpB,KAAK,MAAM,CAAC,OAAO,EAAE,cAAc,CAAC,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;oBAClE,WAAW,CAAC,IAAI,CAAC;wBACb,OAAO;wBACP,QAAQ,EAAE,cAAc,CAAC,eAAe;wBACxC,SAAS,EAAE,cAAc,CAAC,SAAS;wBACnC,UAAU,EAAE,cAAc,CAAC,UAAU;wBACrC,iBAAiB,EAAE,cAAc,CAAC,iBAAiB;wBACnD,gBAAgB,EAAE,CAAC,CAAC,cAAc,CAAC,aAAa;wBAChD,mBAAmB,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ;wBAC9C,gBAAgB,EAAE,CAAC,CAAC,cAAc,CAAC,kBAAkB;qBACxD,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YAED,KAAK,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,WAAW;gBACX,WAAW,EAAE,WAAW,CAAC,MAAM;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,OAAO,CAAC,GAAG,CAAmC,+BAA+B,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QACpG,IAAI,CAAC;YACD,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YAEnC,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,+BAA+B;iBACzC,CAAC,CAAC;YACP,CAAC;YAED,MAAM,OAAO,GAAG,cAAc,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE1D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;iBACrC,CAAC,CAAC;YACP,CAAC;YAED,mDAAmD;YACnD,KAAK,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE;oBACL,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,aAAa,EAAE;wBACX,KAAK,EAAE,OAAO,CAAC,aAAa,EAAE,KAAK;wBACnC,KAAK,EAAE,OAAO,CAAC,aAAa,EAAE,KAAK;wBACnC,cAAc,EAAE,OAAO,CAAC,aAAa,EAAE,cAAc;qBACxD;oBACD,iBAAiB,EAAE;wBACf,eAAe,EAAE,OAAO,CAAC,iBAAiB,EAAE,eAAe;wBAC3D,gBAAgB,EAAE,OAAO,CAAC,iBAAiB,EAAE,gBAAgB;wBAC7D,qBAAqB,EAAE,OAAO,CAAC,iBAAiB,EAAE,eAAe,EAAE,MAAM,IAAI,CAAC;qBACjF;oBACD,eAAe,EAAE,OAAO,CAAC,eAAe;oBACxC,YAAY,EAAE,OAAO,CAAC,YAAY;iBACrC;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,wFAAwF;IACxF,4DAA4D;AAChE,CAAC"}