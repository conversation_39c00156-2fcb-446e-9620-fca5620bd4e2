import { FastifyRequest } from 'fastify';
import { CallConfig } from '../types/api-types';
export interface IncomingCallBody {
    CallSid: string;
    From: string;
    To: string;
    CallStatus: string;
    Direction: string;
}
export interface CallValidationResult {
    isValid: boolean;
    error?: string;
    statusCode?: number;
}
export interface ScriptConfigResult {
    config: CallConfig | null;
    error?: string;
}
export interface WebSocketUrlResult {
    url: string;
    error?: string;
}
/**
 * Validates the incoming call request
 */
export declare function validateIncomingCall(body: IncomingCallBody): CallValidationResult;
/**
 * Determines if the call is outbound based on direction
 */
export declare function isOutboundCall(direction: string): boolean;
/**
 * Gets the outbound call script configuration
 */
export declare function getOutboundScriptConfig(nextCallConfig: any): CallConfig;
/**
 * Gets the inbound call script configuration with fallbacks
 */
export declare function getInboundScriptConfig(scriptManager: any, from: string): ScriptConfigResult;
/**
 * Creates the final call configuration object
 */
export declare function createCallConfig(scriptConfig: CallConfig, callData: IncomingCallBody, isOutbound: boolean): CallConfig;
/**
 * Constructs the WebSocket URL for the call
 */
export declare function constructWebSocketUrl(request: FastifyRequest, isOutbound: boolean, publicUrl?: string): WebSocketUrlResult;
/**
 * Escapes URL for XML safety
 */
export declare function escapeXmlUrl(url: string): string;
/**
 * Generates TwiML response for connecting to WebSocket
 */
export declare function generateTwiML(escapedWsUrl: string): string;
/**
 * Generates error TwiML response
 */
export declare function generateErrorTwiML(): string;
/**
 * Logs call configuration details
 */
export declare function logCallConfiguration(callConfig: CallConfig, scriptConfig: CallConfig, isOutbound: boolean): void;
//# sourceMappingURL=call-handlers.d.ts.map