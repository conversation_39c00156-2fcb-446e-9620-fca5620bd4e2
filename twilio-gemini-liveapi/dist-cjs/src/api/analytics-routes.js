"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerAnalyticsRoutes = registerAnalyticsRoutes;
const audio_processor_1 = require("../audio/audio-processor");
const transcription_manager_1 = require("../audio/transcription-manager");
function registerAnalyticsRoutes(fastify, dependencies) {
    console.log('📊 Registering analytics routes...');
    const { sessionManager, contextManager, activeConnections, healthMonitor, recoveryManager, lifecycleManager, voiceManager, modelManager } = dependencies;
    // Get system analytics
    fastify.get('/api/analytics/system', async (request, reply) => {
        try {
            const analytics = {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                activeConnections: activeConnections.size,
                sessionStats: {
                    total: contextManager.getContextStats().totalContexts || 0,
                    active: lifecycleManager ? lifecycleManager.getActiveSessions().length : 0,
                    recovered: recoveryManager ? recoveryManager.getRecoveryMetrics().successfulRecoveries : 0
                },
                audioStats: {
                    // Get audio processing stats from AudioProcessor
                    qualityMonitor: audio_processor_1.AudioProcessor.audioQualityMonitor
                        ? audio_processor_1.AudioProcessor.audioQualityMonitor.getSummary()
                        : null
                },
                voiceModelStats: {
                    availableVoices: Object.keys(voiceManager.getAvailableVoices()).length,
                    currentVoice: voiceManager.getDefaultVoice(),
                    availableModels: Object.keys(modelManager.getAvailableModels()).length,
                    currentModel: modelManager.getCurrentModel()
                },
                timestamp: new Date().toISOString()
            };
            return {
                success: true,
                analytics,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Error getting system analytics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    // Get audio processing metrics
    fastify.get('/api/analytics/audio', async (request, reply) => {
        try {
            const audioAnalytics = {
                qualityMonitor: audio_processor_1.AudioProcessor.audioQualityMonitor
                    ? audio_processor_1.AudioProcessor.audioQualityMonitor.getSummary()
                    : null,
                enhancerStats: audio_processor_1.AudioProcessor.audioEnhancer
                    ? audio_processor_1.AudioProcessor.audioEnhancer.getProcessingStats()
                    : null,
                transcriptionHealth: null
            };
            // Get transcription health if available
            try {
                const transcriptionManager = new transcription_manager_1.TranscriptionManager();
                audioAnalytics.transcriptionHealth = await transcriptionManager.healthCheck();
            }
            catch (error) {
                console.warn('⚠️ Could not get transcription health:', error.message);
            }
            return {
                success: true,
                audioAnalytics,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Error getting audio analytics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    // Get session analytics
    fastify.get('/api/analytics/sessions', async (request, reply) => {
        try {
            const sessionAnalytics = {
                contextStats: contextManager.getContextStats(),
                lifecycleStats: lifecycleManager ? {
                    activeSessions: lifecycleManager.getActiveSessions()
                } : null,
                recoveryStats: recoveryManager ? recoveryManager.getRecoveryMetrics() : null,
                healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
                activeConnections: Array.from(activeConnections.keys())
            };
            return {
                success: true,
                sessionAnalytics,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Error getting session analytics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    // Reset audio processing statistics
    fastify.post('/api/analytics/audio/reset', async (request, reply) => {
        try {
            // Reset audio quality monitor
            if (audio_processor_1.AudioProcessor.audioQualityMonitor && audio_processor_1.AudioProcessor.audioQualityMonitor.reset) {
                audio_processor_1.AudioProcessor.audioQualityMonitor.reset();
            }
            // Reset audio enhancer stats
            if (audio_processor_1.AudioProcessor.audioEnhancer && audio_processor_1.AudioProcessor.audioEnhancer.resetProcessingStats) {
                audio_processor_1.AudioProcessor.audioEnhancer.resetProcessingStats();
            }
            return {
                success: true,
                message: 'Audio processing statistics reset',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Error resetting audio statistics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    // Get comprehensive system metrics (for monitoring/alerting)
    fastify.get('/api/metrics', async (request, reply) => {
        try {
            const metrics = {
                system: {
                    uptime: process.uptime(),
                    memory: process.memoryUsage(),
                    cpu: process.cpuUsage(),
                    version: process.version,
                    platform: process.platform
                },
                application: {
                    activeConnections: activeConnections.size,
                    healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
                    contextStats: contextManager.getContextStats(),
                    nextCallConfig: await (async () => {
                        const config = await fastify.getNextCallConfig();
                        return config ? {
                            voice: config.voice,
                            model: config.model,
                            hasInstructions: !!config.aiInstructions,
                            hasTarget: !!config.targetPhoneNumber
                        } : null;
                    })()
                },
                services: {
                    sessionManager: sessionManager ? 'available' : 'unavailable',
                    contextManager: contextManager ? 'available' : 'unavailable',
                    healthMonitor: healthMonitor ? 'available' : 'unavailable',
                    recoveryManager: recoveryManager ? 'available' : 'unavailable',
                    lifecycleManager: lifecycleManager ? 'available' : 'unavailable'
                },
                timestamp: new Date().toISOString()
            };
            return metrics;
        }
        catch (error) {
            console.error('❌ Error getting system metrics:', error);
            reply.status(500);
            return { error: 'Failed to get system metrics', message: error.message };
        }
    });
    // Audio quality metrics endpoint
    fastify.get('/api/audio-quality', async (request, reply) => {
        try {
            let qualityMetrics;
            try {
                qualityMetrics = audio_processor_1.AudioProcessor.audioQualityMonitor?.getSummary();
            }
            catch (e) {
                qualityMetrics = null;
            }
            const audioQuality = qualityMetrics || {
                sampleRate: 16000,
                bitDepth: 16,
                channels: 1,
                format: 'PCM',
                averageLatency: 50,
                packetsProcessed: 0,
                errors: 0
            };
            return {
                success: true,
                audioQuality: audioQuality,
                debugMode: process.env.AUDIO_DEBUG === 'true',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Error getting audio quality metrics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    // Reset audio quality metrics endpoint
    fastify.post('/api/audio-quality/reset', async (request, reply) => {
        try {
            // Check if audioQualityMonitor exists before calling reset
            const audioQualityMonitor = audio_processor_1.AudioProcessor.audioQualityMonitor;
            if (audioQualityMonitor && typeof audioQualityMonitor.reset === 'function') {
                audioQualityMonitor.reset();
            }
            return {
                success: true,
                message: 'Audio quality metrics reset successfully',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Error resetting audio quality metrics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    // Provider health check
    fastify.get('/api/provider-health', async (_request, _reply) => {
        // This would check Gemini API health, Twilio connectivity, etc.
        return {
            success: true,
            providers: {
                gemini: {
                    status: 'healthy',
                    lastCheck: new Date().toISOString()
                },
                twilio: {
                    status: 'healthy',
                    lastCheck: new Date().toISOString()
                }
            }
        };
    });
    // Analytics endpoint
    fastify.get('/analytics', async (request, reply) => {
        try {
            return {
                success: true,
                totalOutboundCalls: 0,
                totalInboundCalls: 0,
                callHistory: [],
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('❌ Error getting analytics:', error);
            reply.status(500);
            return { success: false, error: error.message };
        }
    });
    console.log('✅ Analytics routes registered successfully');
}
//# sourceMappingURL=analytics-routes.js.map