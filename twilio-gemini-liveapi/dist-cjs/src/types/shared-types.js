"use strict";
// Shared type definitions used across multiple modules
// This file consolidates duplicate interfaces to ensure consistency
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScriptTypeSchema = exports.ModelSchema = exports.VoiceSchema = void 0;
// Import Zod for schema-derived types
const zod_1 = require("zod");
// Zod schemas for consistent validation
exports.VoiceSchema = zod_1.z.enum([
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Orus', 'Zep<PERSON>r'
]);
exports.ModelSchema = zod_1.z.enum([
    'gemini-2.0-flash-exp',
    'gemini-1.5-flash',
    'gemini-1.5-pro',
    'gemini-2.5-flash-preview-native-audio-dialog',
    'gemini-2.0-flash-live-001',
    'gemini-2.0-flash',
    'gemini-2.0-flash-live-preview-04-09'
]);
exports.ScriptTypeSchema = zod_1.z.enum(['incoming', 'outbound']);
//# sourceMappingURL=shared-types.js.map