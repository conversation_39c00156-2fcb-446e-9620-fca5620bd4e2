"use strict";
// Unified WebSocket message types
// These types are shared across the backend so that handlers
// can operate on consistent structures regardless of the source.
Object.defineProperty(exports, "__esModule", { value: true });
exports.TWILIO_EVENT_MAP = void 0;
// Map of Twilio events to internal message types so downstream handlers see
// a consistent event name
exports.TWILIO_EVENT_MAP = {
    start: 'start-session',
    media: 'audio',
    stop: 'end-session',
    connected: 'connected',
    mark: 'mark'
};
//# sourceMappingURL=index.js.map