import type { WebSocket } from 'ws';
import type { ConnectionData, GeminiClient, GeminiSession, AppConfig } from '../global';
import type { SessionManager } from '../../session/session-manager';
import type { ContextManager } from '../../session/context-manager';
import type { SessionSummaryManager } from '../../session/summary-manager';
import type { ConnectionHealthMonitor } from '../../session/health-monitor';
import type { LifecycleManager } from '../../session/lifecycle-manager';
import type { RecoveryManager } from '../../session/recovery-manager';
import type { TranscriptionManager } from '../../audio/transcription-manager';
import type { ScriptManager } from '../../scripts/script-manager';
import type { VoiceManager } from '../../gemini/voice-manager';
import type { ModelManager } from '../../gemini/model-manager';
import type { ScriptType } from '../shared-types';
export interface WebSocketConnection {
    socket?: WebSocket;
    query?: Record<string, string | string[] | undefined>;
}
export { ConnectionData };
export interface TwilioHelper {
    endCallWithMessage(callSid: string, message: string): Promise<void>;
}
import type { GeminiVoice, GeminiModel } from '../shared-types';
export type { GeminiVoice, GeminiModel };
export interface ExtendedConnectionData extends ConnectionData {
    sessionConfig?: SessionConfig;
    sessionReady?: boolean;
    sessionInitialized?: number;
    geminiSessionError?: string;
    sessionActivatedAt?: number;
    fullyReady?: boolean;
    pendingScript?: string;
    isTwilioCall?: boolean;
    streamSid?: string;
    twilioWs?: WebSocket;
    localWs?: WebSocket;
    summaryFlowType?: string;
    summaryTimeoutId?: NodeJS.Timeout;
}
export interface SessionConfig {
    aiInstructions: string;
    voice: GeminiVoice;
    model: GeminiModel;
    targetName?: string | null;
    targetPhoneNumber?: string | null;
    scriptType: ScriptType;
    scriptId: string;
    isIncomingCall?: boolean;
    isTestMode?: boolean;
    campaignId?: string | number | null;
    sessionType?: string;
    originalAIInstructions?: string;
}
export interface WebSocketDependencies {
    sessionManager: SessionManager;
    contextManager: ContextManager;
    activeConnections: Map<string, ConnectionData>;
    healthMonitor: ConnectionHealthMonitor;
    summaryManager: SessionSummaryManager;
    lifecycleManager: LifecycleManager;
    recoveryManager: RecoveryManager;
    transcriptionManager: TranscriptionManager;
    scriptManager: ScriptManager;
    voiceManager: VoiceManager;
    modelManager: ModelManager;
    getNextCallConfig?: () => SessionConfig | null;
    twilioHelper?: TwilioHelper;
    GEMINI_DEFAULT_VOICE: string;
    GEMINI_DEFAULT_MODEL: string;
    SUMMARY_GENERATION_PROMPT?: string;
    config?: AppConfig;
    geminiClient?: GeminiClient;
}
export interface FlowDependencies extends WebSocketDependencies {
    flowType: string;
    getSessionConfig: (callSid?: string) => Promise<SessionConfig>;
    isIncomingCall: boolean;
    callType?: string;
    isTestMode?: boolean;
    getOutboundTestConfig?: (deps: WebSocketDependencies) => Promise<SessionConfig>;
    getInboundTestConfig?: (deps: WebSocketDependencies) => Promise<SessionConfig>;
}
export interface TwilioMediaMessage {
    event: 'media';
    media: {
        payload: string;
        chunk?: string;
        timestamp?: string;
        track?: string;
    };
    streamSid?: string;
    sequenceNumber?: string;
}
export interface TwilioStartMessage {
    event: 'start';
    start: {
        streamSid: string;
        accountSid: string;
        callSid: string;
        tracks?: string[];
        customParameters?: Record<string, string | number | boolean>;
    };
    streamSid?: string;
}
export interface LocalAudioMessage {
    type: 'audio-data' | 'audio';
    audio?: string;
    audioData?: string;
}
export interface LocalStartMessage {
    type: 'start-session' | 'start_session';
    aiInstructions?: string;
    voice?: string;
    model?: string;
    scriptId?: string;
}
export interface LocalTextMessage {
    type: 'text-message';
    text: string;
}
export interface LocalEndSessionMessage {
    type: 'end-session' | 'end_session';
}
export interface HeartbeatData {
    ws: WebSocket;
    interval: number;
    timeout: number;
    onTimeout: ((sessionId: string, ws: WebSocket) => void) | null;
    intervalId: NodeJS.Timeout | null;
    timeoutId: NodeJS.Timeout | null;
    lastPong: number;
    missedPings: number;
    pongHandler?: () => void;
}
export interface HeartbeatStatus {
    sessionId: string;
    lastPong: number;
    missedPings: number;
    interval: number;
    timeout: number;
    isActive: boolean;
}
export interface HeartbeatStatistics {
    totalSessions: number;
    healthySessions: number;
    unhealthySessions: number;
    averageLastPong: number;
}
export interface SessionData {
    geminiSession: GeminiSession | null;
    isSessionActive: boolean;
    pendingScript?: string;
}
export type InternalMessageType = 'start-session' | 'audio' | 'audio-data' | 'text-message' | 'turn-complete' | 'end-session' | 'audio-response' | 'heartbeat' | 'connected' | 'mark';
export declare const TWILIO_EVENT_MAP: Record<string, InternalMessageType>;
//# sourceMappingURL=index.d.ts.map