import type { SessionManager } from '../session/session-manager';
import type { ContextManager } from '../session/context-manager';
import type { ConnectionHealthMonitor } from '../session/health-monitor';
import type { SessionRecoveryManager } from '../session/recovery-manager';
import type { SessionLifecycleManager } from '../session/lifecycle-manager';
import type { SessionSummaryManager } from '../session/summary-manager';
import type { ScriptManager } from '../scripts/script-manager';
import type { VoiceManager } from '../gemini/voice-manager';
import type { ModelManager } from '../gemini/model-manager';
import type { ConnectionData } from './global';
export interface Dependencies {
    sessionManager: SessionManager;
    contextManager: ContextManager;
    activeConnections: Map<string, ConnectionData>;
    healthMonitor?: ConnectionHealthMonitor;
    recoveryManager?: SessionRecoveryManager;
    lifecycleManager?: SessionLifecycleManager;
    summaryManager?: SessionSummaryManager;
    scriptManager: ScriptManager;
    voiceManager: VoiceManager;
    modelManager: ModelManager;
    GEMINI_DEFAULT_VOICE: string;
    GEMINI_DEFAULT_MODEL: string;
    SUMMARY_GENERATION_PROMPT: string;
}
export interface CallConfig {
    aiInstructions: string;
    voice: string;
    model: string;
    targetName: string | null;
    targetPhoneNumber: string | null;
    callSid?: string;
    from?: string;
    to?: string;
    callStatus?: string;
    isIncomingCall?: boolean;
    timestamp?: string;
    scriptType?: string;
    createdAt?: number;
    scriptId?: string;
    phoneNumber: string;
    mode: 'outbound' | 'inbound' | 'outbound-testing' | 'inbound-testing';
}
export interface CallStatusBody {
    CallSid: string;
    CallStatus: string;
    Duration?: string;
    From?: string;
    To?: string;
}
export interface ConfigureCallBody {
    aiInstructions?: string;
    voice?: string;
    model?: string;
    targetName?: string;
    targetPhoneNumber?: string;
}
export interface EndSessionParams {
    callSid: string;
}
export interface SetVoiceBody {
    voice: string;
}
export interface SetModelBody {
    model: string;
}
export interface AudioSettingsBody {
    [key: string]: any;
}
export interface TriggerRecoveryParams {
    callSid: string;
}
export interface CampaignScriptParams {
    id: string;
}
export interface IncomingCallBody {
    CallSid: string;
    From: string;
    To: string;
    CallStatus: string;
    AccountSid: string;
    ApiVersion: string;
    Direction: string;
    ForwardedFrom?: string;
    CallerName?: string;
}
//# sourceMappingURL=api-types.d.ts.map