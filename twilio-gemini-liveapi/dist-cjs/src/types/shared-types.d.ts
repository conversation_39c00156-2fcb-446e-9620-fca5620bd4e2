import type { FastifyRequest } from 'fastify';
export interface VoiceCharacteristics {
    name: string;
    gender: 'Male' | 'Female';
    characteristics: string;
    pitch: string;
    timbre: string;
    persona: string;
    description?: string;
    useCase?: string;
}
export interface ValidationResult {
    isValid?: boolean;
    valid?: boolean;
    payload?: any;
    error?: string;
    disqualify?: boolean;
}
export interface AuthRequest extends FastifyRequest {
    auth?: {
        token: string;
        authenticated: boolean;
    };
    user?: {
        id: string;
        email: string;
        role: string;
        isAuthenticated: boolean;
    } | null;
}
export type ScriptType = 'incoming' | 'outbound';
export type CallMode = 'outbound' | 'inbound' | 'outbound-testing' | 'inbound-testing';
export type TwilioDirection = 'inbound' | 'outbound';
import { z } from 'zod';
export declare const VoiceSchema: z.Zod<PERSON>num<["Aoede", "Charon", "Fenrir", "<PERSON>re", "Puck", "<PERSON><PERSON>", "Orus", "Zephyr"]>;
export declare const ModelSchema: z.Zod<PERSON>num<["gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro", "gemini-2.5-flash-preview-native-audio-dialog", "gemini-2.0-flash-live-001", "gemini-2.0-flash", "gemini-2.0-flash-live-preview-04-09"]>;
export declare const ScriptTypeSchema: z.ZodEnum<["incoming", "outbound"]>;
export type GeminiVoice = z.infer<typeof VoiceSchema>;
export type GeminiModel = z.infer<typeof ModelSchema>;
//# sourceMappingURL=shared-types.d.ts.map