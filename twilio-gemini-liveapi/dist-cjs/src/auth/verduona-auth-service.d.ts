interface TokenPayload {
    sub: string;
    name: string;
    email?: string;
    roles?: string[];
    exp: number;
    iat: number;
    iss: string;
}
import type { ValidationResult } from '../types/shared-types';
interface TokenValidationResult extends ValidationResult {
    payload?: TokenPayload;
}
export declare class VerduonaAuthService {
    private readonly VERDUONA_AUTH_URL;
    private readonly JWT_SECRET;
    private readonly ISSUER;
    constructor();
    /**
     * Validate JWT token with proper signature and expiration checks
     */
    validateToken(token: string): Promise<TokenValidationResult>;
    /**
     * Decode JWT payload without verification (for inspection)
     */
    private decodePayload;
    /**
     * Verify JWT signature using HMAC SHA256
     */
    private verifySignature;
    /**
     * Validate token with remote Verduona auth service
     */
    private validateWithAuthService;
    /**
     * Extract user information from validated token
     */
    getUserInfo(payload: TokenPayload): {
        id: string;
        name: string;
        email: string | undefined;
        roles: string[];
        isAdmin: boolean;
    };
}
export {};
//# sourceMappingURL=verduona-auth-service.d.ts.map