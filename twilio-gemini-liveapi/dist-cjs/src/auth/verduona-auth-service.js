"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerduonaAuthService = void 0;
const crypto = __importStar(require("crypto"));
const logger_1 = require("../utils/logger");
class VerduonaAuthService {
    VERDUONA_AUTH_URL;
    JWT_SECRET;
    ISSUER = 'verduona.com';
    constructor() {
        this.VERDUONA_AUTH_URL = process.env.VERDUONA_AUTH_URL || 'https://verduona.com/api/auth';
        this.JWT_SECRET = process.env.JWT_SECRET || process.env.VERDUONA_JWT_SECRET || '';
        if (!this.JWT_SECRET) {
            logger_1.apiLogger.warn('⚠️ JWT_SECRET not configured - token validation will use basic checks only');
        }
    }
    /**
     * Validate JWT token with proper signature and expiration checks
     */
    async validateToken(token) {
        try {
            // Basic format validation
            const parts = token.split('.');
            if (parts.length !== 3) {
                return { isValid: false, error: 'Invalid JWT format' };
            }
            // Decode payload
            const payload = this.decodePayload(token);
            if (!payload) {
                return { isValid: false, error: 'Invalid token payload' };
            }
            // Check expiration
            const now = Math.floor(Date.now() / 1000);
            if (payload.exp && payload.exp < now) {
                logger_1.apiLogger.warn('🔐 Token expired', {
                    expiredAt: new Date(payload.exp * 1000).toISOString(),
                    now: new Date(now * 1000).toISOString()
                });
                return { isValid: false, error: 'Token expired' };
            }
            // Check issuer
            if (payload.iss && payload.iss !== this.ISSUER) {
                logger_1.apiLogger.warn('🔐 Invalid token issuer', {
                    expected: this.ISSUER,
                    received: payload.iss
                });
                return { isValid: false, error: 'Invalid token issuer' };
            }
            // Verify signature if JWT_SECRET is available
            if (this.JWT_SECRET) {
                const isSignatureValid = this.verifySignature(token);
                if (!isSignatureValid) {
                    logger_1.apiLogger.warn('🔐 Invalid token signature');
                    return { isValid: false, error: 'Invalid token signature' };
                }
            }
            // Optional: Validate with remote auth service
            const isRemoteValid = await this.validateWithAuthService(token);
            if (!isRemoteValid) {
                return { isValid: false, error: 'Token not valid with auth service' };
            }
            logger_1.apiLogger.info('🔐 Token validation successful', {
                user: payload.sub,
                email: payload.email,
                roles: payload.roles?.length || 0
            });
            return { isValid: true, payload };
        }
        catch (error) {
            logger_1.apiLogger.error('🔐 Token validation error:', error);
            return { isValid: false, error: 'Token validation failed' };
        }
    }
    /**
     * Decode JWT payload without verification (for inspection)
     */
    decodePayload(token) {
        try {
            const parts = token.split('.');
            const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
            return payload;
        }
        catch (error) {
            logger_1.apiLogger.error('🔐 Failed to decode token payload:', error);
            return null;
        }
    }
    /**
     * Verify JWT signature using HMAC SHA256
     */
    verifySignature(token) {
        try {
            if (!this.JWT_SECRET) {
                return true; // Skip if no secret configured
            }
            const parts = token.split('.');
            const header = parts[0];
            const payload = parts[1];
            const signature = parts[2];
            // Create expected signature
            const data = `${header}.${payload}`;
            const expectedSignature = crypto
                .createHmac('sha256', this.JWT_SECRET)
                .update(data)
                .digest('base64url');
            return signature === expectedSignature;
        }
        catch (error) {
            logger_1.apiLogger.error('🔐 Signature verification failed:', error);
            return false;
        }
    }
    /**
     * Validate token with remote Verduona auth service
     */
    async validateWithAuthService(token) {
        try {
            // For now, skip remote validation and rely on JWT signature verification
            // In production, you would implement actual HTTP call to main auth service
            logger_1.apiLogger.info('🔐 Remote auth service validation skipped (using JWT verification)', {
                tokenLength: token.length
            });
            return true;
        }
        catch (error) {
            logger_1.apiLogger.error('❌ Error validating with auth service:', error);
            return false;
        }
    }
    /**
     * Extract user information from validated token
     */
    getUserInfo(payload) {
        return {
            id: payload.sub,
            name: payload.name,
            email: payload.email,
            roles: payload.roles || [],
            isAdmin: payload.roles?.includes('admin') || false
        };
    }
}
exports.VerduonaAuthService = VerduonaAuthService;
//# sourceMappingURL=verduona-auth-service.js.map