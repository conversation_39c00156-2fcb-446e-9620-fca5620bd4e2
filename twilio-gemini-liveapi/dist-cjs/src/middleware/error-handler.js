"use strict";
/**
 * Global Error Handler Middleware
 * Ensures all errors are properly logged and propagated
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createErrorResponse = createErrorResponse;
exports.asyncHandler = asyncHandler;
exports.handleWebSocketError = handleWebSocketError;
exports.handlePromiseRejection = handlePromiseRejection;
exports.registerErrorHandlers = registerErrorHandlers;
exports.registerErrorLoggingHook = registerErrorLoggingHook;
exports.formatValidationError = formatValidationError;
const logger_1 = require("../utils/logger");
/**
 * Creates a standard error response
 */
function createErrorResponse(error, statusCode = 500, requestId) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorCode = error?.code || 'INTERNAL_ERROR';
    return {
        success: false,
        error: {
            message: errorMessage,
            code: errorCode,
            statusCode,
            timestamp: new Date().toISOString(),
            requestId,
            details: process.env.NODE_ENV === 'development' ? error : undefined
        }
    };
}
/**
 * Async error wrapper for route handlers
 */
function asyncHandler(fn) {
    return async (request, reply) => {
        try {
            const result = await fn(request, reply);
            // If the handler didn't send a response, send the result
            if (!reply.sent && result !== undefined) {
                reply.send(result);
            }
        }
        catch (error) {
            // Log the error with context
            const errorData = {
                error: error instanceof Error ? error : new Error(String(error)),
                method: request.method,
                url: request.url,
                params: request.params,
                query: request.query,
                headers: {
                    'user-agent': request.headers['user-agent'],
                    'content-type': request.headers['content-type']
                }
            };
            logger_1.logger.error('❌ Route handler error', errorData);
            // Send standardized error response
            const statusCode = error?.statusCode || 500;
            const errorResponse = createErrorResponse(error, statusCode, request.id);
            reply.status(statusCode).send(errorResponse);
        }
    };
}
/**
 * WebSocket error handler
 */
function handleWebSocketError(error, sessionId, operation) {
    logger_1.logger.error(`❌ WebSocket error during ${operation}`, {
        error: error instanceof Error ? error : new Error(String(error))
    }, sessionId);
    // Note: Custom error event emission can be added here if needed
    // For now, we rely on the structured logging
}
/**
 * Promise rejection handler
 */
function handlePromiseRejection(promise, context) {
    return promise.catch((error) => {
        logger_1.logger.error(`❌ Unhandled promise rejection in ${context}`, {
            error: error instanceof Error ? error : new Error(String(error))
        });
        throw error; // Re-throw to maintain error flow
    });
}
/**
 * Register global error handlers
 */
function registerErrorHandlers(fastify) {
    // Fastify error handler
    fastify.setErrorHandler((error, request, reply) => {
        logger_1.logger.error('❌ Fastify error', {
            error,
            method: request.method,
            url: request.url,
            statusCode: error.statusCode || 500
        });
        const errorResponse = createErrorResponse(error, error.statusCode || 500, request.id);
        reply.status(error.statusCode || 500).send(errorResponse);
    });
    // Not found handler
    fastify.setNotFoundHandler((request, reply) => {
        logger_1.logger.warn('⚠️ Route not found', {
            method: request.method,
            url: request.url
        });
        reply.status(404).send({
            success: false,
            error: {
                message: 'Route not found',
                code: 'NOT_FOUND',
                statusCode: 404,
                timestamp: new Date().toISOString(),
                requestId: request.id
            }
        });
    });
    // Global unhandled rejection handler
    process.on('unhandledRejection', (reason, promise) => {
        logger_1.logger.error('❌ Unhandled Promise Rejection', {
            error: reason instanceof Error ? reason : new Error(String(reason)),
            promise: promise.toString()
        });
    });
    // Global uncaught exception handler
    process.on('uncaughtException', (error) => {
        logger_1.logger.error('❌ Uncaught Exception', {
            error,
            fatal: true
        });
        // Give time for logs to flush before exiting
        setTimeout(() => {
            process.exit(1);
        }, 1000);
    });
    logger_1.logger.info('✅ Error handlers registered');
}
/**
 * Error logging hook
 */
function registerErrorLoggingHook(fastify) {
    fastify.addHook('onSend', async (request, reply, payload) => {
        const statusCode = reply.statusCode;
        if (statusCode >= 400) {
            logger_1.logger.warn(`⚠️ Error response sent`, {
                method: request.method,
                url: request.url,
                statusCode,
                error: payload
            });
        }
        return payload;
    });
}
/**
 * Validation error formatter
 */
function formatValidationError(error) {
    if (error.validation) {
        return {
            success: false,
            error: {
                message: 'Validation failed',
                code: 'VALIDATION_ERROR',
                statusCode: 400,
                timestamp: new Date().toISOString(),
                details: error.validation
            }
        };
    }
    return createErrorResponse(error, 400);
}
//# sourceMappingURL=error-handler.js.map