"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateNginxAuth = validateNginxAuth;
exports.requireNginxAuth = requireNginxAuth;
exports.getAuthenticatedUser = getAuthenticatedUser;
const logger_1 = require("../utils/logger");
/**
 * Middleware for nginx auth_request integration
 * Trusts user headers set by nginx after auth-service validation
 */
async function validateNginxAuth(request, reply) {
    // Skip auth for WebSocket connections
    if (request.headers && request.headers.upgrade === 'websocket') {
        return;
    }
    // Skip auth for CORS preflight OPTIONS requests
    if (request.method === 'OPTIONS') {
        return;
    }
    // Public paths that don't need authentication
    const publicPaths = [
        '/incoming-call', // Twilio webhook (verified by Twilio signature)
        '/call-status', // Twilio webhook (verified by <PERSON>wi<PERSON> signature)
        '/recording-status', // Twilio webhook (verified by <PERSON><PERSON><PERSON> signature)
        '/health', // Health check endpoint
        '/ready', // Kubernetes readiness check
        '/live', // Kubernetes liveness check
        '/available-voices', // Voice configuration
        '/available-models', // Model configuration
        '/audio-settings', // Audio settings
        '/get-campaign-script', // Campaign script loading (CRITICAL: needed for script loading)
        '/incoming-campaign', // Incoming campaign scripts (CRITICAL: needed for script loading)
        '/api/validate-token', // Token validation endpoint
    ];
    // WebSocket paths that need special handling
    const websocketPaths = [
        '/media-stream' // Twilio WebSocket (verified by Twilio)
    ];
    if (publicPaths.some(path => request.url.startsWith(path))) {
        return; // Continue without validation for public endpoints
    }
    if (websocketPaths.some(path => request.url.startsWith(path))) {
        return; // WebSocket auth handled separately
    }
    try {
        // Extract user information from nginx-set headers
        const userId = request.headers['x-user-id'];
        const userEmail = request.headers['x-user-email'];
        const userRole = request.headers['x-user-role'];
        const authStatus = request.headers['x-auth-status'];
        // Check if nginx provided authentication headers
        if (!userId || !userEmail || authStatus !== 'authenticated') {
            // Check if we're in development mode and allow bypass
            const isDevelopment = process.env.NODE_ENV !== 'production';
            const allowUnauthenticated = process.env.ALLOW_UNAUTHENTICATED === 'true';
            if (isDevelopment && allowUnauthenticated) {
                logger_1.authLogger.warn('No nginx auth headers - allowing for development mode', {
                    url: request.url,
                    method: request.method,
                    headers: {
                        userId: userId ? 'present' : 'missing',
                        userEmail: userEmail ? 'present' : 'missing',
                        authStatus: authStatus || 'missing'
                    }
                });
                return;
            }
            logger_1.authLogger.error('Missing nginx auth headers for protected endpoint', {
                url: request.url,
                method: request.method,
                ip: request.ip,
                headers: {
                    userId: userId ? 'present' : 'missing',
                    userEmail: userEmail ? 'present' : 'missing',
                    authStatus: authStatus || 'missing'
                }
            });
            await reply.code(401).send({
                error: 'Authentication required',
                message: 'Access denied by authentication service'
            });
            return;
        }
        // Attach user info to request for downstream use
        request.user = {
            id: userId,
            email: userEmail,
            role: userRole || 'authenticated',
            authenticated: true
        };
        logger_1.authLogger.info('Nginx auth validation passed', {
            userId,
            userEmail,
            userRole: userRole || 'authenticated',
            url: request.url,
            method: request.method
        });
    }
    catch (error) {
        logger_1.authLogger.error('Nginx auth validation failed', error instanceof Error ? error : new Error(String(error)));
        await reply.code(401).send({
            error: 'Authentication failed',
            message: 'Invalid authentication headers'
        });
        return;
    }
}
/**
 * Middleware to require nginx auth for specific routes
 */
function requireNginxAuth(request, reply, done) {
    validateNginxAuth(request, reply)
        .then(() => done())
        .catch(() => {
        if (!reply.sent) {
            reply.code(401).send({ error: 'Authentication required' });
        }
        done();
    });
}
/**
 * Get authenticated user from request (helper function)
 */
function getAuthenticatedUser(request) {
    return request.user || null;
}
//# sourceMappingURL=nginx-auth.js.map