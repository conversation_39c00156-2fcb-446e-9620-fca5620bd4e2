{"version": 3, "file": "error-handler.js", "sourceRoot": "", "sources": ["../../../src/middleware/error-handler.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAoBH,kDAmBC;AAKD,oCAoCC;AAKD,oDAWC;AAKD,wDAUC;AAKD,sDA4DC;AAKD,4DAaC;AAKD,sDAeC;AAnND,4CAAyC;AAczC;;GAEG;AACH,SAAgB,mBAAmB,CAC/B,KAAsB,EACtB,aAAqB,GAAG,EACxB,SAAkB;IAElB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5E,MAAM,SAAS,GAAI,KAAa,EAAE,IAAI,IAAI,gBAAgB,CAAC;IAE3D,OAAO;QACH,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACH,OAAO,EAAE,YAAY;YACrB,IAAI,EAAE,SAAS;YACf,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS;YACT,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACtE;KACJ,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CACxB,EAAkE;IAElE,OAAO,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QAC1D,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACxC,yDAAyD;YACzD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACtC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,6BAA6B;YAC7B,MAAM,SAAS,GAAG;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChE,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,OAAO,EAAE;oBACL,YAAY,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;oBAC3C,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC;iBAClD;aACJ,CAAC;YACF,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC;YAEjD,mCAAmC;YACnC,MAAM,UAAU,GAAI,KAAa,EAAE,UAAU,IAAI,GAAG,CAAC;YACrD,MAAM,aAAa,GAAG,mBAAmB,CACrC,KAAK,EACL,UAAU,EACT,OAAe,CAAC,EAAE,CACtB,CAAC;YAEF,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC;IACL,CAAC,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAChC,KAAsB,EACtB,SAAiB,EACjB,SAAiB;IAEjB,eAAM,CAAC,KAAK,CAAC,4BAA4B,SAAS,EAAE,EAAE;QAClD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACnE,EAAE,SAAS,CAAC,CAAC;IAEd,gEAAgE;IAChE,6CAA6C;AACjD,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CAClC,OAAqB,EACrB,OAAe;IAEf,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QAC3B,eAAM,CAAC,KAAK,CAAC,oCAAoC,OAAO,EAAE,EAAE;YACxD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACnE,CAAC,CAAC;QACH,MAAM,KAAK,CAAC,CAAC,kCAAkC;IACnD,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,OAAwB;IAC1D,wBAAwB;IACxB,OAAO,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QAC9C,eAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE;YAC5B,KAAK;YACL,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,GAAG;SACtC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,mBAAmB,CACrC,KAAK,EACL,KAAK,CAAC,UAAU,IAAI,GAAG,EACtB,OAAe,CAAC,EAAE,CACtB,CAAC;QAEF,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,OAAO,CAAC,kBAAkB,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;QAC1C,eAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC9B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;SACnB,CAAC,CAAC;QAEH,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACH,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE,WAAW;gBACjB,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAG,OAAe,CAAC,EAAE;aACjC;SACJ,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,qCAAqC;IACrC,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;QACjD,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;YAC1C,KAAK,EAAE,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnE,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;SAC9B,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;QACtC,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;YACjC,KAAK;YACL,KAAK,EAAE,IAAI;SACd,CAAC,CAAC;QAEH,6CAA6C;QAC7C,UAAU,CAAC,GAAG,EAAE;YACZ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC,CAAC,CAAC;IAEH,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;AAC/C,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CAAC,OAAwB;IAC7D,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;QACxD,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QACpC,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAClC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,UAAU;gBACV,KAAK,EAAE,OAAO;aACjB,CAAC,CAAC;QACP,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,KAAU;IAC5C,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,OAAO;YACH,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACH,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE,kBAAkB;gBACxB,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,KAAK,CAAC,UAAU;aAC5B;SACJ,CAAC;IACN,CAAC;IAED,OAAO,mBAAmB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC3C,CAAC"}