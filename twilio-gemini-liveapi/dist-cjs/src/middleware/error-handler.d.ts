/**
 * Global Error Handler Middleware
 * Ensures all errors are properly logged and propagated
 */
import type { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
export interface StandardErrorResponse {
    success: false;
    error: {
        message: string;
        code?: string;
        statusCode: number;
        timestamp: string;
        requestId?: string;
        details?: any;
    };
}
/**
 * Creates a standard error response
 */
export declare function createErrorResponse(error: Error | unknown, statusCode?: number, requestId?: string): StandardErrorResponse;
/**
 * Async error wrapper for route handlers
 */
export declare function asyncHandler(fn: (request: FastifyRequest, reply: FastifyReply) => Promise<any>): (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
/**
 * WebSocket error handler
 */
export declare function handleWebSocketError(error: Error | unknown, sessionId: string, operation: string): void;
/**
 * Promise rejection handler
 */
export declare function handlePromiseRejection(promise: Promise<any>, context: string): Promise<any>;
/**
 * Register global error handlers
 */
export declare function registerErrorHandlers(fastify: FastifyInstance): void;
/**
 * Error logging hook
 */
export declare function registerErrorLoggingHook(fastify: FastifyInstance): void;
/**
 * Validation error formatter
 */
export declare function formatValidationError(error: any): StandardErrorResponse;
//# sourceMappingURL=error-handler.d.ts.map