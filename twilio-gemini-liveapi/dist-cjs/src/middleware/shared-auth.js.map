{"version": 3, "file": "shared-auth.js", "sourceRoot": "", "sources": ["../../../src/middleware/shared-auth.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAwDH,gDAeC;AAKD,0CAGC;AAKD,kCAcC;AAKD,sCAQC;AAKD,8CAWC;AAKD,gDAyFC;AAKD,oDAOC;AAKD,oDAEC;AA7OD,2BAAkC;AAClC,+BAA4B;AAC5B,6BAAoC;AACpC,+BAA+B;AAE/B,MAAM,UAAU,GAAG,IAAA,mBAAa,EAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClD,MAAM,SAAS,GAAG,IAAA,cAAO,EAAC,UAAU,CAAC,CAAC;AAEtC,6CAA6C;AAC7C,MAAM,cAAc,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,oCAAoC,CAAC,CAAC;AAC7E,IAAI,UAAe,CAAC;AAEpB,IAAI,CAAC;IACH,gDAAgD;IAChD,MAAM,aAAa,GAAG,IAAA,iBAAY,EAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IAE5D,8DAA8D;IAC9D,MAAM,aAAa,GAAG;MAClB,aAAa;;;;;;;;;;;;;GAahB,CAAC;IAEF,uEAAuE;IACvE,wEAAwE;AAE1E,CAAC;AAAC,OAAO,KAAK,EAAE,CAAC;IACf,OAAO,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAC;AACjG,CAAC;AAYD;;GAEG;AACH,SAAgB,kBAAkB,CAAC,OAAuB;IACxD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;IACtD,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAW,CAAC;IAC5D,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAW,CAAC;IAE1D,IAAI,MAAM,IAAI,SAAS,EAAE,CAAC;QACxB,OAAO;YACL,EAAE,EAAE,MAAM;YACV,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,QAAQ,IAAI,eAAe;YACjC,eAAe,EAAE,IAAI;SACtB,CAAC;IACJ,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAgB,eAAe,CAAC,OAAuB;IACrD,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACzC,OAAO,IAAI,KAAK,IAAI,CAAC;AACvB,CAAC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,OAAuB;IACjD,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAEzC,IAAI,IAAI,EAAE,CAAC;QACT,OAAO,IAAI,CAAC;IACd,CAAC;IAED,+BAA+B;IAC/B,OAAO;QACL,EAAE,EAAE,WAAW;QACf,KAAK,EAAE,EAAE;QACT,IAAI,EAAE,WAAW;QACjB,eAAe,EAAE,KAAK;KACvB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,aAAa,CAAC,OAAuB,EAAE,cAAsB,eAAe;IAC1F,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAEzC,IAAI,IAAI,EAAE,CAAC;QACT,OAAO,CAAC,GAAG,CAAC,MAAM,WAAW,yBAAyB,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACnF,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,QAAQ,WAAW,4CAA4C,CAAC,CAAC;IAC/E,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,OAAuB;IACvD,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAElC,OAAO;QACL,eAAe,EAAE,IAAI,CAAC,eAAe;QACrC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAC3B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAC,CAAC,IAAI;KACT,CAAC;AACJ,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,kBAAkB,CAAC,OAAoB,EAAE,KAAmB;IAChF,sCAAsC;IACtC,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,KAAK,WAAW,EAAE,CAAC;QAC/D,OAAO;IACT,CAAC;IAED,gDAAgD;IAChD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACjC,OAAO;IACT,CAAC;IAED,mEAAmE;IACnE,MAAM,WAAW,GAAG;QAClB,gBAAgB,EAAS,gDAAgD;QACzE,cAAc,EAAW,gDAAgD;QACzE,mBAAmB,EAAM,gDAAgD;QACzE,SAAS,EAAgB,wBAAwB;QACjD,QAAQ,EAAiB,6BAA6B;QACtD,OAAO,EAAkB,4BAA4B;QACrD,mBAAmB,EAAM,sBAAsB;QAC/C,mBAAmB,EAAM,sBAAsB;QAC/C,iBAAiB,EAAQ,iBAAiB;QAC1C,sBAAsB,EAAG,gEAAgE;QACzF,oBAAoB,EAAK,kEAAkE;QAC3F,qBAAqB,EAAI,4BAA4B;QACrD,wBAAwB,EAAE,qEAAqE;KAChG,CAAC;IAEF,6CAA6C;IAC7C,MAAM,cAAc,GAAG;QACrB,eAAe,CAAQ,wCAAwC;KAChE,CAAC;IAEF,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAC3D,OAAO,CAAC,mDAAmD;IAC7D,CAAC;IAED,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAC9D,OAAO,CAAC,oCAAoC;IAC9C,CAAC;IAED,IAAI,CAAC;QACH,oEAAoE;QACpE,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAEzC,4BAA4B;QAC5B,aAAa,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QAExC,iDAAiD;QACjD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,+CAA+C;YAC/C,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC;YAC5D,MAAM,oBAAoB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,MAAM,CAAC;YAE1E,mDAAmD;YACnD,IAAI,aAAa,IAAI,oBAAoB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBACnF,OAAO,CAAC,IAAI,CAAC,2DAA2D,EAAE;oBACxE,GAAG,EAAE,OAAO,CAAC,GAAG;oBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;iBACvB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,OAAO,CAAC,KAAK,CAAC,sDAAsD,EAAE;gBACpE,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,EAAE,EAAE,OAAO,CAAC,EAAE;aACf,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACzB,KAAK,EAAE,yBAAyB;gBAChC,OAAO,EAAE,yCAAyC;aACnD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,iDAAiD;QACjD,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QAEpB,OAAO,CAAC,GAAG,CAAC,uCAAuC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAEnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACzB,KAAK,EAAE,uBAAuB;YAC9B,OAAO,EAAE,gCAAgC;SAC1C,CAAC,CAAC;QACH,OAAO;IACT,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAAC,IAAS,EAAE,OAAuB;IACrE,MAAM,WAAW,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAE/C,OAAO;QACL,GAAG,IAAI;QACP,IAAI,EAAE,WAAW;KAClB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAAC,OAAoB;IACvD,OAAO,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC;AAC9B,CAAC"}