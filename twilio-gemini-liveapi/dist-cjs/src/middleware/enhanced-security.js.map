{"version": 3, "file": "enhanced-security.js", "sourceRoot": "", "sources": ["../../../src/middleware/enhanced-security.ts"], "names": [], "mappings": ";;;AAEA,qDAAiD;AACjD,4CAAkD;AA+BlD;;GAEG;AACH,MAAa,gBAAgB;IACjB,MAAM,CAAiB;IACvB,OAAO,CAAkB;IACzB,aAAa,GAAwB,IAAI,GAAG,EAAE,CAAC;IAC/C,cAAc,GAA+B,IAAI,GAAG,EAAE,CAAC;IAE/D,YAAY,SAAkC,EAAE;QAC5C,IAAI,CAAC,MAAM,GAAG;YACV,eAAe,EAAE,IAAI;YACrB,qBAAqB,EAAE,IAAI;YAC3B,oBAAoB,EAAE,IAAI;YAC1B,oBAAoB,EAAE,EAAE;YACxB,kBAAkB,EAAE,IAAI;YACxB,cAAc,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;YACzC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,EAAE,wBAAwB,CAAC;YACzH,cAAc,EAAE,CAAC,WAAW,EAAE,KAAK,CAAC;YACpC,GAAG,MAAM;SACZ,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG;YACX,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,mBAAmB,EAAE,CAAC;YACtB,kBAAkB,EAAE,CAAC;YACrB,kBAAkB,EAAE,CAAC;YACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;QAEF,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC5B,mCAAmC;QACnC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE;YAC9B,QAAQ,EAAE,QAAQ;YAClB,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,KAAK,CAAC,WAAW;SAC9B,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,EAAE;YACtC,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,KAAK,CAAC,WAAW;SAC9B,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,EAAE;YACtC,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,GAAG;YAChB,QAAQ,EAAE,KAAK,CAAC,WAAW;SAC9B,CAAC,CAAC;QAEH,8CAA8C;QAC9C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE;YAC/B,QAAQ,EAAE,SAAS;YACnB,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,MAAM,CAAC,YAAY;SAChC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,UAAU;QACN,OAAO,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAiB,EAAE;YACzE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAE7B,IAAI,CAAC;gBACD,sCAAsC;gBACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAE3C,4BAA4B;gBAC5B,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAChC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,8BAA8B,EAAE,QAAQ,CAAC,CAAC;oBACnE,OAAO;gBACX,CAAC;gBAED,wBAAwB;gBACxB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;oBACrC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,2BAA2B,EAAE,QAAQ,CAAC,CAAC;oBAChE,OAAO;gBACX,CAAC;gBAED,sBAAsB;gBACtB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC;oBACzE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,qBAAqB,EAAE,QAAQ,CAAC,CAAC;oBAC1D,OAAO;gBACX,CAAC;gBAED,mCAAmC;gBACnC,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;oBACpE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,yBAAyB,EAAE,QAAQ,CAAC,CAAC;oBAC9D,OAAO;gBACX,CAAC;gBAED,yBAAyB;gBACzB,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;oBACnC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;gBAC/D,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,wBAAe,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9G,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,uBAAuB,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;YACjF,CAAC;QACL,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,OAAuB;QACvC,2DAA2D;QAC3D,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAW,CAAC;QAClE,IAAI,YAAY,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YACzD,sDAAsD;YACtD,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC;gBACnB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC3C,OAAO,EAAE,CAAC;gBACd,CAAC;YACL,CAAC;QACL,CAAC;QAED,yBAAyB;QACzB,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;QACtD,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACzD,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,yCAAyC;QACzC,OAAO,OAAO,CAAC,MAAM,CAAC,aAAa,IAAI,SAAS,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,EAAU;QAC7B,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QACxD,OAAO,eAAe,GAAG,EAAE,CAAC,CAAC,uCAAuC;IACxE,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,EAAU;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAuB;QAC/C,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC;QAC7E,OAAO,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAuB,EAAE,QAAgB;QAC5D,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QAExB,gCAAgC;QAChC,IAAI,IAA+B,CAAC;QACpC,KAAK,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,CAAC;gBACpC,IAAI,GAAG,UAAU,CAAC;gBAClB,MAAM;YACV,CAAC;QACL,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,IAAI,GAAG;gBACH,QAAQ,EAAE,SAAS;gBACnB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAC7C,QAAQ,EAAE,KAAK;aAClB,CAAC;QACN,CAAC;QAED,MAAM,YAAY,GAAG,GAAG,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpD,MAAM,OAAO,GAAG,8BAAa,CAAC,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE5F,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YACnC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,GAAW,EAAE,OAAe;QAC/C,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,GAAG,KAAK,OAAO,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,OAAuB;QACzC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAW,CAAC;QAEjC,IAAI,CAAC;YACD,sCAAsC;YACtC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC;YAED,yCAAyC;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAE3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,IAAS,EAAE,GAAW;QAC3C,IAAI,CAAC,IAAI,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC,CAAC,oCAAoC;QAE9D,6BAA6B;QAC7B,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,8BAAa,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3G,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,8BAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,8BAAa,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACzD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,8BAAa,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACzD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,8BAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,IAAS;QACnC,IAAI,CAAC,IAAI,EAAE,CAAC;YAAA,OAAO,KAAK,CAAC;QAAA,CAAC;QAE1B,yBAAyB;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,8BAAa,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAChE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,8BAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,8BAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YACzD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,OAAuB;QACpD,uCAAuC;QACvC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;QACxC,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;QAE9C,IAAI,OAAO,KAAK,WAAW,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,6BAA6B;QAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;QACtC,IAAI,MAAM,IAAI,CAAC,8BAAa,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;YAC9E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,IAAS;QAClC,IAAI,CAAC,IAAI,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAEzB,sCAAsC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACrC,MAAM,kBAAkB,GAAG;YACvB,UAAU;YACV,cAAc;YACd,kBAAkB;YAClB,YAAY;YACZ,UAAU;YACV,WAAW;YACX,SAAS;YACT,aAAa;YACb,WAAW;SACd,CAAC;QAEF,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,KAAmB,EAAE,MAAc,EAAE,QAAgB;QACtE,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAE/B,wBAAe,CAAC,IAAI,CAAC,kCAAkC,QAAQ,KAAK,MAAM,EAAE,CAAC,CAAC;QAE9E,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACjB,KAAK,EAAE,sCAAsC;YAC7C,IAAI,EAAE,oBAAoB;YAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,OAAuB,EAAE,QAAgB,EAAE,QAAgB;QAC1E,wBAAe,CAAC,IAAI,CAAC,mBAAmB,EAAE;YACtC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,EAAE,EAAE,QAAQ;YACZ,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;YACxC,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,UAAU;QACN,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,YAAY;QACR,IAAI,CAAC,OAAO,GAAG;YACX,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,mBAAmB,EAAE,CAAC;YACtB,kBAAkB,EAAE,CAAC;YACrB,kBAAkB,EAAE,CAAC;YACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACN,CAAC;IAED;;OAEG;IACH,oBAAoB;QAChB,wCAAwC;QACxC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACJ;AAxYD,4CAwYC;AAED,4BAA4B;AACf,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC;AAEvD,qCAAqC;AACrC,WAAW,CAAC,GAAG,EAAE,CAAC,wBAAgB,CAAC,oBAAoB,EAAE,EAAE,OAAO,CAAC,CAAC"}