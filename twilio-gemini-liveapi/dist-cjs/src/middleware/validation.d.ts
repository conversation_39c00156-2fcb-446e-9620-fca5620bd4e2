import { FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
/**
 * Generic validation middleware factory for Fastify routes
 */
export declare function validateBody<T extends z.ZodType>(schema: T): (request: FastifyRequest, reply: FastifyReply) => Promise<undefined>;
/**
 * Validate query parameters
 */
export declare function validateQuery<T extends z.ZodType>(schema: T): (request: FastifyRequest, reply: FastifyReply) => Promise<undefined>;
/**
 * Validate WebSocket messages
 */
export declare function validateWebSocketMessage<T extends z.ZodType>(schema: T, message: unknown): {
    success: true;
    data: z.infer<T>;
} | {
    success: false;
    error: string;
};
/**
 * Sanitize string input to prevent XSS and injection attacks
 */
export declare function sanitizeString(input: string): string;
/**
 * Validate and sanitize phone numbers
 */
export declare function validatePhoneNumber(phone: string): {
    valid: boolean;
    normalized?: string;
    error?: string;
};
/**
 * Rate limiting validation
 */
export declare function validateRateLimit(identifier: string, limit: number, windowMs: number, storage: Map<string, {
    count: number;
    resetTime: number;
}>): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
};
/**
 * Clean up expired rate limit entries
 */
export declare function cleanupRateLimit(storage: Map<string, {
    count: number;
    resetTime: number;
}>): void;
/**
 * Validate file upload size and type
 */
export declare function validateFileUpload(file: {
    size: number;
    mimetype: string;
}, maxSize: number, allowedTypes: string[]): {
    valid: boolean;
    error?: string;
};
//# sourceMappingURL=validation.d.ts.map