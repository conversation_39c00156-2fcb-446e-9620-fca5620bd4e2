{"version": 3, "file": "nginx-auth.js", "sourceRoot": "", "sources": ["../../../src/middleware/nginx-auth.ts"], "names": [], "mappings": ";;AAgBA,8CA4GC;AAKD,4CAaC;AAKD,oDAEC;AApJD,4CAA6C;AAW7C;;;GAGG;AACI,KAAK,UAAU,iBAAiB,CAAC,OAAyB,EAAE,KAAmB;IACpF,sCAAsC;IACtC,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,KAAK,WAAW,EAAE,CAAC;QAC/D,OAAO;IACT,CAAC;IAED,gDAAgD;IAChD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACjC,OAAO;IACT,CAAC;IAED,8CAA8C;IAC9C,MAAM,WAAW,GAAG;QAClB,gBAAgB,EAAS,gDAAgD;QACzE,cAAc,EAAW,gDAAgD;QACzE,mBAAmB,EAAM,gDAAgD;QACzE,SAAS,EAAgB,wBAAwB;QACjD,QAAQ,EAAiB,6BAA6B;QACtD,OAAO,EAAkB,4BAA4B;QACrD,mBAAmB,EAAM,sBAAsB;QAC/C,mBAAmB,EAAM,sBAAsB;QAC/C,iBAAiB,EAAQ,iBAAiB;QAC1C,sBAAsB,EAAG,gEAAgE;QACzF,oBAAoB,EAAK,kEAAkE;QAC3F,qBAAqB,EAAI,4BAA4B;KACtD,CAAC;IAEF,6CAA6C;IAC7C,MAAM,cAAc,GAAG;QACrB,eAAe,CAAQ,wCAAwC;KAChE,CAAC;IAEF,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAC3D,OAAO,CAAC,mDAAmD;IAC7D,CAAC;IAED,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAC9D,OAAO,CAAC,oCAAoC;IAC9C,CAAC;IAED,IAAI,CAAC;QACH,kDAAkD;QAClD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;QACtD,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAW,CAAC;QAC5D,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAW,CAAC;QAC1D,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,eAAe,CAAW,CAAC;QAE9D,iDAAiD;QACjD,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,IAAI,UAAU,KAAK,eAAe,EAAE,CAAC;YAC5D,sDAAsD;YACtD,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC;YAC5D,MAAM,oBAAoB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,MAAM,CAAC;YAE1E,IAAI,aAAa,IAAI,oBAAoB,EAAE,CAAC;gBAC1C,mBAAU,CAAC,IAAI,CAAC,uDAAuD,EAAE;oBACvE,GAAG,EAAE,OAAO,CAAC,GAAG;oBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,OAAO,EAAE;wBACP,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;wBACtC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;wBAC5C,UAAU,EAAE,UAAU,IAAI,SAAS;qBACpC;iBACF,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,mBAAU,CAAC,KAAK,CAAC,mDAAmD,EAAE;gBACpE,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,OAAO,EAAE;oBACP,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;oBACtC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;oBAC5C,UAAU,EAAE,UAAU,IAAI,SAAS;iBACpC;aACF,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACzB,KAAK,EAAE,yBAAyB;gBAChC,OAAO,EAAE,yCAAyC;aACnD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,iDAAiD;QACjD,OAAO,CAAC,IAAI,GAAG;YACb,EAAE,EAAE,MAAM;YACV,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,QAAQ,IAAI,eAAe;YACjC,aAAa,EAAE,IAAI;SACpB,CAAC;QAEF,mBAAU,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC9C,MAAM;YACN,SAAS;YACT,QAAQ,EAAE,QAAQ,IAAI,eAAe;YACrC,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,mBAAU,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5G,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACzB,KAAK,EAAE,uBAAuB;YAC9B,OAAO,EAAE,gCAAgC;SAC1C,CAAC,CAAC;QACH,OAAO;IACT,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,OAAyB,EACzB,KAAmB,EACnB,IAA2B;IAE3B,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC;SAC9B,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;SAClB,KAAK,CAAC,GAAG,EAAE;QACV,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAChB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,EAAE,CAAC;IACT,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAAC,OAAyB;IAC5D,OAAO,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC;AAC9B,CAAC"}