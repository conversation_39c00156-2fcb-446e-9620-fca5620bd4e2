import { FastifyRequest, FastifyReply } from 'fastify';
export interface SecurityConfig {
    enableRateLimit: boolean;
    enableInputValidation: boolean;
    enableRequestLogging: boolean;
    maxRequestsPerMinute: number;
    maxRequestsPerHour: number;
    maxPayloadSize: number;
    allowedOrigins: string[];
    trustedProxies: string[];
}
export interface RateLimitRule {
    endpoint: string;
    maxRequests: number;
    windowMs: number;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
}
export interface SecurityMetrics {
    totalRequests: number;
    blockedRequests: number;
    rateLimitViolations: number;
    validationFailures: number;
    suspiciousActivity: number;
    lastReset: number;
}
/**
 * Enhanced security middleware with comprehensive protection
 */
export declare class EnhancedSecurity {
    private config;
    private metrics;
    private suspiciousIPs;
    private rateLimitRules;
    constructor(config?: Partial<SecurityConfig>);
    /**
     * Initialize rate limit rules for different endpoints
     */
    private initializeRateLimitRules;
    /**
     * Main security middleware
     */
    middleware(): (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    /**
     * Get client IP address considering proxies
     */
    private getClientIP;
    /**
     * Check if IP has suspicious activity
     */
    private isSuspiciousIP;
    /**
     * Mark IP as suspicious
     */
    private markSuspicious;
    /**
     * Validate request payload size
     */
    private validateRequestSize;
    /**
     * Enhanced rate limiting with multiple rules
     */
    private checkRateLimit;
    /**
     * Check if URL matches pattern (simple wildcard support)
     */
    private matchesPattern;
    /**
     * Enhanced input validation based on endpoint
     */
    private validateInput;
    /**
     * Validate API endpoint inputs
     */
    private validateAPIInput;
    /**
     * Validate Twilio webhook inputs
     */
    private validateTwilioWebhook;
    /**
     * Validate WebSocket connection requests
     */
    private validateWebSocketRequest;
    /**
     * Generic input validation
     */
    private validateGenericInput;
    /**
     * Block request and log security event
     */
    private blockRequest;
    /**
     * Log request for monitoring
     */
    private logRequest;
    /**
     * Get security metrics
     */
    getMetrics(): SecurityMetrics;
    /**
     * Reset security metrics
     */
    resetMetrics(): void;
    /**
     * Clean up old suspicious IP entries
     */
    cleanupSuspiciousIPs(): void;
}
export declare const enhancedSecurity: EnhancedSecurity;
//# sourceMappingURL=enhanced-security.d.ts.map