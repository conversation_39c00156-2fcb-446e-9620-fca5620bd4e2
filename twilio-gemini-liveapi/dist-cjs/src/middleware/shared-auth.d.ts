/**
 * Shared Auth Integration for Twilio Gemini API
 * Wrapper around the shared-auth utilities with TypeScript support
 */
import { FastifyRequest, FastifyReply } from 'fastify';
interface User {
    id: string;
    email: string;
    role: string;
    isAuthenticated: boolean;
}
import type { AuthRequest } from '../types/shared-types';
/**
 * Extract user information from request headers (set by nginx auth_request)
 */
export declare function getUserFromHeaders(request: FastifyRequest): User | null;
/**
 * Check if user is authenticated
 */
export declare function isAuthenticated(request: FastifyRequest): boolean;
/**
 * Get user info with fallback for anonymous access
 */
export declare function getUserInfo(request: FastifyRequest): User;
/**
 * Log authentication status for debugging
 */
export declare function logAuthStatus(request: FastifyRequest, serviceName?: string): void;
/**
 * Create user context for frontend applications
 */
export declare function createUserContext(request: FastifyRequest): {
    isAuthenticated: boolean;
    user: {
        id: string;
        email: string;
        role: string;
    } | null;
};
/**
 * Middleware for Fastify with shared auth integration
 */
export declare function validateSharedAuth(request: AuthRequest, reply: FastifyReply): Promise<void>;
/**
 * Create a secure response with user context
 */
export declare function createSecureResponse(data: any, request: FastifyRequest): any;
/**
 * Get authenticated user from request (helper function)
 */
export declare function getAuthenticatedUser(request: AuthRequest): User | null;
export {};
//# sourceMappingURL=shared-auth.d.ts.map