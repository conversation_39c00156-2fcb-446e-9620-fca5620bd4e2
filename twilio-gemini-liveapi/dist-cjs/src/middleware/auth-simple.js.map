{"version": 3, "file": "auth-simple.js", "sourceRoot": "", "sources": ["../../../src/middleware/auth-simple.ts"], "names": [], "mappings": ";;AAOA,oDAqJC;AAGD,kCAaC;AA5KD,4DAA4D;AAC5D,4CAA6C;AAC7C,mCAAyC;AAKlC,KAAK,UAAU,oBAAoB,CAAC,OAAoB,EAAE,KAAmB;IAClF,sCAAsC;IACtC,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,KAAK,WAAW,EAAE,CAAC;QAC/D,OAAO;IACT,CAAC;IAED,gDAAgD;IAChD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACjC,OAAO;IACT,CAAC;IAED,0FAA0F;IAC1F,MAAM,WAAW,GAAG;QAClB,gBAAgB,EAAK,gDAAgD;QACrE,cAAc,EAAO,gDAAgD;QACrE,mBAAmB,EAAE,gDAAgD;QACrE,SAAS,EAAY,wBAAwB;QAC7C,mBAAmB,EAAE,+CAA+C;QACpE,mBAAmB,EAAE,+CAA+C;QACpE,iBAAiB,EAAI,0CAA0C;QAC/D,mBAAmB,EAAE,+CAA+C;QACpE,uBAAuB,EAAE,gDAAgD;QACzE,uBAAuB,EAAE,4CAA4C;QACrE,uBAAuB,EAAE,4CAA4C;QACrE,qBAAqB,EAAI,4CAA4C;QACrE,sBAAsB,EAAE,4CAA4C;QACpE,QAAQ,EAAa,6BAA6B;QAClD,OAAO,CAAc,4BAA4B;KAClD,CAAC;IAEF,6CAA6C;IAC7C,MAAM,cAAc,GAAG;QACrB,eAAe,CAAM,wCAAwC;KAC9D,CAAC;IAEF,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAC3D,OAAO,CAAC,0EAA0E;IACpF,CAAC;IAED,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAC9D,OAAO,CAAC,oCAAoC;IAC9C,CAAC;IAED,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC;IAElD,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,mEAAmE;QACnE,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC/D,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,MAAM,CAAC,CAAC;QAEtC,IAAI,UAAU,EAAE,CAAC;YACf,mBAAU,CAAC,KAAK,CAAC,gDAAgD,EAAE;gBACjE,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,EAAE,EAAE,OAAO,CAAC,EAAE;aACf,CAAC,CAAC;YACH,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,CAAC,CAAC;YAChE,OAAO;QACT,CAAC;QACD,0DAA0D;QAC1D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YACpC,mBAAU,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,mBAAU,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QAC7E,CAAC;QACD,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,6CAA6C;QAC7C,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAEhD,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,oDAAoD;QACpD,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC/D,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,MAAM,CAAC,CAAC;QAEtC,IAAI,UAAU,EAAE,CAAC;YACf,sCAAsC;YACtC,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACrC,CAAC;YAED,0FAA0F;YAC1F,IAAI,WAA+B,CAAC;YACpC,IAAI,WAAmB,CAAC;YAExB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5E,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;gBACzC,WAAW,GAAG,QAAQ,CAAC;YACzB,CAAC;iBAAM,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnF,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;gBAC5C,WAAW,GAAG,QAAQ,CAAC;YACzB,CAAC;iBAAM,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvF,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;gBAC/C,WAAW,GAAG,UAAU,CAAC;YAC3B,CAAC;iBAAM,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC9F,0DAA0D;gBAC1D,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;gBAC/D,WAAW,GAAG,OAAO,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,8EAA8E;gBAC9E,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;gBAClC,WAAW,GAAG,SAAS,CAAC;YAC1B,CAAC;YAED,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,mBAAU,CAAC,KAAK,CAAC,qCAAqC,WAAW,sBAAsB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBACtG,MAAM,IAAI,KAAK,CAAC,mCAAmC,WAAW,UAAU,CAAC,CAAC;YAC5E,CAAC;YAED,uEAAuE;YACvE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAC7D,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAClD,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAE3D,IAAI,CAAC,IAAA,wBAAe,EAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC;gBAC5E,mBAAU,CAAC,IAAI,CAAC,kCAAkC,WAAW,iBAAiB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC5F,MAAM,IAAI,KAAK,CAAC,uBAAuB,WAAW,UAAU,CAAC,CAAC;YAChE,CAAC;YAED,uDAAuD;YACvD,IAAI,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE,CAAC;gBACxC,mBAAU,CAAC,IAAI,CAAC,+BAA+B,WAAW,iBAAiB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzF,MAAM,IAAI,KAAK,CAAC,8BAA8B,WAAW,UAAU,CAAC,CAAC;YACvE,CAAC;YAED,mBAAU,CAAC,IAAI,CAAC,mBAAmB,WAAW,wBAAwB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,mBAAU,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACxC,WAAW,EAAE,KAAK,CAAC,MAAM;YACzB,GAAG,EAAE,OAAO,CAAC,GAAG;SACjB,CAAC,CAAC;QAEH,iDAAiD;QACjD,OAAO,CAAC,IAAI,GAAG;YACb,KAAK;YACL,aAAa,EAAE,IAAI;SACpB,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,mBAAU,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtG,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;QACtD,OAAO;IACT,CAAC;AACH,CAAC;AAED,iDAAiD;AACjD,SAAgB,WAAW,CACzB,OAAoB,EACpB,KAAmB,EACnB,IAA2B;IAE3B,oBAAoB,CAAC,OAAO,EAAE,KAAK,CAAC;SACjC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;SAClB,KAAK,CAAC,GAAG,EAAE;QACV,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAChB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,EAAE,CAAC;IACT,CAAC,CAAC,CAAC;AACP,CAAC"}