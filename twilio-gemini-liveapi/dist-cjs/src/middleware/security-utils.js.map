{"version": 3, "file": "security-utils.js", "sourceRoot": "", "sources": ["../../../src/middleware/security-utils.ts"], "names": [], "mappings": ";AAAA,8CAA8C;;;AAI9C,MAAa,aAAa;IAChB,MAAM,CAAC,cAAc,CAAkC;IAE/D;;;;OAIG;IACH,MAAM,CAAC,mBAAmB,CAAC,WAAoB;QAC7C,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAEnE,4DAA4D;QAC5D,MAAM,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAErD,8DAA8D;QAC9D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAEpD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,YAAY,CAAC,IAAa,EAAE,YAAoB,IAAI;QACzD,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAErD,0CAA0C;QAC1C,MAAM,SAAS,GAAG,IAAI;aACnB,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,mBAAmB;aACxC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,8BAA8B;aAC3D,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,wBAAwB;aAC/C,IAAI,EAAE,CAAC;QAEV,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAEhD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,YAAY,CAAC,UAAmB,EAAE,UAAkB,KAAK;QAC9D,IAAI,CAAC,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAEjE,IAAI,UAAU,CAAC,MAAM,GAAG,OAAO,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAE/C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAEtC,2CAA2C;YAC3C,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBAAA,OAAO,IAAI,CAAC;YAAA,CAAC;YAEjE,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,cAAc,CAAC,GAAW,EAAE,cAAsB,EAAE,EAAE,WAAmB,KAAK;QACnF,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAClC,aAAa,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3C,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,GAAG,GAAG,QAAQ,CAAC;QAEnC,6CAA6C;QAC7C,IAAI,QAAQ,GAAG,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAE3D,yCAAyC;QACzC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC;QAE/D,0BAA0B;QAC1B,IAAI,QAAQ,CAAC,MAAM,IAAI,WAAW,EAAE,CAAC;YACnC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,sBAAsB;QACtB,QAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;QAClC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAEhD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB;QACrB,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAAA,OAAO;QAAA,CAAC;QAE5C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,YAAY;QAEnC,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,aAAa,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YACrE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC;YAC3E,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAiB;QACvC,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAAA,OAAO,KAAK,CAAC;QAAA,CAAC;QAE9D,2CAA2C;QAC3C,OAAO,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,IAAI,EAAE,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,aAAa,CAAC,KAAc;QACjC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAAA,OAAO,KAAK,CAAC;QAAA,CAAC;QAExD,MAAM,WAAW,GAAG;YAClB,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ;YAC3C,MAAM,EAAE,MAAM,EAAE,QAAQ;SACzB,CAAC;QAEF,OAAO,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,aAAa,CAAC,KAAc;QACjC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAAA,OAAO,KAAK,CAAC;QAAA,CAAC;QAExD,MAAM,WAAW,GAAG;YAClB,8CAA8C;YAC9C,2BAA2B;SAC5B,CAAC;QAEF,OAAO,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,eAAe,CAAC,OAAgB;QACrC,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAE3D,4DAA4D;QAC5D,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAErD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,qBAAqB,CAAC,QAAiB;QAC5C,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAElF,MAAM,iBAAiB,GAAwB,EAAE,CAAC;QAClD,MAAM,WAAW,GAAG,QAA+B,CAAC;QAEpD,mBAAmB;QACnB,MAAM,aAAa,GAAG,CAAC,iBAAiB,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,WAAW,CAAC,CAAC;QACpG,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;YAClC,IAAI,KAAK,IAAI,WAAW,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC;gBACpE,iBAAiB,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,aAAa,GAAiD;YAClE,gBAAgB,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;YACrC,cAAc,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;YAClC,cAAc,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE;SACrC,CAAC;QAEF,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YAC3D,IAAI,KAAK,IAAI,WAAW,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACnE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3E,iBAAiB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;YACnC,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,iBAAiB;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,OAAO,WAAW,SAAS,IAAI,UAAU,EAAE,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,cAAc,CAAC,MAA0B,EAAE,iBAA2B,EAAE;QAC7E,IAAI,CAAC,MAAM,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC,CAAC,wDAAwD;QAEpF,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;YAC7C,OAAO,cAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,UAAU,CAAC,EAAsB,EAAE,gBAA0B,EAAE;QACpE,IAAI,CAAC,EAAE,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAErD,0CAA0C;QAC1C,uDAAuD;QACvD,OAAO,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,UAAU,CAAC,IAAa;QAC7B,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAAA,OAAO,EAAE,CAAC;QAAA,CAAC;QAEnD,OAAO,IAAI;aACR,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;aACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;aACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,kBAAkB,CAAC,GAAW,EAAE,WAAmB,KAAK;QAI7D,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAClC,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;QAC1C,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,GAAG,GAAG,QAAQ,CAAC;QACnC,MAAM,QAAQ,GAAG,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAC7D,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC;QAE1E,OAAO;YACL,QAAQ,EAAE,aAAa,CAAC,MAAM;YAC9B,SAAS,EAAE,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI;SACnF,CAAC;IACJ,CAAC;CACF;AAnSD,sCAmSC;AAED,4CAA4C;AAC5C,WAAW,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,gBAAgB,EAAE,EAAE,MAAM,CAAC,CAAC"}