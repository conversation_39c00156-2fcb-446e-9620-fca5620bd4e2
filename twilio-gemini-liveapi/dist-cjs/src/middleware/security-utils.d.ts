export declare class SecurityUtils {
    private static rateLimitStore;
    /**
     * Validate and sanitize phone number input
     * @param phoneNumber - Phone number to validate
     * @returns Sanitized phone number or null if invalid
     */
    static validatePhoneNumber(phoneNumber: unknown): string | null;
    /**
     * Validate and sanitize text input
     * @param text - Text to validate
     * @param maxLength - Maximum allowed length
     * @returns Sanitized text or null if invalid
     */
    static validateText(text: unknown, maxLength?: number): string | null;
    /**
     * Validate JSON input
     * @param jsonString - JSON string to validate
     * @param maxSize - Maximum size in bytes
     * @returns Parsed JSON or null if invalid
     */
    static validateJSON(jsonString: unknown, maxSize?: number): Record<string, any> | null;
    /**
     * Rate limiting check for specific operations
     * @param key - Unique key for rate limiting
     * @param maxRequests - Maximum requests allowed
     * @param windowMs - Time window in milliseconds
     * @returns True if request is allowed
     */
    static checkRateLimit(key: string, maxRequests?: number, windowMs?: number): boolean;
    /**
     * Clean up old rate limit entries
     */
    static cleanupRateLimit(): void;
    /**
     * Validate script ID
     * @param scriptId - Script ID to validate
     * @returns True if valid
     */
    static validateScriptId(scriptId: unknown): boolean;
    /**
     * Validate voice name
     * @param voice - Voice name to validate
     * @returns True if valid
     */
    static validateVoice(voice: unknown): boolean;
    /**
     * Validate model name
     * @param model - Model name to validate
     * @returns True if valid
     */
    static validateModel(model: unknown): boolean;
    /**
     * Sanitize call SID
     * @param callSid - Call SID to sanitize
     * @returns Sanitized call SID or null if invalid
     */
    static sanitizeCallSid(callSid: unknown): string | null;
    /**
     * Validate audio settings
     * @param settings - Audio settings to validate
     * @returns Validated settings or null if invalid
     */
    static validateAudioSettings(settings: unknown): Record<string, any> | null;
    /**
     * Generate secure session ID
     * @returns Secure session ID
     */
    static generateSessionId(): string;
    /**
     * Validate request origin for CORS
     * @param origin - Request origin
     * @param allowedOrigins - Array of allowed origins
     * @returns True if origin is allowed
     */
    static validateOrigin(origin: string | undefined, allowedOrigins?: string[]): boolean;
    /**
     * Check if IP is in allowed range (basic implementation)
     * @param ip - IP address to check
     * @param allowedRanges - Array of allowed IP ranges/addresses
     * @returns True if IP is allowed
     */
    static validateIP(ip: string | undefined, allowedRanges?: string[]): boolean;
    /**
     * Escape HTML to prevent XSS
     * @param text - Text to escape
     * @returns Escaped text
     */
    static escapeHtml(text: unknown): string;
    /**
     * Get rate limit status for a key
     * @param key - Rate limit key
     * @param windowMs - Time window in milliseconds
     * @returns Rate limit status
     */
    static getRateLimitStatus(key: string, windowMs?: number): {
        requests: number;
        resetTime: number | null;
    };
}
//# sourceMappingURL=security-utils.d.ts.map