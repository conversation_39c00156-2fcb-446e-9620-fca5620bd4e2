"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.enhancedSecurity = exports.EnhancedSecurity = void 0;
const security_utils_1 = require("./security-utils");
const logger_1 = require("../utils/logger");
/**
 * Enhanced security middleware with comprehensive protection
 */
class EnhancedSecurity {
    config;
    metrics;
    suspiciousIPs = new Map();
    rateLimitRules = new Map();
    constructor(config = {}) {
        this.config = {
            enableRateLimit: true,
            enableInputValidation: true,
            enableRequestLogging: true,
            maxRequestsPerMinute: 60,
            maxRequestsPerHour: 1000,
            maxPayloadSize: 10 * 1024 * 1024, // 10MB
            allowedOrigins: process.env.CORS_ORIGIN ? [process.env.CORS_ORIGIN] : ['http://localhost:3000', 'https://localhost:3000'],
            trustedProxies: ['127.0.0.1', '::1'],
            ...config
        };
        this.metrics = {
            totalRequests: 0,
            blockedRequests: 0,
            rateLimitViolations: 0,
            validationFailures: 0,
            suspiciousActivity: 0,
            lastReset: Date.now()
        };
        this.initializeRateLimitRules();
    }
    /**
     * Initialize rate limit rules for different endpoints
     */
    initializeRateLimitRules() {
        // API endpoints - more restrictive
        this.rateLimitRules.set('/api/*', {
            endpoint: '/api/*',
            maxRequests: 30,
            windowMs: 60000 // 1 minute
        });
        // WebSocket connections - moderate
        this.rateLimitRules.set('/media-stream*', {
            endpoint: '/media-stream*',
            maxRequests: 5,
            windowMs: 60000 // 1 minute
        });
        // Twilio webhooks - more lenient
        this.rateLimitRules.set('/incoming-call', {
            endpoint: '/incoming-call',
            maxRequests: 100,
            windowMs: 60000 // 1 minute
        });
        // Authentication endpoints - very restrictive
        this.rateLimitRules.set('/auth/*', {
            endpoint: '/auth/*',
            maxRequests: 5,
            windowMs: 300000 // 5 minutes
        });
    }
    /**
     * Main security middleware
     */
    middleware() {
        return async (request, reply) => {
            const startTime = Date.now();
            this.metrics.totalRequests++;
            try {
                // Get client IP (considering proxies)
                const clientIP = this.getClientIP(request);
                // Check if IP is suspicious
                if (this.isSuspiciousIP(clientIP)) {
                    this.blockRequest(reply, 'Suspicious activity detected', clientIP);
                    return;
                }
                // Validate request size
                if (!this.validateRequestSize(request)) {
                    this.blockRequest(reply, 'Request payload too large', clientIP);
                    return;
                }
                // Apply rate limiting
                if (this.config.enableRateLimit && !this.checkRateLimit(request, clientIP)) {
                    this.blockRequest(reply, 'Rate limit exceeded', clientIP);
                    return;
                }
                // Validate input based on endpoint
                if (this.config.enableInputValidation && !this.validateInput(request)) {
                    this.blockRequest(reply, 'Input validation failed', clientIP);
                    return;
                }
                // Log request if enabled
                if (this.config.enableRequestLogging) {
                    this.logRequest(request, clientIP, Date.now() - startTime);
                }
            }
            catch (error) {
                logger_1.websocketLogger.error('Security middleware error', error instanceof Error ? error : new Error(String(error)));
                this.blockRequest(reply, 'Security check failed', this.getClientIP(request));
            }
        };
    }
    /**
     * Get client IP address considering proxies
     */
    getClientIP(request) {
        // Check X-Forwarded-For header (from trusted proxies only)
        const forwardedFor = request.headers['x-forwarded-for'];
        if (forwardedFor) {
            const ips = forwardedFor.split(',').map(ip => ip.trim());
            // Return the first IP that's not from a trusted proxy
            for (const ip of ips) {
                if (!this.config.trustedProxies.includes(ip)) {
                    return ip;
                }
            }
        }
        // Check X-Real-IP header
        const realIP = request.headers['x-real-ip'];
        if (realIP && !this.config.trustedProxies.includes(realIP)) {
            return realIP;
        }
        // Fall back to connection remote address
        return request.socket.remoteAddress || 'unknown';
    }
    /**
     * Check if IP has suspicious activity
     */
    isSuspiciousIP(ip) {
        const suspiciousCount = this.suspiciousIPs.get(ip) || 0;
        return suspiciousCount > 10; // Block after 10 suspicious activities
    }
    /**
     * Mark IP as suspicious
     */
    markSuspicious(ip) {
        const current = this.suspiciousIPs.get(ip) || 0;
        this.suspiciousIPs.set(ip, current + 1);
        this.metrics.suspiciousActivity++;
    }
    /**
     * Validate request payload size
     */
    validateRequestSize(request) {
        const contentLength = parseInt(request.headers['content-length'] || '0', 10);
        return contentLength <= this.config.maxPayloadSize;
    }
    /**
     * Enhanced rate limiting with multiple rules
     */
    checkRateLimit(request, clientIP) {
        const url = request.url;
        // Find matching rate limit rule
        let rule;
        for (const [pattern, ruleConfig] of this.rateLimitRules.entries()) {
            if (this.matchesPattern(url, pattern)) {
                rule = ruleConfig;
                break;
            }
        }
        // Use default rule if no specific rule found
        if (!rule) {
            rule = {
                endpoint: 'default',
                maxRequests: this.config.maxRequestsPerMinute,
                windowMs: 60000
            };
        }
        const rateLimitKey = `${clientIP}:${rule.endpoint}`;
        const allowed = security_utils_1.SecurityUtils.checkRateLimit(rateLimitKey, rule.maxRequests, rule.windowMs);
        if (!allowed) {
            this.metrics.rateLimitViolations++;
            this.markSuspicious(clientIP);
        }
        return allowed;
    }
    /**
     * Check if URL matches pattern (simple wildcard support)
     */
    matchesPattern(url, pattern) {
        if (pattern.endsWith('*')) {
            return url.startsWith(pattern.slice(0, -1));
        }
        return url === pattern;
    }
    /**
     * Enhanced input validation based on endpoint
     */
    validateInput(request) {
        const url = request.url;
        const method = request.method;
        const body = request.body;
        try {
            // Validate based on endpoint patterns
            if (url.includes('/api/')) {
                return this.validateAPIInput(body, url);
            }
            if (url.includes('/incoming-call')) {
                return this.validateTwilioWebhook(body);
            }
            if (url.includes('/media-stream')) {
                return this.validateWebSocketRequest(request);
            }
            // Default validation for other endpoints
            return this.validateGenericInput(body);
        }
        catch (error) {
            this.metrics.validationFailures++;
            return false;
        }
    }
    /**
     * Validate API endpoint inputs
     */
    validateAPIInput(body, url) {
        if (!body) {
            return true;
        } // Allow empty body for GET requests
        // Validate common API fields
        if (body.sessionId && !security_utils_1.SecurityUtils.sanitizeCallSid(body.sessionId) && !body.sessionId.startsWith('local-')) {
            return false;
        }
        if (body.scriptId && !security_utils_1.SecurityUtils.validateScriptId(body.scriptId)) {
            return false;
        }
        if (body.voice && !security_utils_1.SecurityUtils.validateVoice(body.voice)) {
            return false;
        }
        if (body.model && !security_utils_1.SecurityUtils.validateModel(body.model)) {
            return false;
        }
        if (body.phoneNumber && !security_utils_1.SecurityUtils.validatePhoneNumber(body.phoneNumber)) {
            return false;
        }
        return true;
    }
    /**
     * Validate Twilio webhook inputs
     */
    validateTwilioWebhook(body) {
        if (!body) {
            return false;
        }
        // Required Twilio fields
        if (!body.CallSid || !security_utils_1.SecurityUtils.sanitizeCallSid(body.CallSid)) {
            return false;
        }
        if (body.From && !security_utils_1.SecurityUtils.validatePhoneNumber(body.From)) {
            return false;
        }
        if (body.To && !security_utils_1.SecurityUtils.validatePhoneNumber(body.To)) {
            return false;
        }
        return true;
    }
    /**
     * Validate WebSocket connection requests
     */
    validateWebSocketRequest(request) {
        // Check for required WebSocket headers
        const upgrade = request.headers.upgrade;
        const connection = request.headers.connection;
        if (upgrade !== 'websocket' || !connection?.toLowerCase().includes('upgrade')) {
            return false;
        }
        // Validate origin if present
        const origin = request.headers.origin;
        if (origin && !security_utils_1.SecurityUtils.validateOrigin(origin, this.config.allowedOrigins)) {
            return false;
        }
        return true;
    }
    /**
     * Generic input validation
     */
    validateGenericInput(body) {
        if (!body) {
            return true;
        }
        // Check for common injection patterns
        const bodyStr = JSON.stringify(body);
        const suspiciousPatterns = [
            /<script/i,
            /javascript:/i,
            /data:text\/html/i,
            /vbscript:/i,
            /onload=/i,
            /onerror=/i,
            /eval\(/i,
            /document\./i,
            /window\./i
        ];
        return !suspiciousPatterns.some(pattern => pattern.test(bodyStr));
    }
    /**
     * Block request and log security event
     */
    blockRequest(reply, reason, clientIP) {
        this.metrics.blockedRequests++;
        logger_1.websocketLogger.warn(`Security: Blocked request from ${clientIP}: ${reason}`);
        reply.code(429).send({
            error: 'Request blocked for security reasons',
            code: 'SECURITY_VIOLATION',
            timestamp: new Date().toISOString()
        });
    }
    /**
     * Log request for monitoring
     */
    logRequest(request, clientIP, duration) {
        logger_1.websocketLogger.info('Request processed', {
            method: request.method,
            url: request.url,
            ip: clientIP,
            userAgent: request.headers['user-agent'],
            duration,
            timestamp: new Date().toISOString()
        });
    }
    /**
     * Get security metrics
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Reset security metrics
     */
    resetMetrics() {
        this.metrics = {
            totalRequests: 0,
            blockedRequests: 0,
            rateLimitViolations: 0,
            validationFailures: 0,
            suspiciousActivity: 0,
            lastReset: Date.now()
        };
    }
    /**
     * Clean up old suspicious IP entries
     */
    cleanupSuspiciousIPs() {
        // Reset suspicious IP counts every hour
        this.suspiciousIPs.clear();
    }
}
exports.EnhancedSecurity = EnhancedSecurity;
// Create singleton instance
exports.enhancedSecurity = new EnhancedSecurity();
// Clean up suspicious IPs every hour
setInterval(() => exports.enhancedSecurity.cleanupSuspiciousIPs(), 3600000);
//# sourceMappingURL=enhanced-security.js.map