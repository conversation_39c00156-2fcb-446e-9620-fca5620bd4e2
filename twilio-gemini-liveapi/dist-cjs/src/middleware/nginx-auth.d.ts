import { FastifyRequest, FastifyReply } from 'fastify';
interface NginxAuthRequest extends FastifyRequest {
    user?: {
        id: string;
        email: string;
        role: string;
        authenticated: boolean;
    };
}
/**
 * Middleware for nginx auth_request integration
 * Trusts user headers set by nginx after auth-service validation
 */
export declare function validateNginxAuth(request: NginxAuthRequest, reply: FastifyReply): Promise<void>;
/**
 * Middleware to require nginx auth for specific routes
 */
export declare function requireNginxAuth(request: NginxAuthRequest, reply: FastifyReply, done: (err?: Error) => void): void;
/**
 * Get authenticated user from request (helper function)
 */
export declare function getAuthenticatedUser(request: NginxAuthRequest): {
    id: string;
    email: string;
    role: string;
    authenticated: boolean;
} | null;
export {};
//# sourceMappingURL=nginx-auth.d.ts.map