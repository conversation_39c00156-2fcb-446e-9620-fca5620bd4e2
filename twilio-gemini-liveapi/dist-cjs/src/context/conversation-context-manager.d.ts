/**
 * Conversation Context Manager
 *
 * Implements patterns from the Conversation Context Cookbook:
 * - Recipe #3: Hybrid Window + Summary
 * - Recipe #6: Role Separation & Pinned Instructions
 * - Recipe #8: Token Budgeting & Cost Control
 */
interface ConversationTurn {
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
    tokens: number;
}
interface ConversationContext {
    systemInstructions: string;
    campaignScript: string;
    conversationSummary: string;
    recentTurns: ConversationTurn[];
    totalTokens: number;
    lastSummaryUpdate: number;
}
interface ConversationContextOptions {
    maxRecentTurns?: number;
    maxRequestTokens?: number;
    summaryThreshold?: number;
}
interface ContextStats {
    totalTokens: number;
    recentTurns: number;
    hasSummary: boolean;
    lastSummaryUpdate: number;
}
interface FormattedMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
}
export declare class ConversationContextManager {
    private maxRecentTurns;
    private maxRequestTokens;
    private summaryThreshold;
    private sessions;
    constructor(options?: ConversationContextOptions);
    /**
     * Initialize context for a new session
     * Recipe #6: Role Separation & Pinned Instructions
     */
    initializeSession(sessionId: string, systemInstructions: string, campaignScript: string): ConversationContext;
    /**
     * Add a new conversation turn
     * Recipe #3: Hybrid Window + Summary
     */
    addTurn(sessionId: string, role: 'user' | 'assistant' | 'system', content: string): ConversationContext | null;
    /**
     * Get formatted messages for AI model
     * Recipe #3: Hybrid Window + Summary + Recipe #6: Role Separation
     */
    getFormattedMessages(sessionId: string): FormattedMessage[];
    /**
     * Recipe #2: Summarization Buffer
     */
    summarizeOldTurns(sessionId: string): Promise<void>;
    /**
     * Simple summarization (placeholder for AI-based summarization)
     */
    private createSimpleSummary;
    /**
     * Recipe #8: Token Budgeting
     */
    private estimateTokens;
    /**
     * Get context statistics
     */
    getStats(sessionId: string): ContextStats | null;
    /**
     * Clean up session context
     */
    clearSession(sessionId: string): boolean;
}
export default ConversationContextManager;
//# sourceMappingURL=conversation-context-manager.d.ts.map