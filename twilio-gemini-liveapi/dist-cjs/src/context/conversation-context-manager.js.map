{"version": 3, "file": "conversation-context-manager.js", "sourceRoot": "", "sources": ["../../../src/context/conversation-context-manager.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;;AAoCH,MAAa,0BAA0B;IAC7B,cAAc,CAAS;IACvB,gBAAgB,CAAS;IACzB,gBAAgB,CAAS;IACzB,QAAQ,CAAmC;IAEnD,YAAY,UAAsC,EAAE;QAClD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,EAAE,CAAC;QACnD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC;QAC1D,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC;QACzD,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,iBAAiB,CAAC,SAAiB,EAAE,kBAA0B,EAAE,cAAsB;QACrF,MAAM,OAAO,GAAwB;YACnC,kBAAkB;YAClB,cAAc;YACd,mBAAmB,EAAE,EAAE;YACvB,WAAW,EAAE,EAAE;YACf,WAAW,EAAE,CAAC;YACd,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE;SAC9B,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,8BAA8B,cAAc,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC9G,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,SAAiB,EAAE,IAAqC,EAAE,OAAe;QAC/E,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,OAAO,SAAS,gCAAgC,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAqB;YAC7B,IAAI;YACJ,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;SACrC,CAAC;QAEF,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC;QAEnC,wDAAwD;QACxD,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAChD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC;QAED,qDAAqD;QACrD,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACrD,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,MAAM,CAAC;YACxC,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,WAAW,IAAI,UAAU,IAAI,CAAC,MAAM,mBAAmB,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC;QAC3G,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,oBAAoB,CAAC,SAAiB;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YAAA,OAAO,EAAE,CAAC;QAAA,CAAC;QAE1B,MAAM,QAAQ,GAAuB,EAAE,CAAC;QAExC,+BAA+B;QAC/B,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC/B,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,OAAO,CAAC,kBAAkB;aACpC,CAAC,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,qBAAqB,OAAO,CAAC,cAAc,EAAE;aACvD,CAAC,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,yBAAyB,OAAO,CAAC,mBAAmB,EAAE;aAChE,CAAC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACjC,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QACvC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAAA,OAAO;QAAA,CAAC;QAEzD,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,gBAAgB,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YAEnG,MAAM,gBAAgB,GAAG,gBAAgB;iBACtC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;iBAC5C,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,qDAAqD;YACrD,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;YAE9D,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBAChC,OAAO,CAAC,mBAAmB,IAAI,MAAM,UAAU,EAAE,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,mBAAmB,GAAG,UAAU,CAAC;YAC3C,CAAC;YAED,qBAAqB;YACrB,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACnF,OAAO,CAAC,WAAW,IAAI,aAAa,CAAC;YACrC,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvC,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,gBAAgB,gBAAgB,CAAC,MAAM,iBAAiB,aAAa,SAAS,CAAC,CAAC;QAC9G,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,MAAM,SAAS,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,gBAAwB;QAClD,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxD,OAAO,kCAAkC,KAAK,CAAC,MAAM,kCAAkC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,IAAY;QACjC,wCAAwC;QACxC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,SAAiB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAE5B,OAAO;YACL,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC,MAAM;YACvC,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,mBAAmB;YACzC,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;SAC7C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAiB;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,QAAQ,SAAS,mBAAmB,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA/LD,gEA+LC;AAED,kBAAe,0BAA0B,CAAC"}