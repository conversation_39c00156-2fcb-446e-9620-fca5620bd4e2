interface ConnectionStateData {
    callSid: string;
    state: 'connecting' | 'connected' | 'failed' | 'disconnected' | 'recovered' | 'recovering';
    timestamp: number;
    metadata: Record<string, any>;
    consecutiveFailures: number;
    lastPing?: number;
    duration: number;
}
interface DetailedHealthReport {
    summary: {
        totalConnections: number;
        activeConnections: number;
        failedConnections: number;
        recoveredConnections: number;
        lastHealthCheck: number;
        connectionStates: ConnectionStateData[];
        timestamp: number;
    };
    healthScore: number;
    connections: Array<{
        callSid: string;
        state: string;
        duration: number;
        consecutiveFailures: number;
        lastPing: number | null;
        isHealthy: boolean;
    }>;
    systemInfo: {
        uptime: number;
        memory: NodeJS.MemoryUsage;
        timestamp: number;
    };
}
export declare class ConnectionHealthMonitor {
    private connectionStates;
    private healthMetrics;
    private healthCheckInterval;
    private maxConnectionAge;
    private healthMonitoringInterval?;
    constructor();
    trackConnection(callSid: string, state: ConnectionStateData['state'], metadata?: Record<string, any>): void;
    isConnectionHealthy(callSid: string): boolean;
    pingConnection(callSid: string, geminiSession: any): boolean;
    private startHealthMonitoring;
    stopHealthChecks(): void;
    private performHealthCheck;
    getHealthStatus(): {
        totalConnections: number;
        activeConnections: number;
        failedConnections: number;
        recoveredConnections: number;
        lastHealthCheck: number;
        connectionStates: ConnectionStateData[];
        timestamp: number;
    };
    emit(event: string, data: {
        callSid: string;
        reason: string;
    }): void;
    private handleRecoveryEvent;
    removeConnection(callSid: string): void;
    getConnectionMetrics(callSid: string): ConnectionStateData | null;
    getHealthScore(): number;
    reset(): void;
    getDetailedHealthReport(): DetailedHealthReport;
}
export {};
//# sourceMappingURL=health-monitor.d.ts.map