"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.recoverSession = recoverSession;
const logger_1 = require("../utils/logger");
async function recoverSession(callSid, reason, contextManager, sessionMetrics, recoverySet) {
    if (recoverySet.has(callSid)) {
        logger_1.sessionLogger.info(`⏳ [${callSid}] Recovery already in progress`);
        return;
    }
    recoverySet.add(callSid);
    try {
        const context = contextManager.getSessionContext(callSid);
        if (!context || !contextManager.canRecover(callSid)) {
            logger_1.sessionLogger.info(`❌ [${callSid}] Cannot recover session`);
            return;
        }
        const recoveryCount = contextManager.incrementRecoveryAttempt(callSid);
        logger_1.sessionLogger.info(`🔄 [${callSid}] Attempting session recovery #${recoveryCount} (reason: ${reason})`);
        const metrics = sessionMetrics.get(callSid);
        if (metrics) {
            metrics.recoveryCount = (metrics.recoveryCount || 0) + 1;
            metrics.lastRecoveryTime = Date.now();
        }
        logger_1.sessionLogger.info(`✅ [${callSid}] Recovery preparation completed`);
    }
    catch (error) {
        logger_1.sessionLogger.error(`❌ [${callSid}] Error during session recovery:`, error instanceof Error ? error : new Error(String(error)));
    }
    finally {
        recoverySet.delete(callSid);
    }
}
//# sourceMappingURL=recovery.js.map