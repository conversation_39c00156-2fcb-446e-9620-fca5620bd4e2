"use strict";
/**
 * Recovery Lock Manager
 * Provides thread-safe locking mechanism for session recovery operations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.recoveryLockManager = exports.RecoveryLockManager = void 0;
const logger_1 = require("../utils/logger");
class RecoveryLockManager {
    locks = new Map();
    pendingLockRequests = new Map();
    lockTimeout;
    constructor(lockTimeout = 30000) {
        this.lockTimeout = lockTimeout;
    }
    /**
     * Attempts to acquire a lock for the given key
     * Returns true if lock acquired, false if already locked
     */
    async acquireLock(key) {
        return new Promise((resolve) => {
            // Check if lock exists and is still valid
            const existingLock = this.locks.get(key);
            if (existingLock && Date.now() - existingLock.timestamp < this.lockTimeout) {
                // Lock is held, add to pending queue
                const pending = this.pendingLockRequests.get(key) || [];
                pending.push(resolve);
                this.pendingLockRequests.set(key, pending);
                logger_1.recoveryLogger.debug(`🔒 Lock request queued`, {
                    key,
                    queueLength: pending.length,
                    lockAge: Date.now() - existingLock.timestamp
                });
                return;
            }
            // Clean up expired lock if exists
            if (existingLock) {
                this.releaseLock(key);
            }
            // Acquire new lock
            const timeoutId = setTimeout(() => {
                logger_1.recoveryLogger.warn(`⏰ Lock timeout`, { key });
                this.releaseLock(key);
            }, this.lockTimeout);
            this.locks.set(key, {
                timestamp: Date.now(),
                promiseResolve: resolve,
                timeoutId
            });
            logger_1.recoveryLogger.debug(`🔒 Lock acquired`, { key });
            resolve(true);
        });
    }
    /**
     * Releases a lock and processes any pending requests
     */
    releaseLock(key) {
        const lock = this.locks.get(key);
        if (!lock) {
            return;
        }
        // Clear timeout if exists
        if (lock.timeoutId) {
            clearTimeout(lock.timeoutId);
        }
        // Delete the lock
        this.locks.delete(key);
        logger_1.recoveryLogger.debug(`🔓 Lock released`, { key });
        // Process pending requests
        const pending = this.pendingLockRequests.get(key);
        if (pending && pending.length > 0) {
            const nextRequest = pending.shift();
            this.pendingLockRequests.set(key, pending);
            // Give the next request the lock
            const timeoutId = setTimeout(() => {
                logger_1.recoveryLogger.warn(`⏰ Lock timeout (pending)`, { key });
                this.releaseLock(key);
            }, this.lockTimeout);
            this.locks.set(key, {
                timestamp: Date.now(),
                timeoutId
            });
            logger_1.recoveryLogger.debug(`🔒 Lock transferred to pending request`, {
                key,
                remainingPending: pending.length
            });
            nextRequest(true);
        }
        else {
            // No pending requests, clean up
            this.pendingLockRequests.delete(key);
        }
    }
    /**
     * Checks if a lock is currently held
     */
    isLocked(key) {
        const lock = this.locks.get(key);
        return !!(lock && Date.now() - lock.timestamp < this.lockTimeout);
    }
    /**
     * Gets the current lock status
     */
    getLockStatus() {
        const locks = Array.from(this.locks.entries()).map(([key, lock]) => ({
            key,
            age: Date.now() - lock.timestamp,
            hasPending: (this.pendingLockRequests.get(key) || []).length > 0
        }));
        const totalPending = Array.from(this.pendingLockRequests.values())
            .reduce((sum, pending) => sum + pending.length, 0);
        return {
            activeLocks: this.locks.size,
            pendingRequests: totalPending,
            locks
        };
    }
    /**
     * Cleans up all locks and pending requests
     */
    cleanup() {
        // Clear all timeouts
        for (const [key, lock] of this.locks.entries()) {
            if (lock.timeoutId) {
                clearTimeout(lock.timeoutId);
            }
        }
        // Reject all pending requests
        for (const [key, pending] of this.pendingLockRequests.entries()) {
            pending.forEach(resolve => resolve(false));
        }
        this.locks.clear();
        this.pendingLockRequests.clear();
        logger_1.recoveryLogger.info(`🧹 Lock manager cleaned up`);
    }
}
exports.RecoveryLockManager = RecoveryLockManager;
// Export a singleton instance
exports.recoveryLockManager = new RecoveryLockManager();
//# sourceMappingURL=recovery-lock.js.map