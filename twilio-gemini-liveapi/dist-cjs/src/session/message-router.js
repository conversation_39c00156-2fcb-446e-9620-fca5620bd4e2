"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.routeGeminiMessage = routeGeminiMessage;
const audio_forwarding_1 = require("../audio/audio-forwarding");
const logger_1 = require("../utils/logger");
async function routeGeminiMessage(callSid, message, connectionData, deps) {
    const { audioProcessor, activeConnections, sessionMetrics, forwardAudioFn = audio_forwarding_1.forwardAudio } = deps;
    try {
        const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
        if (!message || !message.serverContent) {
            logger_1.sessionLogger.warn(`⚠️ [${callSid}] Received invalid message structure`);
            return;
        }
        const metrics = sessionMetrics?.get(callSid);
        if (metrics) {
            metrics.messagesReceived = (metrics.messagesReceived || 0) + 1;
            metrics.lastActivity = Date.now();
        }
        if (hasAudio && hasAudio.mimeType && hasAudio.mimeType.includes('audio')) {
            if (!hasAudio.data || hasAudio.data.length === 0) {
                logger_1.sessionLogger.warn(`⚠️ [${callSid}] Received empty audio data from Gemini`);
            }
            else {
                logger_1.sessionLogger.info(`🎤 [${callSid}] Received audio from Gemini`, {
                    dataLength: hasAudio.data.length,
                    mimeType: hasAudio.mimeType,
                    sessionType: connectionData.sessionType,
                    isTwilioCall: connectionData.isTwilioCall,
                    audioForwardingEnabled: connectionData.audioForwardingEnabled,
                    lastAudioSent: connectionData.lastAudioSent
                });
                const fresh = activeConnections?.get(callSid) || connectionData;
                // CRITICAL FIX: Check if session is ready for audio processing
                const sessionReady = fresh.sessionReady;
                const wsConnected = !!(fresh.ws || fresh.twilioWs || fresh.localWs);
                const audioForwardingEnabled = fresh.audioForwardingEnabled;
                // Log the fresh connection data state for debugging
                logger_1.sessionLogger.debug(`🔍 [${callSid}] Fresh connection data state`, {
                    sessionId: fresh.sessionId,
                    sessionType: fresh.sessionType,
                    isTwilioCall: fresh.isTwilioCall,
                    streamSid: fresh.streamSid,
                    sequenceNumber: fresh.sequenceNumber,
                    sessionReady: sessionReady,
                    audioForwardingEnabled: audioForwardingEnabled,
                    wsConnected: wsConnected
                });
                // If session isn't ready or WebSocket not connected, log and continue
                if (!sessionReady || !wsConnected) {
                    logger_1.sessionLogger.warn(`⚠️ [${callSid}] Session not fully ready, buffering audio`, {
                        sessionReady: sessionReady,
                        wsConnected: wsConnected,
                        audioForwardingEnabled: audioForwardingEnabled
                    });
                }
                if (!audioForwardingEnabled) {
                    logger_1.sessionLogger.warn(`⚠️ [${callSid}] Audio forwarding not enabled, buffering audio`);
                }
                const forwardingResult = await forwardAudioFn(callSid, hasAudio, fresh, audioProcessor);
                if (forwardingResult) {
                    logger_1.sessionLogger.debug(`✅ [${callSid}] Audio forwarding successful`);
                }
                else {
                    logger_1.sessionLogger.error(`❌ [${callSid}] Audio forwarding failed - AI audio lost!`, {
                        hasAudio: !!hasAudio,
                        hasConnectionData: !!fresh,
                        hasAudioProcessor: !!audioProcessor
                    });
                }
            }
        }
        const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
        if (text) {
            connectionData.lastAIResponse = Date.now();
            connectionData.responseTimeouts = 0;
            connectionData.connectionQuality = 'good';
            if (connectionData.conversationLog) {
                const MAX_LOG_SIZE = 500;
                connectionData.conversationLog.push({ role: 'assistant', content: text, timestamp: Date.now() });
                if (connectionData.conversationLog.length > MAX_LOG_SIZE) {
                    connectionData.conversationLog.splice(0, connectionData.conversationLog.length - MAX_LOG_SIZE);
                }
            }
            if (connectionData.summaryRequested) {
                connectionData.summaryText = (connectionData.summaryText || '') + text;
            }
        }
    }
    catch (error) {
        logger_1.sessionLogger.error(`❌ [${callSid}] Error processing Gemini message:`, error instanceof Error ? error : new Error(String(error)));
    }
}
//# sourceMappingURL=message-router.js.map