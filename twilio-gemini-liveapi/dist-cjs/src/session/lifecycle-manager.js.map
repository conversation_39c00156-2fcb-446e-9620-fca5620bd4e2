{"version": 3, "file": "lifecycle-manager.js", "sourceRoot": "", "sources": ["../../../src/session/lifecycle-manager.ts"], "names": [], "mappings": ";;;AAiCA,MAAa,uBAAuB;IACxB,cAAc,CAAiB;IAC/B,aAAa,CAA0B;IACvC,cAAc,CAAwB;IACtC,aAAa,CAAoC;IACjD,uBAAuB,CAAoD;IAC3E,mBAAmB,CAAS;IAC5B,eAAe,CAA8B;IAErD,YAAY,cAA8B,EAAE,aAAsC,EAAE,cAAqC;QACrH,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,uBAAuB,GAAG,IAAI,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC,CAAC,8CAA8C;QAC7E,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QAEjC,6CAA6C;QAC7C,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC3B,qCAAqC;QACrC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE;YACtD,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,2BAA2B,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE;YAChD,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,kBAAkB,CAAC,CAAC;YAClD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE;YAChD,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,qBAAqB,CAAC,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YAC/C,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,oBAAoB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9E,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;gBACvD,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;aAClC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACH,iBAAiB,CAAC,OAAe,EAAE,eAAqC;QACpE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,YAAY,GAAmB;YACjC,KAAK,EAAE,cAAc;YACrB,SAAS,EAAE,GAAG;YACd,QAAQ,EAAE,eAAe;SAC5B,CAAC;QAEF,MAAM,aAAa,GAAyB;YACxC,OAAO;YACP,YAAY,EAAE,YAAY;YAC1B,YAAY,EAAE,CAAC,YAAY,CAAC;YAC5B,SAAS,EAAE,GAAG;YACd,UAAU,EAAE,CAAC;YACb,eAAe,EAAE,CAAC;SACrB,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAE/C,oCAAoC;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACjE,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,aAAa,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,iCAAiC,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;OAMG;IACH,eAAe,CAAC,OAAe,EAAE,QAAiC,EAAE,MAAe,EAAE,QAA8B;QAC/G,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,OAAO,OAAO,gDAAgD,CAAC,CAAC;YAC7E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,CAAC;QAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,iDAAiD;QACjD,IAAI,QAAQ,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,8DAA8D;YAC9D,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,OAAO,OAAO,+BAA+B,QAAQ,CAAC,KAAK,OAAO,QAAQ,EAAE,CAAC,CAAC;YAC3F,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,mBAAmB;QACnB,MAAM,YAAY,GAAmB;YACjC,KAAK,EAAE,QAAQ;YACf,SAAS,EAAE,GAAG;YACd,MAAM;YACN,QAAQ;SACX,CAAC;QAEF,wBAAwB;QACxB,aAAa,CAAC,YAAY,GAAG,YAAY,CAAC;QAC1C,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9C,aAAa,CAAC,eAAe,EAAE,CAAC;QAEhC,2BAA2B;QAC3B,IAAI,aAAa,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/D,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QACvC,CAAC;QAED,eAAe;QACf,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACvB,aAAa,CAAC,UAAU,EAAE,CAAC;QAC/B,CAAC;QAED,qCAAqC;QACrC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACvB,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC;YAC5B,aAAa,CAAC,QAAQ,GAAG,GAAG,GAAG,aAAa,CAAC,SAAS,CAAC;QAC3D,CAAC;QAED,mCAAmC;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,aAAa,CAAC,CAAC;QAC3B,CAAC;QAED,iBAAiB;QACjB,MAAM,UAAU,GAAwB;YACpC,IAAI,EAAE,QAAQ,CAAC,KAAK;YACpB,EAAE,EAAE,QAAQ;YACZ,SAAS,EAAE,GAAG;YACd,QAAQ,EAAE,GAAG,GAAG,QAAQ,CAAC,SAAS;YAClC,MAAM;SACT,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,qBAAqB,EAAE,UAAU,CAAC,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACK,iBAAiB,CAAC,SAAkC,EAAE,OAAgC;QAC1F,MAAM,gBAAgB,GAA+D;YACjF,cAAc,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;YAC5C,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC;YACrD,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;YACvC,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;YAC3C,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YAC5B,OAAO,EAAE,EAAE,EAAE,iBAAiB;YAC9B,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC;SAC7C,CAAC;QAEF,OAAO,gBAAgB,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,cAA8B,EAAE,MAAe;QAC7E,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,uCAAuC,MAAM,IAAI,QAAQ,GAAG,CAAC,CAAC;QACxF,MAAM,iBAAiB,GAAG,cAAwC,CAAC;QAEnE,6BAA6B;QAC7B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEhD,IAAI,CAAC;YACD,2CAA2C;YAC3C,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,6CAA6C,CAAC,CAAC;gBACzE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAE9G,IAAI,cAAc,EAAE,CAAC;oBACjB,qEAAqE;oBACrE,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,qDAAqD,CAAC,CAAC;oBAChF,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;gBAC3E,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,mDAAmD,CAAC,CAAC;gBACnF,CAAC;YACL,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAE7C,mBAAmB;YACnB,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAEjD,4BAA4B;YAC5B,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YAE5D,6BAA6B;YAC7B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAElC,4BAA4B;YAC5B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAE3D,uCAAuC;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,aAAa,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,8BAA8B,EAAE;oBACtD,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;oBAC9F,gBAAgB,EAAE,aAAa,CAAC,eAAe;oBAC/C,UAAU,EAAE,aAAa,CAAC,UAAU;oBACpC,UAAU,EAAE,aAAa,CAAC,YAAY,CAAC,KAAK;iBAC/C,CAAC,CAAC;YACP,CAAC;YAED,wEAAwE;YACxE,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,0BAA0B,CAAC,CAAC;YAC3D,CAAC,EAAE,IAAI,CAAC,CAAC;QAEb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,MAAM,OAAO,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;QAC5D,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,iBAAiB,CAAC,OAAe,EAAE,eAAuB,EAAE,cAA0B;QAClF,6BAA6B;QAC7B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAElC,kBAAkB;QAClB,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,qCAAqC,eAAe,IAAI,CAAC,CAAC;YACnF,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACrC,cAAc,EAAE,CAAC;QACrB,CAAC,EAAE,eAAe,CAAC,CAAC;QAEpB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,6BAA6B,eAAe,IAAI,CAAC,CAAC;IAChF,CAAC;IAED;;;OAGG;IACH,mBAAmB,CAAC,OAAe;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,SAAS,EAAE,CAAC;YACZ,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,2BAA2B,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,eAAe,CAAC,OAAe;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACtD,OAAO,aAAa,EAAE,YAAY,IAAI,IAAI,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACH,eAAe,CAAC,OAAe;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACtD,OAAO,aAAa,EAAE,YAAY,IAAI,EAAE,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACH,mBAAmB,CAAC,OAAe;QAO/B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,aAAa,EAAE,CAAC;YAAA,OAAO,IAAI,CAAC;QAAA,CAAC;QAElC,OAAO;YACH,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa,CAAC,SAAS,CAAC;YAC1E,gBAAgB,EAAE,aAAa,CAAC,eAAe;YAC/C,UAAU,EAAE,aAAa,CAAC,UAAU;YACpC,YAAY,EAAE,aAAa,CAAC,YAAY,CAAC,KAAK;YAC9C,QAAQ,EAAE,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC;SAC3E,CAAC;IACN,CAAC;IAED;;;;OAIG;IACH,iBAAiB,CAAC,OAAe;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAC5C,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACH,oBAAoB,CAAC,KAA8B,EAAE,OAA6C;QAC9F,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACb,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,KAAK,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YACzD,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxD,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QACD,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,wBAAwB,CAAC,OAAe,EAAE,cAAsC,EAAE,SAAiB;QAC7G,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,aAAa,GAAG,GAAG,CAAC,CAAC,oBAAoB;QAE/C,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC;YACxC,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;gBACjC,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,0BAA0B,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,IAAI,CAAC,CAAC;gBAC/E,OAAO;YACX,CAAC;YAED,wCAAwC;YACxC,IAAI,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtE,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,gCAAgC,cAAc,CAAC,WAAW,CAAC,MAAM,SAAS,CAAC,CAAC;YAC1G,CAAC;YAED,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC;QACrE,CAAC;QAED,kBAAkB;QAClB,IAAI,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,0DAA0D,CAAC,CAAC;YACrF,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC,0CAA0C;QACrF,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,mDAAmD,CAAC,CAAC;QAClF,CAAC;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACH,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAE/C,qBAAqB;QACrB,KAAK,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QAErC,OAAO,CAAC,GAAG,CAAC,gCAAgC,YAAY,iBAAiB,YAAY,WAAW,CAAC,CAAC;IACtG,CAAC;CACJ;AA3ZD,0DA2ZC;AAGmC,mDAAgB"}