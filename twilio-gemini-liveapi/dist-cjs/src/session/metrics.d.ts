export interface SessionMetrics {
    startTime: number;
    messagesReceived: number;
    messagesSent: number;
    recoveryCount: number;
    lastActivity: number;
    isInitializing: boolean;
    lastRecoveryTime?: number;
}
export declare class BoundedMap<K, V> extends Map<K, V> {
    private maxSize;
    constructor(maxSize?: number);
    set(key: K, value: V): this;
}
export declare class BoundedSet<T> extends Set<T> {
    private maxSize;
    constructor(maxSize?: number);
    add(value: T): this;
}
export declare function addToBoundedArray<T>(array: T[], item: T, maxSize: number): T[];
//# sourceMappingURL=metrics.d.ts.map