import { ConnectionData } from '../types/global';
import { ContextManager } from './context-manager';
import { ConnectionHealthMonitor } from './health-monitor';
import { SessionManager } from './session-manager';
interface RecoveryStatus {
    canRecover: boolean;
    isRecovering: boolean;
    recoveryAttempts: number;
    wasInterrupted: boolean;
    lastRecoveryTime?: number;
}
interface RecoveryQueueStatus {
    queueSize: number;
    inProgress: number;
    queuedSessions: string[];
    inProgressSessions: string[];
}
export declare class SessionRecoveryManager {
    private contextManager;
    private healthMonitor;
    private sessionManager;
    private recoveryQueue;
    private recoveryInProgress;
    private maxRecoveryTime;
    private retryTimeouts;
    private cleanupHandlers;
    private recoveryLocks?;
    private recoveryHealthChecks?;
    constructor(contextManager: ContextManager, healthMonitor: ConnectionHealthMonitor, sessionManager: SessionManager);
    recoverSession(callSid: string, reason: string | undefined, activeConnections: Map<string, ConnectionData>): Promise<boolean>;
    cleanupSession(callSid: string): void;
    private recreateGeminiSession;
    private sendRecoveryNotification;
    needsRecovery(callSid: string, activeConnections: Map<string, ConnectionData>): boolean;
    getRecoveryStatus(callSid: string): RecoveryStatus;
    getRecoveryQueueStatus(): RecoveryQueueStatus;
    private recreateGeminiSessionWithRetry;
    private scheduleRecoveryHealthChecks;
    cleanupRecovery(callSid: string): void;
    cleanup(): void;
    /**
     * Calculate exponential backoff delay for retry attempts
     * @param attempt - Current attempt number (1-based)
     * @param isError - Whether this is an error retry (shorter delays)
     * @returns Delay in milliseconds
     */
    private calculateExponentialBackoff;
    /**
     * Get recovery metrics for monitoring
     * @returns Recovery metrics object
     */
    getRecoveryMetrics(): {
        totalRecoveries: number;
        successfulRecoveries: number;
        failedRecoveries: number;
        averageRecoveryTime: number;
        activeRecoveries: number;
        queuedRecoveries: number;
    };
}
export { SessionRecoveryManager as RecoveryManager };
//# sourceMappingURL=recovery-manager.d.ts.map