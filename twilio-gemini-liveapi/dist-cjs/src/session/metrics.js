"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BoundedSet = exports.BoundedMap = void 0;
exports.addToBoundedArray = addToBoundedArray;
const logger_1 = require("../utils/logger");
class BoundedMap extends Map {
    maxSize;
    constructor(maxSize = 1000) {
        super();
        this.maxSize = maxSize;
    }
    set(key, value) {
        if (this.size >= this.maxSize && !this.has(key)) {
            const firstKey = this.keys().next().value;
            if (firstKey !== undefined) {
                const oldValue = this.get(firstKey);
                this.delete(firstKey);
                logger_1.sessionLogger.info(`🧹 SessionManager BoundedMap: Removed oldest entry ${String(firstKey)}`, {
                    mapSize: this.size,
                    maxSize: this.maxSize,
                    removedKey: firstKey,
                    removedValueType: typeof oldValue
                });
            }
        }
        return super.set(key, value);
    }
}
exports.BoundedMap = BoundedMap;
class BoundedSet extends Set {
    maxSize;
    constructor(maxSize = 1000) {
        super();
        this.maxSize = maxSize;
    }
    add(value) {
        if (this.size >= this.maxSize && !this.has(value)) {
            const firstValue = this.values().next().value;
            if (firstValue !== undefined) {
                this.delete(firstValue);
                logger_1.sessionLogger.info(`🧹 SessionManager BoundedSet: Removed oldest entry ${String(firstValue)}`, {
                    setSize: this.size,
                    maxSize: this.maxSize,
                    removedValue: firstValue
                });
            }
        }
        return super.add(value);
    }
}
exports.BoundedSet = BoundedSet;
function addToBoundedArray(array, item, maxSize) {
    if (!Array.isArray(array)) {
        logger_1.sessionLogger.warn('addToBoundedArray: array is not an array, initializing as empty array');
        array = [];
    }
    array.push(item);
    if (array.length > maxSize) {
        const removed = array.splice(0, array.length - maxSize);
        logger_1.sessionLogger.info(`🧹 Trimmed ${removed.length} old entries from bounded array (max: ${maxSize})`);
    }
    return array;
}
//# sourceMappingURL=metrics.js.map