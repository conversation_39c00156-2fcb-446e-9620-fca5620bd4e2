import { AudioProcessor } from '../audio/audio-processor';
import { ConnectionData, GeminiSession } from '../types/global';
import { SessionMetrics } from './metrics';
interface RoutingDeps {
    audioProcessor: AudioProcessor;
    sessionMetrics: Map<string, SessionMetrics>;
    activeConnections?: Map<string, ConnectionData> | null;
    earlyAudioBuffers: Map<string, Buffer[]>;
}
export declare function sendAudioToGemini(callSid: string, geminiSession: GeminiSession | null, audioBuffer: Buffer, deps: RoutingDeps): Promise<void>;
export declare function sendBrowserAudioToGemini(callSid: string, geminiSession: GeminiSession | null, base64Audio: string, deps: RoutingDeps): Promise<void>;
export declare function setupTwilioReadinessCheck(callSid: string, connectionData: any, earlyAudioBuffers: Map<string, Buffer[]>, activeConnections?: Map<string, ConnectionData> | null): void;
export declare function bufferEarlyAudio(callSid: string, audioBuffer: Buffer, earlyAudioBuffers: Map<string, Buffer[]>): void;
export declare function processBufferedAudio(callSid: string, deps: {
    earlyAudioBuffers: Map<string, Buffer[]>;
    activeConnections?: Map<string, ConnectionData> | null;
    audioProcessor?: AudioProcessor;
    sessionMetrics?: Map<string, SessionMetrics>;
}, geminiSession?: GeminiSession | null): Promise<void>;
export {};
//# sourceMappingURL=websocket-routing.d.ts.map