{"version": 3, "file": "health-monitor.js", "sourceRoot": "", "sources": ["../../../src/session/health-monitor.ts"], "names": [], "mappings": ";AAAA,sCAAsC;;;AAkDtC,MAAa,uBAAuB;IACxB,gBAAgB,CAAmC;IACnD,aAAa,CAAgB;IAC7B,mBAAmB,CAAS;IAC5B,gBAAgB,CAAS;IACzB,wBAAwB,CAAkB;IAElD;QACI,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,qCAAqC;QACxE,IAAI,CAAC,aAAa,GAAG;YACjB,gBAAgB,EAAE,CAAC;YACnB,iBAAiB,EAAE,CAAC;YACpB,iBAAiB,EAAE,CAAC;YACpB,oBAAoB,EAAE,CAAC;YACvB,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE;SAC9B,CAAC;QACF,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC,aAAa;QAC/C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,CAAC,SAAS;QAC1C,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IAED,iCAAiC;IACjC,eAAe,CAAC,OAAe,EAAE,KAAmC,EAAE,WAAgC,EAAE;QACpG,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEzD,MAAM,eAAe,GAAwB;YACzC,OAAO;YACP,KAAK,EAAE,mEAAmE;YAC1E,SAAS;YACT,QAAQ;YACR,mBAAmB,EAAE,KAAK,KAAK,QAAQ,CAAC,CAAC;gBACrC,CAAC,aAAa,EAAE,mBAAmB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,QAAQ,EAAE,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,EAAE,QAAQ;YACrE,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACpE,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QAEpD,wBAAwB;QACxB,QAAQ,KAAK,EAAE,CAAC;YACZ,KAAK,WAAW;gBACZ,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;gBACvC,IAAI,aAAa,EAAE,KAAK,KAAK,QAAQ,EAAE,CAAC;oBACpC,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;gBAC9C,CAAC;gBACD,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;gBACvC,MAAM;YACV,KAAK,cAAc;gBACf,IAAI,aAAa,EAAE,KAAK,KAAK,WAAW,EAAE,CAAC;oBACvC,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;gBACjG,CAAC;gBACD,MAAM;QACd,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,uBAAuB,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC;IACxE,CAAC;IAED,mCAAmC;IACnC,mBAAmB,CAAC,OAAe;QAC/B,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE,CAAC;YAAA,OAAO,KAAK,CAAC;QAAA,CAAC;QAErC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,qBAAqB,GAAG,GAAG,GAAG,eAAe,CAAC,SAAS,CAAC;QAC9D,MAAM,QAAQ,GAAG,qBAAqB,GAAG,MAAM,CAAC,CAAC,YAAY;QAC7D,MAAM,WAAW,GAAG,eAAe,CAAC,KAAK,KAAK,WAAW,CAAC;QAC1D,MAAM,cAAc,GAAG,eAAe,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAE/D,OAAO,QAAQ,IAAI,WAAW,IAAI,cAAc,CAAC;IACrD,CAAC;IAED,iCAAiC;IACjC,cAAc,CAAC,OAAe,EAAE,aAAkB;QAC9C,IAAI,CAAC,aAAa,EAAE,CAAC;YAAA,OAAO,KAAK,CAAC;QAAA,CAAC;QAEnC,IAAI,CAAC;YACD,wBAAwB;YACxB,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3D,IAAI,eAAe,EAAE,CAAC;gBAClB,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACtC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YACxD,CAAC;YAED,0EAA0E;YAC1E,kEAAkE;YAClE,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,MAAM,OAAO,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED,mCAAmC;IAC3B,qBAAqB;QACzB,IAAI,CAAC,wBAAwB,GAAG,WAAW,CAAC,GAAG,EAAE;YAC7C,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE7B,OAAO,CAAC,GAAG,CAAC,sDAAsD,IAAI,CAAC,mBAAmB,KAAK,CAAC,CAAC;IACrG,CAAC;IAED,yBAAyB;IACzB,gBAAgB;QACZ,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,aAAa,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC7C,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACnD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,6BAA6B,eAAe,oBAAoB,CAAC,CAAC;IAClF,CAAC;IAED,qCAAqC;IAC7B,kBAAkB;QACtB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAE7B,KAAK,MAAM,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;YACvE,MAAM,qBAAqB,GAAG,GAAG,GAAG,eAAe,CAAC,SAAS,CAAC;YAC9D,MAAM,OAAO,GAAG,qBAAqB,GAAG,MAAM,CAAC,CAAC,YAAY;YAE5D,IAAI,OAAO,EAAE,CAAC;gBACV,6BAA6B;gBAC7B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,kCAAkC,CAAC,CAAC;gBAC9D,SAAS;YACb,CAAC;YAED,IAAI,eAAe,CAAC,KAAK,KAAK,WAAW,IAAI,eAAe,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;gBACrF,kBAAkB,EAAE,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACJ,oBAAoB,EAAE,CAAC;YAC3B,CAAC;QACL,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG,kBAAkB,CAAC;QAC1D,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,GAAG,CAAC;QAEzC,qBAAqB;QACrB,IAAI,kBAAkB,GAAG,CAAC,IAAI,oBAAoB,GAAG,CAAC,EAAE,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,oBAAoB,kBAAkB,aAAa,oBAAoB,wBAAwB,CAAC,CAAC;QACjH,CAAC;IACL,CAAC;IAED,4BAA4B;IAC5B,eAAe;QASX,OAAO;YACH,GAAG,IAAI,CAAC,aAAa;YACrB,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC5D,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACN,CAAC;IAED,2CAA2C;IAC3C,IAAI,CAAC,KAAa,EAAE,IAAyC;QACzD,IAAI,KAAK,KAAK,iBAAiB,EAAE,CAAC;YAC9B,oCAAoC;YACpC,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACnC,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,CAAC;IACL,CAAC;IAED,wBAAwB;IAChB,mBAAmB,CAAC,IAAyC;QACjE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,+BAA+B,MAAM,EAAE,CAAC,CAAC;QAEnE,yDAAyD;QACzD,qCAAqC;QACrC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED,+BAA+B;IAC/B,gBAAgB,CAAC,OAAe;QAC5B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,sCAAsC,CAAC,CAAC;IACvE,CAAC;IAED,6CAA6C;IAC7C,oBAAoB,CAAC,OAAe;QAChC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IACtD,CAAC;IAED,0CAA0C;IAC1C,cAAc;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;QAClD,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;YAAA,OAAO,GAAG,CAAC;QAAA,CAAC;QAE9B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;QAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAEpD,0DAA0D;QAC1D,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;QACrD,MAAM,YAAY,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACnE,MAAM,WAAW,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC;QAE/E,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IACvG,CAAC;IAED,iDAAiD;IACjD,KAAK;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG;YACjB,gBAAgB,EAAE,CAAC;YACnB,iBAAiB,EAAE,CAAC;YACpB,iBAAiB,EAAE,CAAC;YACpB,oBAAoB,EAAE,CAAC;YACvB,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE;SAC9B,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACnD,CAAC;IAED,6BAA6B;IAC7B,uBAAuB;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QAE/D,OAAO;YACH,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAClC,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,GAAG,GAAG,IAAI,CAAC,SAAS;gBAC9B,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;gBAC7C,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;gBACpD,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC;aACpD,CAAC,CAAC;YACH,UAAU,EAAE;gBACR,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;gBACxB,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE;gBAC7B,SAAS,EAAE,GAAG;aACjB;SACJ,CAAC;IACN,CAAC;CACJ;AA7PD,0DA6PC"}