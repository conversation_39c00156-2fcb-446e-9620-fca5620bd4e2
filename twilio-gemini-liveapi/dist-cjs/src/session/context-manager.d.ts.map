{"version": 3, "file": "context-manager.d.ts", "sourceRoot": "", "sources": ["../../../src/session/context-manager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAkB,iBAAiB,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,iBAAiB,CAAC;AAC5G,OAAO,EAAE,aAAa,EAA4B,MAAM,oBAAoB,CAAC;AAG7E,UAAU,mBAAmB;IACzB,cAAc,EAAE,MAAM,CAAC;IACvB,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB,UAAU,CAAC,EAAE,UAAU,GAAG,UAAU,CAAC;IACrC,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAChC,eAAe,EAAE,OAAO,CAAC;IACzB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,eAAe,CAAC,EAAE,iBAAiB,EAAE,CAAC;IACtC,cAAc,CAAC,EAAE,eAAe,EAAE,CAAC;IACnC,gBAAgB,CAAC,EAAE,qBAAqB,EAAE,CAAC;IAC3C,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,gBAAgB,CAAC,EAAE,GAAG,CAAC;IACvB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAChC,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,uBAAuB,CAAC,EAAE,MAAM,CAAC;CACpC;AAGD,cAAM,UAAU,CAAC,CAAC,EAAE,CAAC,CAAE,SAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxB,OAAO,CAAC,OAAO;gBAAP,OAAO,GAAE,MAAa;IAI1C,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,IAAI;CAiB9B;AAED,UAAU,iBAAiB;IACvB,eAAe,EAAE,OAAO,CAAC;IACzB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,WAAW,EAAE,MAAM,CAAC;IACpB,eAAe,EAAE,OAAO,CAAC;IACzB,eAAe,EAAE,KAAK,CAAC;QACnB,IAAI,EAAE,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC;QACtC,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,MAAM,CAAC;QAClB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,UAAU,CAAC,EAAE,MAAM,CAAC;KACvB,CAAC,CAAC;IACH,cAAc,EAAE,KAAK,CAAC;QAClB,IAAI,EAAE,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC;QACtC,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,MAAM,CAAC,EAAE,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;KACrC,CAAC,CAAC;IACH,gBAAgB,EAAE,KAAK,CAAC;QACpB,IAAI,EAAE,MAAM,CAAC;QACb,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,EAAE,MAAM,CAAC;KACtB,CAAC,CAAC;IACH,cAAc,EAAE,MAAM,CAAC;IACvB,gBAAgB,EAAE,MAAM,CAAC;IACzB,iBAAiB,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;CAC/C;AAED,UAAU,eAAe;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,YAAY,EAAE,MAAM,CAAC;IACrB,gBAAgB,EAAE,GAAG,GAAG,IAAI,CAAC;IAC7B,gBAAgB,EAAE,MAAM,CAAC;IACzB,eAAe,EAAE,OAAO,CAAC;CAC5B;AAED,UAAU,YAAY;IAClB,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,aAAa,EAAE,MAAM,CAAC;IACtB,cAAc,EAAE,OAAO,CAAC;IACxB,kBAAkB,EAAE,MAAM,GAAG,IAAI,CAAC;IAClC,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,mBAAmB,EAAE,MAAM,CAAC;IAC5B,mBAAmB,CAAC,EAAE,OAAO,CAAC;CACjC;AAED,MAAM,WAAW,cAAc;IAC3B,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE,aAAa,CAAC;IAC7B,iBAAiB,EAAE,iBAAiB,CAAC;IACrC,eAAe,EAAE,eAAe,CAAC;IACjC,YAAY,EAAE,YAAY,CAAC;CAC9B;AAGD,qBAAa,cAAc;IAChB,YAAY,EAAE,UAAU,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;IACxD,OAAO,CAAC,gBAAgB,CAA6B;IACrD,OAAO,CAAC,mBAAmB,CAAS;IACpC,OAAO,CAAC,mBAAmB,CAAS;;IAUpC,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,GAAG,cAAc;IAmDjF,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,cAAc,GAAG,SAAS;IAK9D,sBAAsB,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,GAAE,MAAkB,GAAG,IAAI;IAYzE,UAAU,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO;IAcpC,wBAAwB,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM;IAOjD,kBAAkB,IAAI,IAAI;IAc1B,kBAAkB,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAqDlD,mBAAmB,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAO1C,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG;QAChC,UAAU,EAAE,OAAO,CAAC;QACpB,UAAU,EAAE,OAAO,CAAC;QACpB,aAAa,EAAE,MAAM,CAAC;QACtB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,cAAc,EAAE,OAAO,CAAC;QACxB,YAAY,EAAE,MAAM,GAAG,SAAS,CAAC;QACjC,kBAAkB,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;KACjD;IAgBD,eAAe,IAAI;QACf,aAAa,EAAE,MAAM,CAAC;QACtB,gBAAgB,EAAE,MAAM,CAAC;QACzB,aAAa,EAAE,MAAM,CAAC;QACtB,aAAa,EAAE,MAAM,CAAC;KACzB;IAUD;;OAEG;IACH,gBAAgB,IAAI,IAAI;CAS3B"}