{"version": 3, "file": "context-manager.js", "sourceRoot": "", "sources": ["../../../src/session/context-manager.ts"], "names": [], "mappings": ";;;AAiCA,gCAAgC;AAChC,MAAM,UAAiB,SAAQ,GAAS;IAChB;IAApB,YAAoB,UAAkB,IAAI;QACtC,KAAK,EAAE,CAAC;QADQ,YAAO,GAAP,OAAO,CAAe;IAE1C,CAAC;IAED,GAAG,CAAC,GAAM,EAAE,KAAQ;QAChB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YAC1C,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,sDAAsD,QAAQ,EAAE,EAAE;oBAC1E,OAAO,EAAE,IAAI,CAAC,IAAI;oBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,UAAU,EAAE,QAAQ;oBACpB,gBAAgB,EAAE,OAAO,QAAQ;oBACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;CACJ;AA0DD,sCAAsC;AACtC,MAAa,cAAc;IAChB,YAAY,CAAqC;IAChD,gBAAgB,CAA6B;IAC7C,mBAAmB,CAAS;IAC5B,mBAAmB,CAAS;IAEpC;QACI,IAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAyB,IAAI,CAAC,CAAC,CAAC,2DAA2D;QAC7H,IAAI,CAAC,gBAAgB,GAAG,IAAI,UAAU,CAAiB,IAAI,CAAC,CAAC,CAAC,0DAA0D;QACxH,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC,+BAA+B;IACpE,CAAC;IAED,oCAAoC;IACpC,kBAAkB,CAAC,OAAe,EAAE,OAA4B;QAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,cAAc,GAAmB;YACnC,OAAO;YACP,SAAS;YACT,aAAa,EAAE;gBACX,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,KAAK,EAAE,OAAO,CAAC,KAAoB;gBACnC,KAAK,EAAE,OAAO,CAAC,KAAoB;gBACnC,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;gBAC5C,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,GAAG;gBACjC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,UAAU;gBAC5C,sBAAsB,EAAE,OAAO,CAAC,sBAAsB,IAAI,OAAO,CAAC,cAAc;aACnF;YACD,iBAAiB,EAAE;gBACf,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,KAAK;gBACnD,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;gBACtC,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,KAAK;gBACjD,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,EAAE;gBAC9C,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,EAAE;gBAC5C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,EAAE,EAAE,8BAA8B;gBAChF,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,SAAS;gBACnD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,CAAC;gBAC/C,iBAAiB,EAAE,CAAC,OAAO,CAAC,iBAAiB,IAAI,MAAM,CAA6B;aACvF;YACD,eAAe,EAAE;gBACb,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,YAAY,EAAE,SAAS;gBACvB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,IAAI;gBAClD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,SAAS;gBACvD,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,KAAK;aACpD;YACD,YAAY,EAAE;gBACV,gBAAgB,EAAE,IAAI;gBACtB,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;gBACtD,cAAc,EAAE,KAAK;gBACrB,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB,EAAE,IAAI;gBACtB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;aAChD;SACJ,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,8BAA8B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC7F,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED,wCAAwC;IACxC,iBAAiB,CAAC,OAAe;QAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,mDAAmD;IACnD,sBAAsB,CAAC,OAAe,EAAE,SAAiB,SAAS;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3C,OAAO,CAAC,YAAY,CAAC,kBAAkB,GAAG,MAAM,CAAC;YACjD,OAAO,CAAC,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACnD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,oCAAoC,MAAM,EAAE,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;IAED,oCAAoC;IACpC,UAAU,CAAC,OAAe;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9D,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC;QAChF,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,YAAY;QAE5C,OAAO,aAAa,GAAG,IAAI,CAAC,mBAAmB,IAAI,qBAAqB,GAAG,eAAe,CAAC;IAC/F,CAAC;IAED,qCAAqC;IACrC,wBAAwB,CAAC,OAAe;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAChD,OAAO,OAAO,GAAG,CAAC,CAAC;IACvB,CAAC;IAED,wBAAwB;IACxB,kBAAkB;QACd,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,SAAS;QAEjC,KAAK,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;gBACnC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAClC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,kCAAkC,CAAC,CAAC;YAClE,CAAC;QACL,CAAC;IACL,CAAC;IAED,oEAAoE;IACpE,kBAAkB,CAAC,OAAe;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC;QACzD,MAAM,kBAAkB,GAAG,OAAO,CAAC,YAAY,CAAC,kBAAkB,IAAI,kBAAkB,CAAC;QAEzF,mEAAmE;QACnE,IAAI,uBAAuB,GAAG,EAAE,CAAC;QACjC,IAAI,uBAAuB,GAAG,EAAE,CAAC;QAEjC,2BAA2B;QAC3B,IAAI,OAAO,CAAC,iBAAiB,EAAE,eAAe,IAAI,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrG,MAAM,eAAe,GAAG,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACxE,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC;gBACvE,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC;gBAC/D,OAAO,IAAI,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;YACvD,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,uBAAuB,IAAI,0BAA0B,eAAe,IAAI,CAAC;QAC7E,CAAC;QAED,uCAAuC;QACvC,IAAI,OAAO,CAAC,iBAAiB,EAAE,cAAc,IAAI,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnG,MAAM,aAAa,GAAG,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACrE,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC;gBACvE,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC;gBAC/D,MAAM,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9F,OAAO,IAAI,SAAS,KAAK,OAAO,GAAG,UAAU,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;YACpE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,uBAAuB,IAAI,yBAAyB,aAAa,IAAI,CAAC;QAC1E,CAAC;QAED,qDAAqD;QACrD,IAAI,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,OAAO,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvG,MAAM,SAAS,GAAG,OAAO,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACtE,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC;gBAClE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;gBACvD,OAAO,IAAI,SAAS,kBAAkB,UAAU,kBAAkB,MAAM,CAAC,IAAI,EAAE,CAAC;YACpF,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,uBAAuB,GAAG,+BAA+B,SAAS,IAAI,CAAC;QAC3E,CAAC;QAED,wDAAwD;QACxD,MAAM,oBAAoB,GAAG,OAAO,CAAC,aAAa,EAAE,sBAAsB,IAAI,OAAO,CAAC,aAAa,EAAE,cAAc,IAAI,EAAE,CAAC;QAC1H,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,CAAC;QAEnG,4EAA4E;QAC5E,OAAO,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,oBAAoB,IAAI,EAAE,CAAC;IACtE,CAAC;IAED,wBAAwB;IACxB,mBAAmB,CAAC,OAAe;QAC/B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,2BAA2B,CAAC,CAAC;IAC5D,CAAC;IAED,qCAAqC;IACrC,iBAAiB,CAAC,OAAe;QAS7B,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9D,OAAO;YACH,UAAU,EAAE,CAAC,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACpC,aAAa;YACb,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,cAAc,EAAE,OAAO,EAAE,YAAY,EAAE,cAAc,IAAI,KAAK;YAC9D,YAAY,EAAE,OAAO,EAAE,eAAe,EAAE,YAAY;YACpD,kBAAkB,EAAE,OAAO,EAAE,YAAY,EAAE,kBAAkB;SAChE,CAAC;IACN,CAAC;IAED,yBAAyB;IACzB,eAAe;QAMX,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,OAAO;YACH,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YACrC,gBAAgB,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,MAAM;YAChF,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC9D,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SACjE,CAAC;IACN,CAAC;IAED;;OAEG;IACH,gBAAgB;QACZ,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAEjD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAE9B,OAAO,CAAC,GAAG,CAAC,8BAA8B,YAAY,iBAAiB,aAAa,oBAAoB,CAAC,CAAC;IAC9G,CAAC;CACJ;AApOD,wCAoOC"}