import { ConversationEntry, TranscriptEntry, SpeechTranscriptEntry } from '../types/global';
import { SessionConfig } from '../types/websocket';
interface SessionContextInput {
    aiInstructions: string;
    voice: string;
    model: string;
    targetName?: string;
    targetPhoneNumber?: string;
    isIncomingCall?: boolean;
    scriptId?: string | null;
    scriptType?: 'incoming' | 'outbound';
    originalAIInstructions?: string;
    isSessionActive: boolean;
    summaryRequested?: boolean;
    summaryText?: string;
    summaryReceived?: boolean;
    conversationLog?: ConversationEntry[];
    fullTranscript?: TranscriptEntry[];
    speechTranscript?: SpeechTranscriptEntry[];
    lastAIResponse?: number;
    responseTimeouts?: number;
    connectionQuality?: string;
    streamSid?: string;
    audioBufferState?: any;
    sessionStartTime?: number;
    keepAliveActive?: boolean;
    maxConversationLogSize?: number;
    maxTranscriptSize?: number;
    maxSpeechTranscriptSize?: number;
}
declare class BoundedMap<K, V> extends Map<K, V> {
    private maxSize;
    constructor(maxSize?: number);
    set(key: K, value: V): this;
}
interface ConversationState {
    isSessionActive: boolean;
    summaryRequested: boolean;
    summaryText: string;
    summaryReceived: boolean;
    conversationLog: Array<{
        role: 'user' | 'assistant' | 'system';
        content: string;
        timestamp: number;
        messageId?: string;
        confidence?: number;
    }>;
    fullTranscript: Array<{
        role: 'user' | 'assistant' | 'system';
        content: string;
        timestamp: number;
        confidence?: number;
        source?: 'speech' | 'text' | 'ai';
    }>;
    speechTranscript: Array<{
        text: string;
        timestamp: number;
        confidence: number;
    }>;
    lastAIResponse: number;
    responseTimeouts: number;
    connectionQuality: 'good' | 'fair' | 'poor';
}
interface ConnectionState {
    streamSid?: string;
    lastActivity: number;
    audioBufferState: any | null;
    sessionStartTime: number;
    keepAliveActive: boolean;
}
interface RecoveryInfo {
    lastRecoveryTime: number | null;
    recoveryCount: number;
    wasInterrupted: boolean;
    interruptionReason: string | null;
    interruptionTime: number | null;
    maxRecoveryAttempts: number;
    autoRecoveryEnabled?: boolean;
}
export interface SessionContext {
    callSid: string;
    timestamp: number;
    sessionConfig: SessionConfig;
    conversationState: ConversationState;
    connectionState: ConnectionState;
    recoveryInfo: RecoveryInfo;
}
export declare class ContextManager {
    contextStore: BoundedMap<string, SessionContext>;
    private recoveryAttempts;
    private maxRecoveryAttempts;
    private contextSaveInterval;
    constructor();
    saveSessionContext(callSid: string, context: SessionContextInput): SessionContext;
    getSessionContext(callSid: string): SessionContext | undefined;
    markSessionInterrupted(callSid: string, reason?: string): void;
    canRecover(callSid: string): boolean;
    incrementRecoveryAttempt(callSid: string): number;
    cleanupOldContexts(): void;
    getRecoveryMessage(callSid: string): string | null;
    clearSessionContext(callSid: string): void;
    getRecoveryStatus(callSid: string): {
        hasContext: boolean;
        canRecover: boolean;
        recoveryCount: number;
        maxRecoveryAttempts: number;
        wasInterrupted: boolean;
        lastActivity: number | undefined;
        interruptionReason: string | null | undefined;
    };
    getContextStats(): {
        totalContexts: number;
        activeRecoveries: number;
        oldestContext: number;
        newestContext: number;
    };
    /**
     * Clear all contexts (for shutdown)
     */
    clearAllContexts(): void;
}
export {};
//# sourceMappingURL=context-manager.d.ts.map