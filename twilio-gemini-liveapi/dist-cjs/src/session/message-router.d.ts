import { forwardAudio } from '../audio/audio-forwarding';
import { AudioProcessor } from '../audio/audio-processor';
import type { ConnectionData, GeminiLiveMessage } from '../types/global';
type ExtendedConnectionData = ConnectionData & {
    sessionReady?: boolean;
    sessionInitialized?: number;
};
interface MessageRouterDeps {
    audioProcessor: AudioProcessor;
    activeConnections?: Map<string, ConnectionData> | null;
    sessionMetrics?: Map<string, {
        messagesReceived: number;
        lastActivity: number;
    }>;
    forwardAudioFn?: typeof forwardAudio;
}
export declare function routeGeminiMessage(callSid: string, message: GeminiLiveMessage, connectionData: ExtendedConnectionData, deps: MessageRouterDeps): Promise<void>;
export {};
//# sourceMappingURL=message-router.d.ts.map