"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionSummaryManager = void 0;
// Session Summary Manager - handles session summary generation and storage
const promises_1 = require("fs/promises");
const path_1 = __importDefault(require("path"));
const url_1 = require("url");
const summary_generator_1 = require("./summary-generator");
const logger_1 = require("../utils/logger");
const __filename = (0, url_1.fileURLToPath)(import.meta.url);
const __dirname = path_1.default.dirname(__filename);
class SessionSummaryManager {
    summaryTimeouts;
    summaryInProgress;
    defaultSummaryTimeout;
    summaryPrompts;
    constructor() {
        this.summaryTimeouts = new Map(); // Track summary timeouts
        this.summaryInProgress = new Set(); // Track sessions currently generating summaries
        this.defaultSummaryTimeout = 30000; // 30 seconds timeout for summary generation (increased from 15s)
        // Summary prompts - use environment variable only, no hardcoded fallbacks
        this.summaryPrompts = {
            outbound_call: process.env.SUMMARY_GENERATION_PROMPT || '',
            inbound_call: process.env.SUMMARY_GENERATION_PROMPT || '',
            outbound_test: process.env.SUMMARY_GENERATION_PROMPT || '',
            inbound_test: process.env.SUMMARY_GENERATION_PROMPT || '',
            default: process.env.SUMMARY_GENERATION_PROMPT || ''
        };
    }
    /**
     * Request session summary from AI with flow-specific prompts
     * @param callSid - Call/session ID
     * @param connectionData - Connection data with Gemini session
     * @param contextManager - Context manager for saving summary state
     * @returns Success status
     */
    async requestSummary(callSid, connectionData, contextManager) {
        const extConnectionData = connectionData;
        if (!extConnectionData || extConnectionData.summaryRequested || extConnectionData.summaryReceived) {
            logger_1.sessionLogger.debug(`[${callSid}] Summary request skipped (already requested/received or no connection data)`);
            return false;
        }
        if (this.summaryInProgress.has(callSid)) {
            logger_1.sessionLogger.debug(`[${callSid}] Summary generation already in progress`);
            return false;
        }
        // Determine flow type for appropriate summary prompt
        const flowType = this.determineFlowType(extConnectionData);
        const summaryPrompt = this.getSummaryPrompt(flowType);
        logger_1.sessionLogger.info(`[${callSid}] Requesting ${flowType} session summary from AI`);
        extConnectionData.summaryRequested = true;
        extConnectionData.summaryText = '';
        extConnectionData.summaryFlowType = flowType;
        this.summaryInProgress.add(callSid);
        try {
            // Check if we have a valid Gemini session
            if (!extConnectionData.geminiSession) {
                logger_1.sessionLogger.warn(`[${callSid}] No active Gemini session for summary request`);
                return await this.generateFallbackSummary(callSid, extConnectionData, contextManager);
            }
            // Set timeout for summary generation
            const timeoutId = setTimeout(async () => {
                logger_1.sessionLogger.warn(`[${callSid}] Summary generation timeout - using fallback`);
                await this.handleSummaryTimeout(callSid, extConnectionData, contextManager);
            }, this.defaultSummaryTimeout);
            extConnectionData.summaryTimeoutId = timeoutId;
            this.summaryTimeouts.set(callSid, timeoutId);
            // Send flow-specific summary request to AI using Live API - FIXED METHOD
            // Use sendRealtimeInput with proper text data format
            await extConnectionData.geminiSession.sendRealtimeInput({
                media: {
                    data: Buffer.from(summaryPrompt, 'utf-8').toString('base64'),
                    mimeType: 'text/plain'
                }
            });
            logger_1.sessionLogger.info(`[${callSid}] ${flowType} summary request sent to AI successfully`);
            return true;
        }
        catch (error) {
            logger_1.sessionLogger.error(`[${callSid}] Error requesting summary from AI`, error);
            this.summaryInProgress.delete(callSid);
            return await this.generateFallbackSummary(callSid, extConnectionData, contextManager);
        }
    }
    /**
     * Determine the flow type from connection data
     * @param connectionData - Connection data
     * @returns Flow type
     */
    determineFlowType(connectionData) {
        if (connectionData.flowType) {
            return connectionData.flowType;
        }
        if (connectionData.sessionType === 'local_test') {
            return connectionData.isIncomingCall ? 'inbound_test' : 'outbound_test';
        }
        if (connectionData.sessionType === 'twilio_call') {
            return connectionData.isIncomingCall ? 'inbound_call' : 'outbound_call';
        }
        // Fallback determination
        if (connectionData.isTestMode) {
            return connectionData.isIncomingCall ? 'inbound_test' : 'outbound_test';
        }
        return connectionData.isIncomingCall ? 'inbound_call' : 'outbound_call';
    }
    /**
     * Get appropriate summary prompt for flow type
     * @param flowType - Flow type
     * @returns Summary prompt
     */
    getSummaryPrompt(flowType) {
        return this.summaryPrompts[flowType] || this.summaryPrompts.default;
    }
    /**
     * Handle summary response from AI
     * @param callSid - Call/session ID
     * @param summaryText - Summary text from AI
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     */
    async handleSummaryResponse(callSid, summaryText, connectionData, contextManager) {
        const extConnectionData = connectionData;
        if (!this.summaryInProgress.has(callSid)) {
            logger_1.sessionLogger.warn(`[${callSid}] Received summary but no generation in progress`);
            return;
        }
        logger_1.sessionLogger.info(`[${callSid}] Received summary from AI (${summaryText.length} characters)`);
        // Clear timeout
        this.clearSummaryTimeout(callSid, extConnectionData);
        // Save summary
        await this.saveSummaryInfo(callSid, summaryText, 'neutral', 'completed', extConnectionData.targetName, extConnectionData.targetPhoneNumber);
        // Mark as received
        extConnectionData.summaryReceived = true;
        extConnectionData.summaryText = summaryText;
        this.summaryInProgress.delete(callSid);
        // Update context
        if (contextManager) {
            const context = contextManager.getSessionContext(callSid);
            if (context) {
                context.conversationState.summaryReceived = true;
                context.conversationState.summaryText = summaryText;
                contextManager.contextStore.set(callSid, context);
            }
        }
        logger_1.sessionLogger.info(`[${callSid}] Summary processed and saved successfully`);
    }
    /**
     * Generate fallback summary from conversation data with flow-specific formatting
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     * @returns Success status
 */
    async generateFallbackSummary(callSid, connectionData, contextManager) {
        logger_1.sessionLogger.info(`[${callSid}] Generating fallback summary from conversation data`);
        try {
            const flowType = this.determineFlowType(connectionData);
            const prompt = this.getSummaryPrompt(flowType);
            const summary = (0, summary_generator_1.generateLocalSummary)(connectionData.conversationLog || [], prompt);
            await this.saveSummaryInfo(callSid, summary, 'neutral', 'completed', connectionData.targetName, connectionData.targetPhoneNumber, flowType);
            connectionData.summaryReceived = true;
            connectionData.summaryText = summary;
            connectionData.summaryFlowType = flowType;
            this.summaryInProgress.delete(callSid);
            logger_1.sessionLogger.info(`[${callSid}] ${flowType} fallback summary generated and saved successfully`);
            return true;
        }
        catch (error) {
            logger_1.sessionLogger.error(`[${callSid}] Error generating fallback summary`, error);
            this.summaryInProgress.delete(callSid);
            return false;
        }
    }
    /**
     * Handle summary timeout
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     */
    async handleSummaryTimeout(callSid, connectionData, contextManager) {
        logger_1.sessionLogger.warn(`[${callSid}] Summary generation timed out`);
        this.clearSummaryTimeout(callSid, connectionData);
        if (connectionData.summaryText && connectionData.summaryText.length > 0) {
            // Use partial summary if available
            logger_1.sessionLogger.info(`[${callSid}] Using partial summary received before timeout`);
            await this.saveSummaryInfo(callSid, connectionData.summaryText, 'neutral', 'timeout', connectionData.targetName, connectionData.targetPhoneNumber);
        }
        else {
            // Generate fallback summary
            await this.generateFallbackSummary(callSid, connectionData, contextManager);
        }
        connectionData.summaryReceived = true;
        this.summaryInProgress.delete(callSid);
    }
    /**
     * Clear summary timeout
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    clearSummaryTimeout(callSid, connectionData) {
        const timeoutId = this.summaryTimeouts.get(callSid);
        if (timeoutId) {
            clearTimeout(timeoutId);
            this.summaryTimeouts.delete(callSid);
        }
        if (connectionData && connectionData.summaryTimeoutId) {
            clearTimeout(connectionData.summaryTimeoutId);
            connectionData.summaryTimeoutId = undefined;
        }
    }
    /**
     * Save summary information to file with flow type support
     * @param callSid - Call/session ID
     * @param rawSummaryText - Raw summary text
     * @param defaultSentiment - Default sentiment
     * @param status - Call status
     * @param targetName - Target name
     * @param targetPhoneNumber - Target phone number
     * @param flowType - Flow type (outbound_call, inbound_call, etc.)
     */
    async saveSummaryInfo(callSid, rawSummaryText, defaultSentiment = 'neutral', status = 'completed', targetName = null, targetPhoneNumber = null, flowType = null) {
        const dataDir = path_1.default.join(process.cwd(), 'data');
        const infoFilePath = path_1.default.join(dataDir, `${callSid}_info.json`);
        logger_1.sessionLogger.info(`[${callSid}] Saving summary info. Status: ${status}, Target: ${targetName}`);
        logger_1.sessionLogger.debug(`[${callSid}] Summary text: "${rawSummaryText?.substring(0, 100)}..." (length: ${rawSummaryText?.length || 0})`);
        let summary = 'Summary generation failed or was not applicable.';
        let sentiment = defaultSentiment;
        // Parse summary and sentiment
        if (rawSummaryText && status !== 'incomplete' && status !== 'error' && !['failed', 'canceled', 'no-answer', 'busy'].includes(status)) {
            const sentimentMatch = rawSummaryText.match(/sentiment:\s*(positive|neutral|negative)/i);
            if (sentimentMatch && sentimentMatch[1]) {
                sentiment = sentimentMatch[1].toLowerCase();
                summary = rawSummaryText.replace(/sentiment:\s*(positive|neutral|negative)/i, '').trim();
                console.log(`🎭 [${callSid}] Parsed sentiment: ${sentiment}`);
            }
            else {
                summary = rawSummaryText.trim();
            }
        }
        else if (rawSummaryText) {
            summary = rawSummaryText;
        }
        else if (['failed', 'canceled', 'no-answer', 'busy'].includes(status)) {
            summary = `Call ended with status: ${status}. No summary generated.`;
            sentiment = 'neutral';
        }
        else if (status === 'timeout') {
            summary = 'Summary generation timed out.';
            sentiment = 'neutral';
        }
        else if (status === 'completed' && targetName) {
            summary = `Call completed with ${targetName}. AI summary generation was not successful, but the call connected and ended normally.`;
            sentiment = 'neutral';
        }
        const summaryData = {
            callSid: callSid,
            call_summary: summary,
            customer_sentiment: sentiment,
            status: status,
            targetName: targetName || 'Unknown',
            targetPhoneNumber: targetPhoneNumber || 'Unknown',
            timestamp: new Date().toISOString()
        };
        try {
            // Ensure data directory exists
            await (0, promises_1.mkdir)(dataDir, { recursive: true });
            // Read existing data if available
            let existingData = {};
            try {
                const existingContent = await (0, promises_1.readFile)(infoFilePath, 'utf8');
                existingData = JSON.parse(existingContent);
            }
            catch (readError) {
                if (readError.code !== 'ENOENT') {
                    throw readError;
                }
            }
            // Merge new data with existing data (preserve recordingUrl, etc.)
            const finalData = { ...existingData, ...summaryData };
            await (0, promises_1.writeFile)(infoFilePath, JSON.stringify(finalData, null, 2));
            console.log(`✅ [${callSid}] Summary info saved to ${infoFilePath}`);
        }
        catch (error) {
            console.error(`❌ [${callSid}] Error saving summary info:`, error);
        }
    }
    /**
     * Check if summary is in progress
     * @param callSid - Call/session ID
     * @returns True if summary is in progress
     */
    isSummaryInProgress(callSid) {
        return this.summaryInProgress.has(callSid);
    }
    /**
     * Get summary status
     * @param callSid - Call/session ID
     * @returns Summary status
     */
    getSummaryStatus(callSid) {
        return {
            inProgress: this.summaryInProgress.has(callSid),
            hasTimeout: this.summaryTimeouts.has(callSid),
            timestamp: Date.now()
        };
    }
    /**
     * Clean up summary tracking for a session
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    cleanupSummary(callSid, connectionData) {
        this.clearSummaryTimeout(callSid, connectionData);
        this.summaryInProgress.delete(callSid);
        logger_1.sessionLogger.debug(`[${callSid}] Summary tracking cleaned up`);
    }
    /**
     * Clean up all summary resources (for shutdown)
     */
    cleanup() {
        const timeoutCount = this.summaryTimeouts.size;
        const progressCount = this.summaryInProgress.size;
        // Clear all summary timeouts
        for (const [callSid, timeout] of this.summaryTimeouts.entries()) {
            clearTimeout(timeout);
        }
        this.summaryTimeouts.clear();
        this.summaryInProgress.clear();
        logger_1.sessionLogger.info(`SummaryManager: Cleared ${timeoutCount} timeouts and ${progressCount} in-progress summaries`);
    }
}
exports.SessionSummaryManager = SessionSummaryManager;
//# sourceMappingURL=summary-manager.js.map