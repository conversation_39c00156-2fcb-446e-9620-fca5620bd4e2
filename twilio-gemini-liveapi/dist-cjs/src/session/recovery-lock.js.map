{"version": 3, "file": "recovery-lock.js", "sourceRoot": "", "sources": ["../../../src/session/recovery-lock.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAiD;AAEjD,MAAa,mBAAmB;IACpB,KAAK,GAIR,IAAI,GAAG,EAAE,CAAC;IAEP,mBAAmB,GAAiD,IAAI,GAAG,EAAE,CAAC;IACrE,WAAW,CAAS;IAErC,YAAY,WAAW,GAAG,KAAK;QAC3B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,WAAW,CAAC,GAAW;QACzB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,0CAA0C;YAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACzC,IAAI,YAAY,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBACzE,qCAAqC;gBACrC,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACxD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBAE3C,uBAAc,CAAC,KAAK,CAAC,wBAAwB,EAAE;oBAC3C,GAAG;oBACH,WAAW,EAAE,OAAO,CAAC,MAAM;oBAC3B,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,SAAS;iBAC/C,CAAC,CAAC;gBACH,OAAO;YACX,CAAC;YAED,kCAAkC;YAClC,IAAI,YAAY,EAAE,CAAC;gBACf,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC1B,CAAC;YAED,mBAAmB;YACnB,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,uBAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;gBAC/C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC1B,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAErB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;gBAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,cAAc,EAAE,OAAO;gBACvB,SAAS;aACZ,CAAC,CAAC;YAEH,uBAAc,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAW;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC;QAED,kBAAkB;QAClB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,uBAAc,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;QAElD,2BAA2B;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,EAAG,CAAC;YACrC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAE3C,iCAAiC;YACjC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,uBAAc,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;gBACzD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC1B,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAErB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;gBAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,SAAS;aACZ,CAAC,CAAC;YAEH,uBAAc,CAAC,KAAK,CAAC,wCAAwC,EAAE;gBAC3D,GAAG;gBACH,gBAAgB,EAAE,OAAO,CAAC,MAAM;aACnC,CAAC,CAAC;YAEH,WAAW,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACJ,gCAAgC;YAChC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,GAAW;QAChB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,aAAa;QAST,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YACjE,GAAG;YACH,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;YAChC,UAAU,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;SACnE,CAAC,CAAC,CAAC;QAEJ,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;aAC7D,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAEvD,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YAC5B,eAAe,EAAE,YAAY;YAC7B,KAAK;SACR,CAAC;IACN,CAAC;IAED;;OAEG;IACH,OAAO;QACH,qBAAqB;QACrB,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QAED,8BAA8B;QAC9B,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9D,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QAEjC,uBAAc,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IACtD,CAAC;CACJ;AAlKD,kDAkKC;AAED,8BAA8B;AACjB,QAAA,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC"}