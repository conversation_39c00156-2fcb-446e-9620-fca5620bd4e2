"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionManager = exports.BoundedSet = exports.BoundedMap = void 0;
const client_1 = require("../gemini/client");
const audio_processor_1 = require("../audio/audio-processor");
const audio_forwarding_1 = require("../audio/audio-forwarding");
const message_router_1 = require("./message-router");
const summary_generator_1 = require("./summary-generator");
const timer_manager_1 = require("../utils/timer-manager");
const logger_1 = require("../utils/logger");
const metrics_1 = require("./metrics");
const websocket_routing_1 = require("./websocket-routing");
const recovery_1 = require("./recovery");
var metrics_2 = require("./metrics");
Object.defineProperty(exports, "BoundedMap", { enumerable: true, get: function () { return metrics_2.BoundedMap; } });
Object.defineProperty(exports, "BoundedSet", { enumerable: true, get: function () { return metrics_2.BoundedSet; } });
// Bounded Map and Set for memory safety
// Session Manager for Gemini connections with recovery
class SessionManager {
    contextManager;
    geminiClient;
    recoveryInProgress;
    audioProcessor;
    sessionMetrics;
    activeConnections;
    earlyAudioBuffers;
    cleanupLocks;
    constructor(contextManager, geminiClient, activeConnections = null) {
        this.contextManager = contextManager;
        this.geminiClient = geminiClient;
        this.recoveryInProgress = new metrics_1.BoundedSet(500); // Increased from 200 to 500 for better recovery tracking
        this.audioProcessor = new audio_processor_1.AudioProcessor();
        this.sessionMetrics = new metrics_1.BoundedMap(2000); // Increased from 1000 to 2000 for better metrics retention
        this.activeConnections = activeConnections;
        this.earlyAudioBuffers = new metrics_1.BoundedMap(500); // Buffer for early audio packets with limit
        this.cleanupLocks = new metrics_1.BoundedMap(200); // Limit cleanup locks to prevent memory leak
    }
    // Create new Gemini session
    async createGeminiSession(callSid, config, connectionData) {
        const sessionStartTime = Date.now();
        const extConnectionData = connectionData;
        try {
            logger_1.sessionLogger.info(`🤖 [${callSid}] Creating Gemini session with model: ${config.model}, voice: ${config.voice}`);
            logger_1.sessionLogger.info(`🔍 [${callSid}] ===== SESSION MANAGER MODEL DEBUG =====`);
            logger_1.sessionLogger.info(`🔍 [${callSid}] config.model = "${config.model}"`);
            logger_1.sessionLogger.info(`🔍 [${callSid}] config.voice = "${config.voice}"`);
            logger_1.sessionLogger.info(`🔍 [${callSid}] process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);
            logger_1.sessionLogger.info(`🔍 [${callSid}] ==========================================`);
            // CRITICAL VALIDATION: Verify AI instructions are present and sufficient before creating session
            if (!config.aiInstructions || config.aiInstructions.trim().length === 0) {
                const errorMessage = `Cannot create Gemini session: AI instructions are missing or empty`;
                logger_1.sessionLogger.error(`❌ [${callSid}] VALIDATION FAILED: ${errorMessage}`);
                logger_1.sessionLogger.error(`❌ [${callSid}] Session config validation:`, {
                    hasConfig: !!config,
                    hasAiInstructions: !!config.aiInstructions,
                    instructionLength: config.aiInstructions?.length || 0,
                    sessionType: config.sessionType,
                    scriptId: config.scriptId
                });
                // Mark session as failed and return early
                extConnectionData.isSessionActive = false;
                extConnectionData.geminiSessionError = errorMessage;
                throw new Error(errorMessage);
            }
            // Additional validation: Check instruction length
            if (config.aiInstructions.trim().length < 100) {
                const errorMessage = `AI instructions too short (${config.aiInstructions.length} chars) - minimum 100 characters required`;
                logger_1.sessionLogger.error(`❌ [${callSid}] VALIDATION FAILED: ${errorMessage}`);
                extConnectionData.isSessionActive = false;
                extConnectionData.geminiSessionError = errorMessage;
                throw new Error(errorMessage);
            }
            logger_1.sessionLogger.info(`✅ [${callSid}] AI instructions validation passed (${config.aiInstructions.length} characters)`);
            const validationPreview = config.aiInstructions.substring(0, 200);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Instructions preview: ${validationPreview}...`);
            // Use arrow functions to maintain 'this' context instead of aliasing
            // Initialize session metrics BEFORE creating session to avoid race conditions
            this.sessionMetrics.set(callSid, {
                startTime: Date.now(),
                messagesReceived: 0,
                messagesSent: 0,
                recoveryCount: 0,
                lastActivity: Date.now(),
                isInitializing: true
            });
            // Store reference immediately to prevent race conditions
            extConnectionData.geminiSession = undefined;
            extConnectionData.isSessionActive = false;
            // Store config for callback access
            extConnectionData.sessionConfig = config;
            // Create session and store reference immediately
            // Add call direction context to system instructions
            let systemInstruction = config.aiInstructions;
            logger_1.sessionLogger.info(`🎯 [${callSid}] ===== SYSTEM INSTRUCTION DEBUG =====`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Has config: ${!!config}`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Has AI instructions: ${!!config.aiInstructions}`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Original instruction length: ${config.aiInstructions?.length || 0}`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Final instruction length: ${systemInstruction?.length || 0}`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Script ID: ${config.scriptId || 'NONE'}`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Campaign ID: ${config.campaignId || 'NONE'}`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Script type: ${config.scriptType || 'NONE'}`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Session type: ${config.sessionType || 'NONE'}`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] Is incoming call: ${config.isIncomingCall || false}`);
            const instructionPreview = systemInstruction?.substring(0, 300) || 'NO INSTRUCTIONS';
            logger_1.sessionLogger.info(`🎯 [${callSid}] First 300 chars: ${instructionPreview}...`);
            logger_1.sessionLogger.info(`🎯 [${callSid}] ====================================`);
            // Add context based on call direction for Twilio calls
            if (config.sessionType === 'twilio_call' && systemInstruction) {
                if (!config.isIncomingCall) {
                    // Outbound calls: AI initiates the conversation
                    systemInstruction = `${systemInstruction}\n\n` +
                        'You are making an outbound call. When the call connects, introduce yourself and ' +
                        'begin the conversation according to the script above. Start speaking immediately when the call is answered.';
                }
                else {
                    // Inbound calls: AI should greet the caller and wait for their response
                    systemInstruction = `${systemInstruction}\n\n` +
                        'You are receiving an inbound call. When the customer speaks, greet them warmly and ' +
                        'assist them according to the script above.';
                }
            }
            // Fallback for inbound calls without instructions
            if (!systemInstruction && config.isIncomingCall && config.sessionType === 'twilio_call') {
                logger_1.sessionLogger.warn(`No AI instructions found for inbound call, using fallback`);
                systemInstruction = 'You are a helpful customer service representative. ' +
                    'Greet the caller warmly and ask how you can help them today.';
            }
            // CRITICAL DEBUG: Log full configuration being sent to Gemini
            logger_1.sessionLogger.info(`🔍 [${callSid}] Attempting Gemini connection with config:`, {
                model: config.model,
                voice: config.voice,
                sessionType: config.sessionType,
                isIncomingCall: config.isIncomingCall,
                hasSystemInstruction: !!systemInstruction,
                instructionLength: systemInstruction?.length,
                hasGeminiClient: !!this.geminiClient,
                hasLiveAPI: !!(this.geminiClient && 'live' in this.geminiClient)
            });
            if (systemInstruction) {
                logger_1.sessionLogger.info(`🎯 [${callSid}] System instruction preview: ${systemInstruction.substring(0, 300)}...`);
            }
            else {
                logger_1.sessionLogger.error(`❌ [${callSid}] CRITICAL: No system instruction available!`);
            }
            logger_1.sessionLogger.info(`🚀 [${callSid}] Connecting to Gemini Live API with system instruction...`);
            const geminiSession = await this.geminiClient.live.connect({
                model: config.model,
                systemInstruction: systemInstruction ? {
                    parts: [{
                            text: systemInstruction
                        }]
                } : undefined,
                generationConfig: {
                    responseModalities: ['AUDIO'],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                callbacks: {
                    onopen: () => {
                        logger_1.sessionLogger.info(`✅ [${callSid}] Gemini session opened`);
                        // CRITICAL: Don't immediately mark as active - validate first
                        logger_1.sessionLogger.info(`🔄 [${callSid}] Validating session before activation...`);
                        // Update metrics state
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.isInitializing = false;
                        }
                        // Save initial context with bounded arrays
                        this.contextManager.saveSessionContext(callSid, {
                            ...config,
                            ...extConnectionData,
                            conversationLog: [],
                            fullTranscript: [],
                            speechTranscript: [], // Initialize bounded speech transcript
                            maxConversationLogSize: 1000, // Increased from 500 to 1000 for better conversation retention
                            maxTranscriptSize: 2000, // Increased from 1000 to 2000 for better transcript retention
                            maxSpeechTranscriptSize: 2000 // Increased from 1000 to 2000 for better speech transcript retention
                        });
                        // CRITICAL FIX: Live API setup is now handled in initial connection configuration
                        logger_1.sessionLogger.info(`✅ [${callSid}] Live API setup configuration included in connection`);
                        if (config.aiInstructions) {
                            const instructionSnippet = config.aiInstructions.substring(0, 200);
                            logger_1.sessionLogger.info(`🎯 [${callSid}] AI instructions included: ${instructionSnippet}...`);
                        }
                        else {
                            logger_1.sessionLogger.warn(`⚠️ [${callSid}] No AI instructions in config - session may not work properly`);
                        }
                        // Validate session setup
                        logger_1.sessionLogger.info(`🔍 [${callSid}] Session validation:`, {
                            hasSystemInstruction: !!systemInstruction,
                            instructionLength: systemInstruction?.length,
                            model: config.model,
                            voice: config.voice,
                            hasGeminiSession: !!extConnectionData.geminiSession
                        });
                        // CRITICAL: Mark as active immediately after validation - NO DELAYS
                        if (extConnectionData.geminiSession && !extConnectionData.geminiSessionError) {
                            extConnectionData.isSessionActive = true;
                            extConnectionData.sessionActivatedAt = Date.now();
                            // Send pending script if available
                            if (extConnectionData.pendingScript) {
                                // Use setTimeout to handle async operation in callback
                                setTimeout(async () => {
                                    try {
                                        await extConnectionData.geminiSession.sendRealtimeInput({
                                            media: {
                                                data: Buffer.from(extConnectionData.pendingScript, 'utf-8').toString('base64'),
                                                mimeType: 'text/plain'
                                            }
                                        });
                                        logger_1.sessionLogger.info(`✅ [${callSid}] Pending campaign script sent successfully`);
                                        extConnectionData.pendingScript = undefined; // Clear after sending
                                    }
                                    catch (error) {
                                        logger_1.sessionLogger.error(`❌ [${callSid}] Failed to send pending script:`, error instanceof Error ? error : new Error(String(error)));
                                    }
                                }, 0);
                            }
                            // Check if Twilio is also connected for full readiness
                            if (extConnectionData.twilioConnected) {
                                extConnectionData.fullyReady = true;
                                logger_1.sessionLogger.info(`🎯 [${callSid}] Session fully ready - both Gemini and Twilio connected`);
                            }
                            else {
                                logger_1.sessionLogger.info(`🔄 [${callSid}] Gemini session active, waiting for Twilio connection`);
                                // Set up a check to mark as fully ready when Twilio connects
                                this.setupTwilioReadinessCheck(callSid, extConnectionData);
                            }
                            logger_1.sessionLogger.info(`✅ [${callSid}] Session activated and ready for audio processing`);
                        }
                        else {
                            logger_1.sessionLogger.error(`❌ [${callSid}] Session validation failed during activation`);
                            extConnectionData.geminiSessionError = 'Session validation failed';
                        }
                        logger_1.sessionLogger.info(`🔄 [${callSid}] Session initialized, activation complete`);
                    },
                    onerror: (error) => {
                        logger_1.sessionLogger.error(`❌ [${callSid}] Gemini session error:`, error instanceof Error ? error : new Error(String(error)));
                        this.contextManager.markSessionInterrupted(callSid, 'session_error');
                        // Update metrics
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.recoveryCount++;
                        }
                        // Mark session as inactive but don't end it - let recovery manager handle it
                        extConnectionData.isSessionActive = false;
                        extConnectionData.geminiSessionError = error.message;
                        logger_1.sessionLogger.info(`🔄 [${callSid}] Gemini session error detected, session marked for recovery`);
                        // Recovery will be handled by the recovery manager's health checks or explicit recovery calls
                    },
                    onclose: (event) => {
                        const sessionDuration = Date.now() - sessionStartTime;
                        logger_1.sessionLogger.info(`🔌 [${callSid}] Gemini session closed after ${sessionDuration}ms`);
                        logger_1.sessionLogger.info(`🔌 [${callSid}] Close event details:`, {
                            code: event?.code,
                            reason: event?.reason,
                            wasClean: event?.wasClean,
                            sessionDuration: sessionDuration
                        });
                        extConnectionData.isSessionActive = false;
                        // Check for early session termination (likely initialization failure)
                        if (sessionDuration < 5000) {
                            logger_1.sessionLogger.error(`❌ [${callSid}] Session closed too quickly (${sessionDuration}ms) - likely initialization failure`);
                            logger_1.sessionLogger.error(`❌ [${callSid}] This suggests API key, model, or configuration issues`);
                        }
                        // Session closed - cleanup handled by lifecycle manager
                        // Check if this is an unexpected close (connection still active)
                        // Check if this is an unexpected close (connection still active)
                        const isUnexpectedClose = extConnectionData.ws && extConnectionData.ws.readyState === 1;
                        if (isUnexpectedClose) {
                            logger_1.sessionLogger.info(`⚠️ [${callSid}] Unexpected Gemini session close detected`);
                            this.contextManager.markSessionInterrupted(callSid, 'session_closed_unexpected');
                            // Session will be recovered by recovery manager's health checks or explicit recovery calls
                            logger_1.sessionLogger.info(`🔄 [${callSid}] Session marked for recovery due to unexpected close`);
                        }
                        else {
                            logger_1.sessionLogger.info(`✅ [${callSid}] Gemini session closed normally (connection also closed)`);
                        }
                    },
                    onmessage: async (message) => {
                        await (0, message_router_1.routeGeminiMessage)(callSid, message, extConnectionData, {
                            audioProcessor: this.audioProcessor,
                            activeConnections: this.activeConnections || undefined,
                            sessionMetrics: this.sessionMetrics,
                            forwardAudioFn: audio_forwarding_1.forwardAudio
                        });
                    }
                },
                config: {
                    responseModalities: [client_1.Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192
            });
            // CRITICAL FIX: Send campaign script to Gemini as TEXT (not media)
            logger_1.sessionLogger.info(`📝 [${callSid}] Session created - sending campaign script instructions to AI`);
            logger_1.sessionLogger.info(`🔍 [${callSid}] DIAGNOSTIC: AI instructions validation:`, {
                hasInstructions: !!config.aiInstructions,
                instructionLength: config.aiInstructions?.length || 0,
                isLongEnough: (config.aiInstructions?.length || 0) > 100,
                scriptId: config.scriptId,
                scriptType: config.scriptType
            });
            if (config.aiInstructions && config.aiInstructions.length > 100) {
                logger_1.sessionLogger.info(`📝 [${callSid}] Sending AI instructions (${config.aiInstructions.length} chars) to Gemini as TEXT`);
                // Store the script for delayed delivery to avoid race conditions
                extConnectionData.pendingScript = config.aiInstructions;
                logger_1.sessionLogger.info(`📝 [${callSid}] Campaign script queued for delivery when session is ready`);
            }
            else {
                logger_1.sessionLogger.error(`❌ [${callSid}] CRITICAL: No campaign script available or too short - Gemini will not receive instructions!`);
                logger_1.sessionLogger.error(`❌ [${callSid}] Script details:`, {
                    hasInstructions: !!config.aiInstructions,
                    length: config.aiInstructions?.length || 0,
                    scriptId: config.scriptId,
                    campaignId: config.campaignId
                });
            }
            // Store the session in connectionData immediately after creation
            // This prevents race conditions where callbacks might execute before assignment
            extConnectionData.geminiSession = geminiSession;
            // CRITICAL FIX: Ensure WebSocket is ready before marking session ready for audio
            const wsReady = !!(extConnectionData.ws || extConnectionData.twilioWs || extConnectionData.localWs);
            if (!wsReady) {
                logger_1.sessionLogger.error(`❌ [${callSid}] CRITICAL: No WebSocket connection available when initializing audio forwarding`, {
                    sessionType: config.sessionType,
                    isTwilioCall: extConnectionData.isTwilioCall,
                    hasWs: !!extConnectionData.ws,
                    hasTwilioWs: !!extConnectionData.twilioWs,
                    hasLocalWs: !!extConnectionData.localWs
                });
            }
            // Mark session as ready for audio processing only after WebSocket validation
            extConnectionData.sessionReady = wsReady;
            extConnectionData.sessionInitialized = Date.now();
            // Initialize audio forwarding for this session only if WebSocket is ready
            if (wsReady) {
                (0, audio_forwarding_1.initializeAudioForwarding)(callSid, extConnectionData, config.sessionType || 'unknown');
                logger_1.sessionLogger.info(`✅ [${callSid}] Audio forwarding initialized successfully`, {
                    sessionType: config.sessionType,
                    wsReady: wsReady
                });
            }
            else {
                logger_1.sessionLogger.error(`❌ [${callSid}] Audio forwarding NOT initialized - WebSocket not ready`);
            }
            // Verify session was created successfully
            if (!geminiSession) {
                throw new Error('Gemini session creation returned null/undefined');
            }
            logger_1.sessionLogger.info(`✅ [${callSid}] Gemini session created successfully and ready for audio`);
            // Add session health check after 5 seconds using timer manager
            timer_manager_1.timerManager.setTimeout(`${callSid}_health_check`, () => {
                if (extConnectionData.isSessionActive) {
                    logger_1.sessionLogger.info(`✅ [${callSid}] Session health check: still active after 5 seconds`);
                }
                else {
                    logger_1.sessionLogger.error(`❌ [${callSid}] Session health check: died within 5 seconds - check logs above for errors`);
                }
            }, 5000);
            return geminiSession;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.sessionLogger.error(`❌ [${callSid}] Failed to create Gemini session:`, error instanceof Error ? error : new Error(String(error)));
            logger_1.sessionLogger.error(`❌ [${callSid}] Error details:`, {
                message: errorMessage,
                code: error instanceof Error && 'code' in error ? error.code : undefined,
                details: error instanceof Error && 'details' in error ? error.details : undefined,
                stack: errorStack?.split('\n').slice(0, 5).join('\n') // First 5 lines of stack
            });
            // Log configuration that failed
            logger_1.sessionLogger.error(`❌ [${callSid}] Failed configuration:`, {
                model: config.model,
                voice: config.voice,
                hasInstructions: !!config.aiInstructions,
                sessionType: config.sessionType,
                isIncomingCall: config.isIncomingCall
            });
            // Mark session as failed
            extConnectionData.isSessionActive = false;
            extConnectionData.geminiSessionError = errorMessage;
            return null;
        }
    }
    // Note: Message handling is now done directly in the onmessage callback (like old implementation)
    // REMOVED: sendInitialMessage method - now using Live API setup configuration
    // Initial instructions are sent via setup message in session creation, not as client content
    // Legacy method for backward compatibility - Live API handles continuous conversation automatically
    async sendTextToGemini(sessionId, geminiSession, text) {
        logger_1.sessionLogger.info(`⚠️ [${sessionId}] sendTextToGemini called but not needed in Live API - text: ${text.substring(0, 100)}...`);
        // In Live API, text should be sent as audio through sendRealtimeInput
        // This is a no-op for backward compatibility
    }
    // Legacy method for backward compatibility - Live API handles turn management automatically
    async sendTurnComplete(sessionId, geminiSession) {
        logger_1.sessionLogger.info(`⚠️ [${sessionId}] sendTurnComplete called but not needed in Live API`);
        // Voice Activity Detection (VAD) in the Live API automatically manages conversation turns
        // This is a no-op for backward compatibility
    }
    async sendAudioToGemini(callSid, geminiSession, audioBuffer) {
        await (0, websocket_routing_1.sendAudioToGemini)(callSid, geminiSession, audioBuffer, {
            audioProcessor: this.audioProcessor,
            sessionMetrics: this.sessionMetrics,
            activeConnections: this.activeConnections,
            earlyAudioBuffers: this.earlyAudioBuffers
        });
    }
    // Send browser PCM audio to Gemini session (for local testing)
    async sendBrowserAudioToGemini(callSid, geminiSession, base64Audio) {
        await (0, websocket_routing_1.sendBrowserAudioToGemini)(callSid, geminiSession, base64Audio, {
            audioProcessor: this.audioProcessor,
            sessionMetrics: this.sessionMetrics,
            activeConnections: this.activeConnections,
            earlyAudioBuffers: this.earlyAudioBuffers
        });
    }
    // Recover session after interruption
    async recoverSession(callSid, reason) {
        await (0, recovery_1.recoverSession)(callSid, reason, this.contextManager, this.sessionMetrics, this.recoveryInProgress);
    }
    // Generate session summary
    async generateSummary(callSid, connectionData, summaryPrompt) {
        try {
            logger_1.sessionLogger.info(`📋 [${callSid}] Generating call summary`);
            if (!connectionData?.geminiSession) {
                logger_1.sessionLogger.warn(`⚠️ [${callSid}] No Gemini session for summary generation`);
                return false;
            }
            connectionData.summaryRequested = true;
            connectionData.summaryText = '';
            // ALTERNATIVE APPROACH: For Live API, we can't easily get text-only summaries
            // Instead, we'll generate a summary from the conversation log we've been tracking
            // This is more reliable and doesn't interfere with the continuous conversation
            logger_1.sessionLogger.info(`📝 [${callSid}] Generating summary from conversation log instead of requesting from AI`);
            // Generate summary from conversation log
            const conversationLog = connectionData.conversationLog || [];
            if (conversationLog.length > 0) {
                const summaryText = (0, summary_generator_1.generateLocalSummary)(conversationLog, summaryPrompt);
                connectionData.summaryText = summaryText;
                logger_1.sessionLogger.info(`✅ [${callSid}] Local summary generated: ${summaryText.substring(0, 100)}...`);
            }
            else {
                connectionData.summaryText = 'No conversation content available for summary.';
                logger_1.sessionLogger.info(`⚠️ [${callSid}] No conversation log available for summary`);
            }
            // Summary will be collected in the onmessage callback
            return true;
        }
        catch (error) {
            logger_1.sessionLogger.error(`❌ [${callSid}] Error generating summary:`, error instanceof Error ? error : new Error(String(error)));
            return false;
        }
    }
    // Get session metrics
    getSessionMetrics(callSid) {
        return this.sessionMetrics.get(callSid) || null;
    }
    // Clean up session
    async cleanupSession(callSid) {
        // Use a simple lock pattern to prevent concurrent cleanup
        const lockKey = `cleanup_${callSid}`;
        const cleanupInProgress = this.cleanupLocks.get(lockKey);
        if (cleanupInProgress) {
            // Cleanup already in progress, wait for it
            await cleanupInProgress;
            return;
        }
        // Create a promise to track this cleanup operation
        const cleanupPromise = this.performCleanup(callSid);
        this.cleanupLocks.set(lockKey, cleanupPromise);
        try {
            await cleanupPromise;
        }
        finally {
            this.cleanupLocks.delete(lockKey);
        }
    }
    async performCleanup(callSid) {
        this.sessionMetrics.delete(callSid);
        this.recoveryInProgress.delete(callSid);
        // Clean up early audio buffers to prevent memory leaks
        this.earlyAudioBuffers.delete(callSid);
        // Clear all timers for this session
        timer_manager_1.timerManager.clearSessionTimers(callSid);
        logger_1.sessionLogger.info(`🧹 [${callSid}] Session manager cleanup completed`);
    }
    /**
     * Set up a check to mark session as fully ready when Twilio connects
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    setupTwilioReadinessCheck(callSid, connectionData) {
        (0, websocket_routing_1.setupTwilioReadinessCheck)(callSid, connectionData, this.earlyAudioBuffers, this.activeConnections);
    }
    /**
     * Buffer early audio packets when session is not fully ready
     * @param callSid - Call/session ID
     * @param audioBuffer - Audio buffer to store
     */
    bufferEarlyAudio(callSid, audioBuffer) {
        (0, websocket_routing_1.bufferEarlyAudio)(callSid, audioBuffer, this.earlyAudioBuffers);
    }
    /**
     * Process buffered audio packets when session becomes ready
     * @param callSid - Call/session ID
     */
    async processBufferedAudio(callSid) {
        await (0, websocket_routing_1.processBufferedAudio)(callSid, {
            earlyAudioBuffers: this.earlyAudioBuffers,
            activeConnections: this.activeConnections,
            audioProcessor: this.audioProcessor,
            sessionMetrics: this.sessionMetrics
        });
    }
    /**
     * Get connection data for a session
     * @param callSid - Call/session ID
     * @returns Connection data or null
     */
    getConnectionData(callSid) {
        return this.activeConnections?.get(callSid) || null;
    }
    /**
     * Get audio settings
     */
    getAudioSettings() {
        return this.audioProcessor.getAudioSettings();
    }
    /**
     * Set audio settings
     */
    setAudioSettings(settings) {
        this.audioProcessor.updateAudioSettings(settings);
        return true;
    }
    /**
     * Get audio quality monitor
     */
    getAudioQualityMonitor() {
        return 'audioQualityMonitor' in audio_processor_1.AudioProcessor ? audio_processor_1.AudioProcessor.audioQualityMonitor : null;
    }
}
exports.SessionManager = SessionManager;
//# sourceMappingURL=session-manager.js.map