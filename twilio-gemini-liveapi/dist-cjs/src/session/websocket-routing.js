"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendAudioToGemini = sendAudioToGemini;
exports.sendBrowserAudioToGemini = sendBrowserAudioToGemini;
exports.setupTwilioReadinessCheck = setupTwilioReadinessCheck;
exports.bufferEarlyAudio = bufferEarlyAudio;
exports.processBufferedAudio = processBufferedAudio;
const logger_1 = require("../utils/logger");
const gemini_sender_1 = require("../audio/gemini-sender");
async function sendAudioToGemini(callSid, geminiSession, audioBuffer, deps) {
    const { audioProcessor, sessionMetrics, activeConnections, earlyAudioBuffers } = deps;
    try {
        logger_1.sessionLogger.info(`🔍 [${callSid}] sendAudioToGemini called - geminiSession: ${!!geminiSession}, audioBuffer: ${!!audioBuffer}, audioSize: ${audioBuffer?.length || 0}`);
        if (!geminiSession || !audioBuffer) {
            logger_1.sessionLogger.info(`⚠️ [${callSid}] sendAudioToGemini early return - missing geminiSession or audioBuffer`);
            return;
        }
        const connectionData = activeConnections?.get(callSid);
        if (!connectionData?.sessionReady) {
            logger_1.sessionLogger.info(`⚠️ [${callSid}] Session not ready for audio processing yet`);
            return;
        }
        if (!connectionData.fullyReady) {
            logger_1.sessionLogger.warn(`⚠️ [${callSid}] Session not fully ready, buffering audio packet`);
            bufferEarlyAudio(callSid, audioBuffer, earlyAudioBuffers);
            return;
        }
        if (connectionData.geminiSessionError) {
            logger_1.sessionLogger.error(`❌ [${callSid}] Session has error, skipping audio: ${connectionData.geminiSessionError}`);
            return;
        }
        const metrics = sessionMetrics.get(callSid);
        if (metrics) {
            metrics.messagesSent = (metrics.messagesSent || 0) + 1;
            metrics.lastActivity = Date.now();
        }
        const pcmBuffer = audioProcessor.convertUlawToPCM(audioBuffer, false);
        if (pcmBuffer.length === 0) {
            logger_1.sessionLogger.warn(`⚠️ [${callSid}] PCM conversion resulted in empty buffer - skipping audio send`);
            return;
        }
        await (0, gemini_sender_1.sendAudioBufferToGemini)(callSid, connectionData, pcmBuffer);
    }
    catch (error) {
        logger_1.sessionLogger.error(`❌ [${callSid}] Error sending audio to Gemini:`, error instanceof Error ? error : new Error(String(error)));
    }
}
async function sendBrowserAudioToGemini(callSid, geminiSession, base64Audio, deps) {
    const { sessionMetrics, activeConnections, audioProcessor } = deps;
    try {
        logger_1.sessionLogger.info(`🔍 [${callSid}] sendBrowserAudioToGemini called - geminiSession: ${!!geminiSession}, audioSize: ${base64Audio?.length || 0}`);
        if (!geminiSession || !base64Audio) {
            logger_1.sessionLogger.info(`⚠️ [${callSid}] sendBrowserAudioToGemini early return - missing geminiSession or audio`);
            return;
        }
        const connectionData = activeConnections?.get(callSid);
        if (!connectionData?.sessionReady) {
            logger_1.sessionLogger.info(`⚠️ [${callSid}] Session not ready for audio processing yet`);
            return;
        }
        const metrics = sessionMetrics.get(callSid);
        if (metrics) {
            metrics.messagesSent = (metrics.messagesSent || 0) + 1;
            metrics.lastActivity = Date.now();
        }
        const pcmBuffer = Buffer.from(base64Audio, 'base64');
        await (0, gemini_sender_1.sendAudioBufferToGemini)(callSid, connectionData, pcmBuffer);
    }
    catch (error) {
        logger_1.sessionLogger.error(`❌ [${callSid}] Error sending browser audio to Gemini:`, error instanceof Error ? error : new Error(String(error)));
    }
}
function setupTwilioReadinessCheck(callSid, connectionData, earlyAudioBuffers, activeConnections) {
    // Mark as ready immediately if both conditions are met
    if (connectionData.twilioConnected && connectionData.isSessionActive) {
        connectionData.fullyReady = true;
        logger_1.sessionLogger.info(`🎯 [${callSid}] Session now fully ready - Twilio connection established`);
        // Send initial greeting for inbound calls immediately
        if (connectionData.isIncomingCall && connectionData.geminiSession) {
            sendInitialGreeting(callSid, connectionData).catch(error => {
                logger_1.sessionLogger.error(`❌ [${callSid}] Error sending initial greeting:`, error);
            });
        }
        processBufferedAudio(callSid, { earlyAudioBuffers, activeConnections }, connectionData.geminiSession);
        return;
    }
    // If not ready immediately, set up event-based readiness (no polling)
    const checkReadiness = async () => {
        if (connectionData.twilioConnected && connectionData.isSessionActive && !connectionData.fullyReady) {
            connectionData.fullyReady = true;
            logger_1.sessionLogger.info(`🎯 [${callSid}] Session now fully ready - Twilio connection established`);
            if (connectionData.isIncomingCall && connectionData.geminiSession) {
                await sendInitialGreeting(callSid, connectionData);
            }
            processBufferedAudio(callSid, { earlyAudioBuffers, activeConnections }, connectionData.geminiSession);
        }
    };
    // Use immediate check with minimal fallback timeout
    process.nextTick(checkReadiness);
    // Minimal safety timeout (reduced from 10 seconds to 2 seconds)
    setTimeout(async () => {
        if (!connectionData.fullyReady && connectionData.isSessionActive) {
            logger_1.sessionLogger.info(`🎯 [${callSid}] Proceeding with session - marking as ready`);
            connectionData.fullyReady = true;
            if (connectionData.isIncomingCall && connectionData.geminiSession) {
                await sendInitialGreeting(callSid, connectionData);
            }
            processBufferedAudio(callSid, { earlyAudioBuffers, activeConnections }, connectionData.geminiSession);
        }
    }, 2000); // Reduced from 10 seconds to 2 seconds
}
async function sendInitialGreeting(callSid, connectionData) {
    try {
        // Only send greeting if no audio has been received yet
        if (connectionData.hasReceivedAudio) {
            logger_1.sessionLogger.debug(`[${callSid}] Skipping greeting - audio already received`);
            return;
        }
        logger_1.sessionLogger.info(`👋 [${callSid}] Sending initial greeting for inbound call`);
        // Send a text prompt to trigger the AI to speak first
        const greetingPrompt = "Please greet the caller warmly and ask how you can help them today.";
        // Use sendRealtimeInput with text encoded as base64
        await connectionData.geminiSession.sendRealtimeInput({
            media: {
                data: Buffer.from(greetingPrompt, 'utf-8').toString('base64'),
                mimeType: 'text/plain'
            }
        });
        logger_1.sessionLogger.info(`✅ [${callSid}] Initial greeting prompt sent successfully`);
    }
    catch (error) {
        logger_1.sessionLogger.error(`❌ [${callSid}] Error sending initial greeting:`, error instanceof Error ? error : new Error(String(error)));
    }
}
// Thread-safe audio buffer operations using atomic operations
const audioBufferLocks = new Map();
function bufferEarlyAudio(callSid, audioBuffer, earlyAudioBuffers) {
    // Use atomic operation to prevent race conditions
    const lockKey = `buffer_${callSid}`;
    const currentLock = audioBufferLocks.get(lockKey) || Promise.resolve();
    const newLock = currentLock.then(() => {
        if (!earlyAudioBuffers.has(callSid)) {
            earlyAudioBuffers.set(callSid, []);
        }
        const buffers = earlyAudioBuffers.get(callSid);
        buffers.push(audioBuffer);
        if (buffers.length > 50) {
            buffers.shift();
        }
        logger_1.sessionLogger.debug(`📦 [${callSid}] Buffered audio packet (${buffers.length} total)`);
    }).catch(error => {
        logger_1.sessionLogger.error(`❌ [${callSid}] Error in bufferEarlyAudio:`, error instanceof Error ? error : new Error(String(error)));
    });
    audioBufferLocks.set(lockKey, newLock);
    // Clean up completed locks to prevent memory leaks
    newLock.finally(() => {
        if (audioBufferLocks.get(lockKey) === newLock) {
            audioBufferLocks.delete(lockKey);
        }
    });
}
async function processBufferedAudio(callSid, deps, geminiSession) {
    const { earlyAudioBuffers, activeConnections, audioProcessor, sessionMetrics } = deps;
    // Use the same locking mechanism to prevent race conditions
    const lockKey = `buffer_${callSid}`;
    const currentLock = audioBufferLocks.get(lockKey) || Promise.resolve();
    const processLock = currentLock.then(async () => {
        const buffers = earlyAudioBuffers.get(callSid);
        if (!buffers || buffers.length === 0) {
            return;
        }
        logger_1.sessionLogger.info(`🔄 [${callSid}] Processing ${buffers.length} buffered audio packets`);
        const connectionData = activeConnections?.get(callSid);
        const session = geminiSession || connectionData?.geminiSession;
        if (!session) {
            logger_1.sessionLogger.warn(`⚠️ [${callSid}] No Gemini session for buffered audio processing`);
            earlyAudioBuffers.delete(callSid);
            return;
        }
        // Create a copy of buffers to process and clear the original to prevent new additions during processing
        const buffersToProcess = [...buffers];
        earlyAudioBuffers.delete(callSid);
        for (const buffer of buffersToProcess) {
            try {
                await sendAudioToGemini(callSid, session, buffer, {
                    audioProcessor: audioProcessor,
                    sessionMetrics: sessionMetrics,
                    activeConnections,
                    earlyAudioBuffers
                });
            }
            catch (error) {
                logger_1.sessionLogger.error(`❌ [${callSid}] Error processing buffered audio:`, error instanceof Error ? error : new Error(String(error)));
                break;
            }
        }
        logger_1.sessionLogger.info(`✅ [${callSid}] Finished processing ${buffersToProcess.length} buffered audio packets`);
    }).catch(error => {
        logger_1.sessionLogger.error(`❌ [${callSid}] Error in processBufferedAudio:`, error instanceof Error ? error : new Error(String(error)));
    });
    audioBufferLocks.set(lockKey, processLock);
    // Clean up completed locks to prevent memory leaks
    processLock.finally(() => {
        if (audioBufferLocks.get(lockKey) === processLock) {
            audioBufferLocks.delete(lockKey);
        }
    });
}
//# sourceMappingURL=websocket-routing.js.map