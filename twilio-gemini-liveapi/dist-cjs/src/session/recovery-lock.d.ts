/**
 * Recovery Lock Manager
 * Provides thread-safe locking mechanism for session recovery operations
 */
export declare class RecoveryLockManager {
    private locks;
    private pendingLockRequests;
    private readonly lockTimeout;
    constructor(lockTimeout?: number);
    /**
     * Attempts to acquire a lock for the given key
     * Returns true if lock acquired, false if already locked
     */
    acquireLock(key: string): Promise<boolean>;
    /**
     * Releases a lock and processes any pending requests
     */
    releaseLock(key: string): void;
    /**
     * Checks if a lock is currently held
     */
    isLocked(key: string): boolean;
    /**
     * Gets the current lock status
     */
    getLockStatus(): {
        activeLocks: number;
        pendingRequests: number;
        locks: Array<{
            key: string;
            age: number;
            hasPending: boolean;
        }>;
    };
    /**
     * Cleans up all locks and pending requests
     */
    cleanup(): void;
}
export declare const recoveryLockManager: RecoveryLockManager;
//# sourceMappingURL=recovery-lock.d.ts.map