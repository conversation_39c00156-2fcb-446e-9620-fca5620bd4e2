import { ContextManager } from './context-manager';
import { ConnectionData } from '../types/global';
interface SummaryStatus {
    inProgress: boolean;
    hasTimeout: boolean;
    timestamp: number;
}
export declare class SessionSummaryManager {
    private summaryTimeouts;
    private summaryInProgress;
    private defaultSummaryTimeout;
    private summaryPrompts;
    constructor();
    /**
     * Request session summary from AI with flow-specific prompts
     * @param callSid - Call/session ID
     * @param connectionData - Connection data with Gemini session
     * @param contextManager - Context manager for saving summary state
     * @returns Success status
     */
    requestSummary(callSid: string, connectionData: ConnectionData, contextManager: ContextManager): Promise<boolean>;
    /**
     * Determine the flow type from connection data
     * @param connectionData - Connection data
     * @returns Flow type
     */
    private determineFlowType;
    /**
     * Get appropriate summary prompt for flow type
     * @param flowType - Flow type
     * @returns Summary prompt
     */
    private getSummaryPrompt;
    /**
     * Handle summary response from AI
     * @param callSid - Call/session ID
     * @param summaryText - Summary text from AI
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     */
    handleSummaryResponse(callSid: string, summaryText: string, connectionData: ConnectionData, contextManager: ContextManager): Promise<void>;
    /**
     * Generate fallback summary from conversation data with flow-specific formatting
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     * @returns Success status
 */
    private generateFallbackSummary;
    /**
     * Handle summary timeout
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     */
    private handleSummaryTimeout;
    /**
     * Clear summary timeout
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    private clearSummaryTimeout;
    /**
     * Save summary information to file with flow type support
     * @param callSid - Call/session ID
     * @param rawSummaryText - Raw summary text
     * @param defaultSentiment - Default sentiment
     * @param status - Call status
     * @param targetName - Target name
     * @param targetPhoneNumber - Target phone number
     * @param flowType - Flow type (outbound_call, inbound_call, etc.)
     */
    saveSummaryInfo(callSid: string, rawSummaryText: string, defaultSentiment?: 'positive' | 'negative' | 'neutral', status?: string, targetName?: string | null, targetPhoneNumber?: string | null, flowType?: string | null): Promise<void>;
    /**
     * Check if summary is in progress
     * @param callSid - Call/session ID
     * @returns True if summary is in progress
     */
    isSummaryInProgress(callSid: string): boolean;
    /**
     * Get summary status
     * @param callSid - Call/session ID
     * @returns Summary status
     */
    getSummaryStatus(callSid: string): SummaryStatus;
    /**
     * Clean up summary tracking for a session
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    cleanupSummary(callSid: string, connectionData: ConnectionData): void;
    /**
     * Clean up all summary resources (for shutdown)
     */
    cleanup(): void;
}
export {};
//# sourceMappingURL=summary-manager.d.ts.map