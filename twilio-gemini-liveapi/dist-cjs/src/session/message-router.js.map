{"version": 3, "file": "message-router.js", "sourceRoot": "", "sources": ["../../../src/session/message-router.ts"], "names": [], "mappings": ";;AAkBA,gDA0GC;AA5HD,gEAAyD;AAEzD,4CAAgD;AAgBzC,KAAK,UAAU,kBAAkB,CACpC,OAAe,EACf,OAA0B,EAC1B,cAAsC,EACtC,IAAuB;IAEvB,MAAM,EACF,cAAc,EACd,iBAAiB,EACjB,cAAc,EACd,cAAc,GAAG,+BAAY,EAChC,GAAG,IAAI,CAAC;IAET,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,OAAO,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;QAC1E,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YACrC,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,sCAAsC,CAAC,CAAC;YACzE,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,cAAc,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/D,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;QAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/C,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,yCAAyC,CAAC,CAAC;YAChF,CAAC;iBAAM,CAAC;gBACJ,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,8BAA8B,EAAE;oBAC7D,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;oBAChC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,WAAW,EAAE,cAAc,CAAC,WAAW;oBACvC,YAAY,EAAE,cAAc,CAAC,YAAY;oBACzC,sBAAsB,EAAE,cAAc,CAAC,sBAAsB;oBAC7D,aAAa,EAAE,cAAc,CAAC,aAAa;iBAC9C,CAAC,CAAC;gBAEH,MAAM,KAAK,GAAG,iBAAiB,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,cAAc,CAAC;gBAEhE,+DAA+D;gBAC/D,MAAM,YAAY,GAAI,KAAa,CAAC,YAAY,CAAC;gBACjD,MAAM,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;gBACpE,MAAM,sBAAsB,GAAG,KAAK,CAAC,sBAAsB,CAAC;gBAE5D,oDAAoD;gBACpD,sBAAa,CAAC,KAAK,CAAC,OAAO,OAAO,+BAA+B,EAAE;oBAC/D,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,cAAc,EAAE,KAAK,CAAC,cAAc;oBACpC,YAAY,EAAE,YAAY;oBAC1B,sBAAsB,EAAE,sBAAsB;oBAC9C,WAAW,EAAE,WAAW;iBAC3B,CAAC,CAAC;gBAEH,sEAAsE;gBACtE,IAAI,CAAC,YAAY,IAAI,CAAC,WAAW,EAAE,CAAC;oBAChC,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,4CAA4C,EAAE;wBAC3E,YAAY,EAAE,YAAY;wBAC1B,WAAW,EAAE,WAAW;wBACxB,sBAAsB,EAAE,sBAAsB;qBACjD,CAAC,CAAC;gBACP,CAAC;gBAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBAC1B,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,iDAAiD,CAAC,CAAC;gBACxF,CAAC;gBAED,MAAM,gBAAgB,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;gBAExF,IAAI,gBAAgB,EAAE,CAAC;oBACnB,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,+BAA+B,CAAC,CAAC;gBACtE,CAAC;qBAAM,CAAC;oBACJ,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,4CAA4C,EAAE;wBAC3E,QAAQ,EAAE,CAAC,CAAC,QAAQ;wBACpB,iBAAiB,EAAE,CAAC,CAAC,KAAK;wBAC1B,iBAAiB,EAAE,CAAC,CAAC,cAAc;qBACtC,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QAChE,IAAI,IAAI,EAAE,CAAC;YACP,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3C,cAAc,CAAC,gBAAgB,GAAG,CAAC,CAAC;YACpC,cAAc,CAAC,iBAAiB,GAAG,MAAM,CAAC;YAE1C,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;gBACjC,MAAM,YAAY,GAAG,GAAG,CAAC;gBACzB,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACjG,IAAI,cAAc,CAAC,eAAe,CAAC,MAAM,GAAG,YAAY,EAAE,CAAC;oBACvD,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,eAAe,CAAC,MAAM,GAAG,YAAY,CAAC,CAAC;gBACnG,CAAC;YACL,CAAC;YAED,IAAI,cAAc,CAAC,gBAAgB,EAAE,CAAC;gBAClC,cAAc,CAAC,WAAW,GAAG,CAAC,cAAc,CAAC,WAAW,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;YAC3E,CAAC;QACL,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,oCAAoC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACtI,CAAC;AACL,CAAC"}