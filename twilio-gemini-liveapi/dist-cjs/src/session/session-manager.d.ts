import { SessionMetrics } from './metrics';
import { ConnectionData, GeminiSession, GeminiClient } from '../types/global';
import { SessionConfig, ExtendedConnectionData } from '../types/websocket';
import { ContextManager } from './context-manager';
export type { SessionMetrics } from './metrics';
export { BoundedMap, BoundedSet } from './metrics';
export declare class SessionManager {
    private contextManager;
    private geminiClient;
    private recoveryInProgress;
    private audioProcessor;
    private sessionMetrics;
    private activeConnections;
    private earlyAudioBuffers;
    private cleanupLocks;
    constructor(contextManager: ContextManager, geminiClient: GeminiClient, activeConnections?: Map<string, ConnectionData> | null);
    createGeminiSession(callSid: string, config: SessionConfig, connectionData: ConnectionData): Promise<GeminiSession | null>;
    sendTextToGemini(sessionId: string, geminiSession: GeminiSession, text: string): Promise<void>;
    sendTurnComplete(sessionId: string, geminiSession: GeminiSession): Promise<void>;
    sendAudioToGemini(callSid: string, geminiSession: GeminiSession, audioBuffer: Buffer): Promise<void>;
    sendBrowserAudioToGemini(callSid: string, geminiSession: GeminiSession, base64Audio: string): Promise<void>;
    recoverSession(callSid: string, reason: string): Promise<void>;
    generateSummary(callSid: string, connectionData: ExtendedConnectionData, summaryPrompt: string): Promise<boolean>;
    getSessionMetrics(callSid: string): SessionMetrics | null;
    cleanupSession(callSid: string): Promise<void>;
    private performCleanup;
    /**
     * Set up a check to mark session as fully ready when Twilio connects
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    private setupTwilioReadinessCheck;
    /**
     * Buffer early audio packets when session is not fully ready
     * @param callSid - Call/session ID
     * @param audioBuffer - Audio buffer to store
     */
    private bufferEarlyAudio;
    /**
     * Process buffered audio packets when session becomes ready
     * @param callSid - Call/session ID
     */
    private processBufferedAudio;
    /**
     * Get connection data for a session
     * @param callSid - Call/session ID
     * @returns Connection data or null
     */
    private getConnectionData;
    /**
     * Get audio settings
     */
    getAudioSettings(): import("../audio/audio-types").AudioSettings;
    /**
     * Set audio settings
     */
    setAudioSettings(settings: any): boolean;
    /**
     * Get audio quality monitor
     */
    getAudioQualityMonitor(): any;
}
//# sourceMappingURL=session-manager.d.ts.map