{"version": 3, "file": "websocket-routing.js", "sourceRoot": "", "sources": ["../../../src/session/websocket-routing.ts"], "names": [], "mappings": ";;AAgBA,8CAyCC;AAED,4DA4BC;AAED,8DA+CC;AAgCD,4CA2BC;AAED,oDAwDC;AA3PD,4CAAgD;AAChD,0DAAiE;AAa1D,KAAK,UAAU,iBAAiB,CACnC,OAAe,EACf,aAAmC,EACnC,WAAmB,EACnB,IAAiB;IAEjB,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC;IACtF,IAAI,CAAC;QACD,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,+CAA+C,CAAC,CAAC,aAAa,kBAAkB,CAAC,CAAC,WAAW,gBAAgB,WAAW,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;QAC1K,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,yEAAyE,CAAC,CAAC;YAC5G,OAAO;QACX,CAAC;QACD,MAAM,cAAc,GAAG,iBAAiB,EAAE,GAAG,CAAC,OAAO,CAAQ,CAAC;QAC9D,IAAI,CAAC,cAAc,EAAE,YAAY,EAAE,CAAC;YAChC,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,8CAA8C,CAAC,CAAC;YACjF,OAAO;QACX,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YAC7B,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,mDAAmD,CAAC,CAAC;YACtF,gBAAgB,CAAC,OAAO,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAC1D,OAAO;QACX,CAAC;QACD,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;YACpC,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,wCAAwC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAC9G,OAAO;QACX,CAAC;QACD,MAAM,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,YAAY,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvD,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;QACD,MAAM,SAAS,GAAG,cAAc,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,iEAAiE,CAAC,CAAC;YACpG,OAAO;QACX,CAAC;QACD,MAAM,IAAA,uCAAuB,EAAC,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;IACtE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,kCAAkC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpI,CAAC;AACL,CAAC;AAEM,KAAK,UAAU,wBAAwB,CAC1C,OAAe,EACf,aAAmC,EACnC,WAAmB,EACnB,IAAiB;IAEjB,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;IACnE,IAAI,CAAC;QACD,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,sDAAsD,CAAC,CAAC,aAAa,gBAAgB,WAAW,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;QAClJ,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,0EAA0E,CAAC,CAAC;YAC7G,OAAO;QACX,CAAC;QACD,MAAM,cAAc,GAAG,iBAAiB,EAAE,GAAG,CAAC,OAAO,CAAQ,CAAC;QAC9D,IAAI,CAAC,cAAc,EAAE,YAAY,EAAE,CAAC;YAChC,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,8CAA8C,CAAC,CAAC;YACjF,OAAO;QACX,CAAC;QACD,MAAM,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,YAAY,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvD,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;QACD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QACrD,MAAM,IAAA,uCAAuB,EAAC,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;IACtE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,0CAA0C,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5I,CAAC;AACL,CAAC;AAED,SAAgB,yBAAyB,CAAC,OAAe,EAAE,cAAmB,EAAE,iBAAwC,EAAE,iBAAsD;IAC5K,uDAAuD;IACvD,IAAI,cAAc,CAAC,eAAe,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;QACnE,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC;QACjC,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,2DAA2D,CAAC,CAAC;QAE9F,sDAAsD;QACtD,IAAI,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;YAChE,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACvD,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC;QACP,CAAC;QAED,oBAAoB,CAAC,OAAO,EAAE,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;QACtG,OAAO;IACX,CAAC;IAED,sEAAsE;IACtE,MAAM,cAAc,GAAG,KAAK,IAAI,EAAE;QAC9B,IAAI,cAAc,CAAC,eAAe,IAAI,cAAc,CAAC,eAAe,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACjG,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC;YACjC,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,2DAA2D,CAAC,CAAC;YAE9F,IAAI,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;gBAChE,MAAM,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YACvD,CAAC;YAED,oBAAoB,CAAC,OAAO,EAAE,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;QAC1G,CAAC;IACL,CAAC,CAAC;IAEF,oDAAoD;IACpD,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAEjC,gEAAgE;IAChE,UAAU,CAAC,KAAK,IAAI,EAAE;QAClB,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;YAC/D,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,8CAA8C,CAAC,CAAC;YACjF,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC;YAEjC,IAAI,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;gBAChE,MAAM,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YACvD,CAAC;YAED,oBAAoB,CAAC,OAAO,EAAE,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;QAC1G,CAAC;IACL,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,uCAAuC;AACrD,CAAC;AAED,KAAK,UAAU,mBAAmB,CAAC,OAAe,EAAE,cAAmB;IACnE,IAAI,CAAC;QACD,uDAAuD;QACvD,IAAI,cAAc,CAAC,gBAAgB,EAAE,CAAC;YAClC,sBAAa,CAAC,KAAK,CAAC,IAAI,OAAO,8CAA8C,CAAC,CAAC;YAC/E,OAAO;QACX,CAAC;QAED,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,6CAA6C,CAAC,CAAC;QAEhF,sDAAsD;QACtD,MAAM,cAAc,GAAG,qEAAqE,CAAC;QAE7F,oDAAoD;QACpD,MAAM,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACjD,KAAK,EAAE;gBACH,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC7D,QAAQ,EAAE,YAAY;aACzB;SACJ,CAAC,CAAC;QAEH,sBAAa,CAAC,IAAI,CAAC,MAAM,OAAO,6CAA6C,CAAC,CAAC;IACnF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,mCAAmC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrI,CAAC;AACL,CAAC;AAED,8DAA8D;AAC9D,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAyB,CAAC;AAE1D,SAAgB,gBAAgB,CAAC,OAAe,EAAE,WAAmB,EAAE,iBAAwC;IAC3G,kDAAkD;IAClD,MAAM,OAAO,GAAG,UAAU,OAAO,EAAE,CAAC;IACpC,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;IAEvE,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE;QAClC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAClC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACvC,CAAC;QACD,MAAM,OAAO,GAAG,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1B,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACtB,OAAO,CAAC,KAAK,EAAE,CAAC;QACpB,CAAC;QACD,sBAAa,CAAC,KAAK,CAAC,OAAO,OAAO,4BAA4B,OAAO,CAAC,MAAM,SAAS,CAAC,CAAC;IAC3F,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,8BAA8B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChI,CAAC,CAAC,CAAC;IAEH,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAEvC,mDAAmD;IACnD,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE;QACjB,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,OAAO,EAAE,CAAC;YAC5C,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAEM,KAAK,UAAU,oBAAoB,CACtC,OAAe,EACf,IAA0L,EAC1L,aAAoC;IAEpC,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;IAEtF,4DAA4D;IAC5D,MAAM,OAAO,GAAG,UAAU,OAAO,EAAE,CAAC;IACpC,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;IAEvE,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;QAC5C,MAAM,OAAO,GAAG,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO;QACX,CAAC;QAED,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,gBAAgB,OAAO,CAAC,MAAM,yBAAyB,CAAC,CAAC;QAC1F,MAAM,cAAc,GAAG,iBAAiB,EAAE,GAAG,CAAC,OAAO,CAAQ,CAAC;QAC9D,MAAM,OAAO,GAAG,aAAa,IAAI,cAAc,EAAE,aAAa,CAAC;QAC/D,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,sBAAa,CAAC,IAAI,CAAC,OAAO,OAAO,mDAAmD,CAAC,CAAC;YACtF,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAClC,OAAO;QACX,CAAC;QAED,wGAAwG;QACxG,MAAM,gBAAgB,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;QACtC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAElC,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,IAAI,CAAC;gBACD,MAAM,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;oBAC9C,cAAc,EAAE,cAAe;oBAC/B,cAAc,EAAE,cAAe;oBAC/B,iBAAiB;oBACjB,iBAAiB;iBACpB,CAAC,CAAC;YACP,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,oCAAoC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAClI,MAAM;YACV,CAAC;QACL,CAAC;QACD,sBAAa,CAAC,IAAI,CAAC,MAAM,OAAO,yBAAyB,gBAAgB,CAAC,MAAM,yBAAyB,CAAC,CAAC;IAC/G,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,sBAAa,CAAC,KAAK,CAAC,MAAM,OAAO,kCAAkC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpI,CAAC,CAAC,CAAC;IAEH,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IAE3C,mDAAmD;IACnD,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QACrB,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;YAChD,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC"}