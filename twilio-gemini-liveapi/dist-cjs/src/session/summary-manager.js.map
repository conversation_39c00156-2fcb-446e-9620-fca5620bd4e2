{"version": 3, "file": "summary-manager.js", "sourceRoot": "", "sources": ["../../../src/session/summary-manager.ts"], "names": [], "mappings": ";;;;;;AAAA,2EAA2E;AAC3E,0CAAiE;AACjE,gDAAwB;AACxB,6BAAoC;AAIpC,2DAA2D;AAC3D,4CAAgD;AAEhD,MAAM,UAAU,GAAG,IAAA,mBAAa,EAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClD,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAkB3C,MAAa,qBAAqB;IACtB,eAAe,CAA8B;IAC7C,iBAAiB,CAAc;IAC/B,qBAAqB,CAAS;IAC9B,cAAc,CAAiB;IAEvC;QACI,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,yBAAyB;QAC3D,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,gDAAgD;QACpF,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,CAAC,iEAAiE;QAErG,0EAA0E;QAC1E,IAAI,CAAC,cAAc,GAAG;YAClB,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,EAAE;YAC1D,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,EAAE;YACzD,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,EAAE;YAC1D,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,EAAE;YACzD,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,EAAE;SACvD,CAAC;IACN,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,cAA8B,EAAE,cAA8B;QAChG,MAAM,iBAAiB,GAAG,cAAwC,CAAC;QAEnE,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,gBAAgB,IAAI,iBAAiB,CAAC,eAAe,EAAE,CAAC;YAChG,sBAAa,CAAC,KAAK,CAAC,IAAI,OAAO,8EAA8E,CAAC,CAAC;YAC/G,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,sBAAa,CAAC,KAAK,CAAC,IAAI,OAAO,0CAA0C,CAAC,CAAC;YAC3E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,qDAAqD;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAEtD,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,gBAAgB,QAAQ,0BAA0B,CAAC,CAAC;QAClF,iBAAiB,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC1C,iBAAiB,CAAC,WAAW,GAAG,EAAE,CAAC;QACnC,iBAAiB,CAAC,eAAe,GAAG,QAAQ,CAAC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEpC,IAAI,CAAC;YACD,0CAA0C;YAC1C,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC;gBACnC,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,gDAAgD,CAAC,CAAC;gBAChF,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAC1F,CAAC;YAED,qCAAqC;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpC,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,+CAA+C,CAAC,CAAC;gBAC/E,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAChF,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAE/B,iBAAiB,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAC/C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAE7C,yEAAyE;YACzE,qDAAqD;YACrD,MAAM,iBAAiB,CAAC,aAAa,CAAC,iBAAiB,CAAC;gBACpD,KAAK,EAAE;oBACH,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAC5D,QAAQ,EAAE,YAAY;iBACzB;aACJ,CAAC,CAAC;YAEH,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,KAAK,QAAQ,0CAA0C,CAAC,CAAC;YACvF,OAAO,IAAI,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,sBAAa,CAAC,KAAK,CAAC,IAAI,OAAO,oCAAoC,EAAE,KAAc,CAAC,CAAC;YACrF,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACvC,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAC1F,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,iBAAiB,CAAC,cAAsC;QAC5D,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC1B,OAAO,cAAc,CAAC,QAAoB,CAAC;QAC/C,CAAC;QAED,IAAI,cAAc,CAAC,WAAW,KAAK,YAAY,EAAE,CAAC;YAC9C,OAAO,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC;QAC5E,CAAC;QAED,IAAI,cAAc,CAAC,WAAW,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC;QAC5E,CAAC;QAED,yBAAyB;QACzB,IAAI,cAAc,CAAC,UAAU,EAAE,CAAC;YAC5B,OAAO,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC;QAC5E,CAAC;QAED,OAAO,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACK,gBAAgB,CAAC,QAAkB;QACvC,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;IACxE,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,qBAAqB,CAAC,OAAe,EAAE,WAAmB,EAAE,cAA8B,EAAE,cAA8B;QAC5H,MAAM,iBAAiB,GAAG,cAAwC,CAAC;QAEnE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,kDAAkD,CAAC,CAAC;YAClF,OAAO;QACX,CAAC;QAED,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,+BAA+B,WAAW,CAAC,MAAM,cAAc,CAAC,CAAC;QAE/F,gBAAgB;QAChB,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QAErD,eAAe;QACf,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EACnE,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAEvE,mBAAmB;QACnB,iBAAiB,CAAC,eAAe,GAAG,IAAI,CAAC;QACzC,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC;QAC5C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,iBAAiB;QACjB,IAAI,cAAc,EAAE,CAAC;YACjB,MAAM,OAAO,GAAG,cAAc,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAC1D,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,CAAC,iBAAiB,CAAC,eAAe,GAAG,IAAI,CAAC;gBACjD,OAAO,CAAC,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC;gBACpD,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC;QACL,CAAC;QAED,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,4CAA4C,CAAC,CAAC;IAChF,CAAC;IAED;;;;;;GAMD;IACS,KAAK,CAAC,uBAAuB,CAAC,OAAe,EAAE,cAAsC,EAAE,cAA8B;QACzH,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,sDAAsD,CAAC,CAAC;QAEtF,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,IAAA,wCAAoB,EAAC,cAAc,CAAC,eAAe,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;YAEnF,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAC/D,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YAE3E,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;YACtC,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC;YACrC,cAAc,CAAC,eAAe,GAAG,QAAQ,CAAC;YAC1C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAEvC,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,KAAK,QAAQ,oDAAoD,CAAC,CAAC;YACjG,OAAO,IAAI,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,sBAAa,CAAC,KAAK,CAAC,IAAI,OAAO,qCAAqC,EAAE,KAAc,CAAC,CAAC;YACtF,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACvC,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,cAAsC,EAAE,cAA8B;QACtH,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,gCAAgC,CAAC,CAAC;QAEhE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAElD,IAAI,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,mCAAmC;YACnC,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,iDAAiD,CAAC,CAAC;YACjF,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,cAAc,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAChF,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACJ,4BAA4B;YAC5B,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;QAChF,CAAC;QAED,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACK,mBAAmB,CAAC,OAAe,EAAE,cAAsC;QAC/E,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,SAAS,EAAE,CAAC;YACZ,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,cAAc,IAAI,cAAc,CAAC,gBAAgB,EAAE,CAAC;YACpD,YAAY,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAC9C,cAAc,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,cAAsB,EAAE,mBAAwD,SAAS,EAC1G,SAAiB,WAAW,EAAE,aAA4B,IAAI,EAAE,oBAAmC,IAAI,EACvG,WAA0B,IAAI;QAChD,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;QACjD,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,YAAY,CAAC,CAAC;QAEhE,sBAAa,CAAC,IAAI,CAAC,IAAI,OAAO,kCAAkC,MAAM,aAAa,UAAU,EAAE,CAAC,CAAC;QACjG,sBAAa,CAAC,KAAK,CAAC,IAAI,OAAO,oBAAoB,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,iBAAiB,cAAc,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;QAErI,IAAI,OAAO,GAAG,kDAAkD,CAAC;QACjE,IAAI,SAAS,GAAwC,gBAAgB,CAAC;QAEtE,8BAA8B;QAC9B,IAAI,cAAc,IAAI,MAAM,KAAK,YAAY,IAAI,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnI,MAAM,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;YACzF,IAAI,cAAc,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtC,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,EAAsB,CAAC;gBAChE,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,2CAA2C,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBACzF,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,uBAAuB,SAAS,EAAE,CAAC,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACJ,OAAO,GAAG,cAAc,CAAC,IAAI,EAAE,CAAC;YACpC,CAAC;QACL,CAAC;aAAM,IAAI,cAAc,EAAE,CAAC;YACxB,OAAO,GAAG,cAAc,CAAC;QAC7B,CAAC;aAAM,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACtE,OAAO,GAAG,2BAA2B,MAAM,yBAAyB,CAAC;YACrE,SAAS,GAAG,SAAS,CAAC;QAC1B,CAAC;aAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YAC9B,OAAO,GAAG,+BAA+B,CAAC;YAC1C,SAAS,GAAG,SAAS,CAAC;QAC1B,CAAC;aAAM,IAAI,MAAM,KAAK,WAAW,IAAI,UAAU,EAAE,CAAC;YAC9C,OAAO,GAAG,uBAAuB,UAAU,wFAAwF,CAAC;YACpI,SAAS,GAAG,SAAS,CAAC;QAC1B,CAAC;QAED,MAAM,WAAW,GAAG;YAChB,OAAO,EAAE,OAAO;YAChB,YAAY,EAAE,OAAO;YACrB,kBAAkB,EAAE,SAAS;YAC7B,MAAM,EAAE,MAAM;YACd,UAAU,EAAE,UAAU,IAAI,SAAS;YACnC,iBAAiB,EAAE,iBAAiB,IAAI,SAAS;YACjD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC;QAEF,IAAI,CAAC;YACD,+BAA+B;YAC/B,MAAM,IAAA,gBAAK,EAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE1C,kCAAkC;YAClC,IAAI,YAAY,GAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACD,MAAM,eAAe,GAAG,MAAM,IAAA,mBAAQ,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAC7D,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,SAAc,EAAE,CAAC;gBACtB,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAAA,MAAM,SAAS,CAAC;gBAAA,CAAC;YACvD,CAAC;YAED,kEAAkE;YAClE,MAAM,SAAS,GAAG,EAAE,GAAG,YAAY,EAAE,GAAG,WAAW,EAAE,CAAC;YAEtD,MAAM,IAAA,oBAAS,EAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,2BAA2B,YAAY,EAAE,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,MAAM,OAAO,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,mBAAmB,CAAC,OAAe;QAC/B,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,OAAe;QAC5B,OAAO;YACH,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC/C,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC;YAC7C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACN,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAC,OAAe,EAAE,cAA8B;QAC1D,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAwC,CAAC,CAAC;QAC5E,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,sBAAa,CAAC,KAAK,CAAC,IAAI,OAAO,+BAA+B,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,OAAO;QACH,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;QAElD,6BAA6B;QAC7B,KAAK,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9D,YAAY,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,sBAAa,CAAC,IAAI,CAAC,2BAA2B,YAAY,iBAAiB,aAAa,wBAAwB,CAAC,CAAC;IACtH,CAAC;CACJ;AA/WD,sDA+WC"}