/**
 * Test Utilities for Twilio Gemini Live API
 *
 * Provides comprehensive testing infrastructure for:
 * - All 4 flows: outbound/inbound × twilio/browser
 * - LLM integration testing with mocks
 * - Audio processing validation
 * - Session management testing
 * - Error scenario simulation
 */
import { EventEmitter } from 'events';
interface MockGeminiOptions {
    responses?: Array<{
        audioData?: ArrayBuffer;
    }>;
}
interface MockTwilioOptions {
    simulateErrors?: boolean;
}
interface TwilioCall {
    sid: string;
    to: string;
    from: string;
    url: string;
    status: string;
    startTime: Date;
    endTime?: Date;
    duration: number | null;
}
interface WebhookData {
    CallSid: string;
    CallStatus: string;
    From?: string;
    To?: string;
}
interface WebSocketMessage {
    timestamp: number;
    data: any;
}
interface SessionData {
    id: string;
    type: 'twilio' | 'browser';
    status: string;
    startTime: Date;
    callSid?: string;
    from?: string;
    to?: string;
    direction: 'outbound' | 'inbound';
    campaignId: number;
    geminiSession: any;
    websocket: any;
    audioMetrics: {
        packetsReceived?: number;
        packetsProcessed?: number;
        samplesReceived?: number;
        samplesProcessed?: number;
        audioQuality: string;
    };
}
interface PerformanceMeasurement {
    startTime: number;
    endTime: number | null;
    duration: number | null;
}
/**
 * Mock Gemini API responses for deterministic testing
 */
export declare class MockGeminiClient extends EventEmitter {
    private responses;
    private responseIndex;
    connected: boolean;
    sessionId: string;
    constructor(options?: MockGeminiOptions);
    connect(): Promise<string>;
    disconnect(): Promise<void>;
    sendRealtimeInput(input: any): void;
}
/**
 * Mock Twilio webhooks and API calls
 */
export declare class MockTwilioClient {
    private calls;
    private webhookCallbacks;
    private simulateErrors;
    constructor(options?: MockTwilioOptions);
    makeCall(to: string, from: string, url: string): Promise<TwilioCall>;
    endCall(callSid: string): Promise<void>;
    triggerWebhook(callSid: string, status: string): void;
    onWebhook(callSid: string, callback: (data: WebhookData) => void): void;
    getCall(callSid: string): TwilioCall | undefined;
}
/**
 * Audio test data generator
 */
export declare class AudioTestData {
    static generatePCM16(durationMs?: number, frequency?: number): ArrayBuffer;
    static generateMuLaw(durationMs?: number): ArrayBuffer;
    static generateBase64Audio(format?: 'pcm16' | 'mulaw', durationMs?: number): string;
}
/**
 * WebSocket test utilities
 */
export declare class MockWebSocket extends EventEmitter {
    url: string;
    readyState: number;
    private messages;
    constructor(url: string);
    send(data: string | Buffer): void;
    close(): void;
    getMessages(): WebSocketMessage[];
    getLastMessage(): WebSocketMessage | undefined;
}
/**
 * Test session factory
 */
export declare class TestSessionFactory {
    static createTwilioSession(callSid?: string | null): SessionData;
    static createBrowserSession(sessionId?: string | null): SessionData;
    static createInboundSession(type?: 'twilio' | 'browser'): SessionData;
}
/**
 * Performance measurement utilities
 */
export declare class TestPerformanceMonitor {
    private measurements;
    constructor();
    start(operation: string): void;
    end(operation: string): number | null;
    getDuration(operation: string): number | null;
    getAllMeasurements(): Record<string, PerformanceMeasurement>;
    reset(): void;
}
/**
 * Error simulation utilities
 */
export declare class ErrorSimulator {
    static networkError(): Error;
    static geminiApiError(): Error;
    static twilioWebhookError(): Error;
    static audioCorruptionError(): Error;
    static sessionTimeoutError(): Error;
}
/**
 * Test data factories
 */
export declare class TestDataFactory {
    static createCampaignScript(id?: number, type?: 'outbound' | 'inbound'): {
        id: number;
        type: "outbound" | "inbound";
        campaign: string;
        agentPersona: string;
        script: string;
        transferData: {
            transferNumber: string;
            agentName: string;
        };
    };
    static createCallResult(callSid: string, status?: string): {
        callSid: string;
        status: string;
        startTime: Date;
        endTime: Date | null;
        duration: number | null;
        transcript: string;
        outcome: string;
        campaignId: number;
    };
    static createAudioQualityMetrics(): {
        sampleRate: number;
        bitDepth: number;
        channels: number;
        latency: number;
        jitter: number;
        packetLoss: number;
        snr: number;
    };
}
/**
 * Test assertion helpers
 */
export declare class TestAssertions {
    static assertSessionActive(session: SessionData | null | undefined): void;
    static assertAudioDataValid(audioData: ArrayBuffer | null | undefined, format?: 'pcm16' | 'mulaw'): void;
    static assertWebSocketMessage(message: {
        data?: string;
    } | null, expectedType: string): any;
    static assertPerformanceWithinBounds(duration: number, maxMs: number): void;
    static assertCallSidFormat(callSid: string | null | undefined): void;
}
/**
 * Integration test helpers
 */
export declare class IntegrationTestHelpers {
    static waitForCondition(condition: () => boolean | Promise<boolean>, timeoutMs?: number, intervalMs?: number): Promise<boolean>;
    static simulateCall(mockTwilio: MockTwilioClient, mockGemini: MockGeminiClient, campaignId?: number): Promise<TwilioCall>;
    static createTestServer(port?: number): {
        start: () => Promise<{
            port: number;
        }>;
        stop: () => Promise<void>;
        getUrl: () => string;
    };
}
export {};
//# sourceMappingURL=test-utils.d.ts.map