{"version": 3, "file": "response-formatter.js", "sourceRoot": "", "sources": ["../../../src/utils/response-formatter.ts"], "names": [], "mappings": ";AAAA,sCAAsC;AACtC,kFAAkF;;AA+BlF,sDAYC;AAKD,kDAeC;AAKD,sEAYC;AAKD,0DAqBC;AAKD,wCA0CC;AAKD,kEAQC;AAKD,oDAgBC;AA/JD;;GAEG;AACH,SAAgB,qBAAqB,CACnC,IAAO,EACP,OAAgB,EAChB,SAAkB;IAElB,OAAO;QACL,OAAO,EAAE,IAAI;QACb,IAAI;QACJ,OAAO;QACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS;KACV,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CACjC,KAAqB,EACrB,IAAa,EACb,SAAkB,EAClB,UAAmB;IAEnB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;IAEpE,OAAO;QACL,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,YAAY;QACnB,IAAI,EAAE,IAAI,IAAI,gBAAgB;QAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS;KACV,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,6BAA6B,CAC3C,MAAyB,EACzB,SAAkB;IAElB,OAAO;QACL,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;QACnD,IAAI,EAAE,kBAAkB;QACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS;KACV,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CACrC,IAAS,EACT,IAAY,EACZ,KAAa,EACb,KAAa,EACb,OAAgB,EAChB,SAAkB;IAElB,OAAO;QACL,OAAO,EAAE,IAAI;QACb,IAAI;QACJ,OAAO;QACP,UAAU,EAAE;YACV,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACrC;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS;KACV,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,cAAc,CAC5B,KAAU,EACV,iBAAiB,GAAG,GAAG,EACvB,SAAkB;IAElB,IAAI,UAAU,GAAG,iBAAiB,CAAC;IACnC,IAAI,SAAS,GAAG,gBAAgB,CAAC;IACjC,IAAI,YAAY,GAAG,uBAAuB,CAAC;IAE3C,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;QAC3B,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;QAE7B,gDAAgD;QAChD,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACrC,UAAU,GAAG,GAAG,CAAC;YACjB,SAAS,GAAG,kBAAkB,CAAC;QACjC,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;YAC9C,UAAU,GAAG,GAAG,CAAC;YACjB,SAAS,GAAG,cAAc,CAAC;QAC7B,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;YAC3C,UAAU,GAAG,GAAG,CAAC;YACjB,SAAS,GAAG,WAAW,CAAC;QAC1B,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YAC1C,UAAU,GAAG,GAAG,CAAC;YACjB,SAAS,GAAG,WAAW,CAAC;QAC1B,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YAC1C,UAAU,GAAG,GAAG,CAAC;YACjB,SAAS,GAAG,UAAU,CAAC;QACzB,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;YAC3C,UAAU,GAAG,GAAG,CAAC;YACjB,SAAS,GAAG,qBAAqB,CAAC;QACpC,CAAC;IACH,CAAC;IAED,wCAAwC;IACxC,IAAI,OAAO,KAAK,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QACzC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;IAChC,CAAC;IAED,MAAM,QAAQ,GAAG,mBAAmB,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IAEzE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AAClC,CAAC;AAED;;GAEG;AACH,SAAgB,2BAA2B;IACzC,OAAO;QACL,OAAO,EAAE,qBAAqB;QAC9B,KAAK,EAAE,mBAAmB;QAC1B,eAAe,EAAE,6BAA6B;QAC9C,SAAS,EAAE,uBAAuB;QAClC,WAAW,EAAE,cAAc;KAC5B,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAClC,MAA4C,EAC5C,MAA+E,EAC/E,SAAkB;IAElB,OAAO;QACL,OAAO,EAAE,MAAM,KAAK,SAAS;QAC7B,IAAI,EAAE;YACJ,MAAM;YACN,MAAM;YACN,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;YACxB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,OAAO;SACpD;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS;KACV,CAAC;AACJ,CAAC"}