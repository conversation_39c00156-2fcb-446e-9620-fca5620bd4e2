/**
 * Buffer Pool for Audio Processing
 * Reuses buffers to reduce garbage collection and improve performance
 */
export declare class BufferPool {
    private static instance;
    private pools;
    private maxPoolSize;
    private maxBufferSize;
    private constructor();
    static getInstance(): BufferPool;
    /**
     * Get a buffer from the pool or create a new one
     */
    acquire(size: number): Buffer;
    /**
     * Return a buffer to the pool
     */
    release(buffer: Buffer): void;
    /**
     * Get statistics about the buffer pool
     */
    getStats(): {
        poolCount: number;
        totalBuffers: number;
        sizes: number[];
    };
    /**
     * Clear all pools
     */
    clear(): void;
}
export declare class Float32ArrayPool {
    private static instance;
    private pools;
    private maxPoolSize;
    private maxArraySize;
    private constructor();
    static getInstance(): Float32ArrayPool;
    /**
     * Get a Float32Array from the pool or create a new one
     */
    acquire(length: number): Float32Array;
    /**
     * Return a Float32Array to the pool
     */
    release(array: Float32Array): void;
    /**
     * Clear all pools
     */
    clear(): void;
}
export declare const bufferPool: BufferPool;
export declare const float32ArrayPool: Float32ArrayPool;
//# sourceMappingURL=buffer-pool.d.ts.map