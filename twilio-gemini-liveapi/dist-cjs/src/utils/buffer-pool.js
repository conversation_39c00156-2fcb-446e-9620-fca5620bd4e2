"use strict";
/**
 * Buffer Pool for Audio Processing
 * Reuses buffers to reduce garbage collection and improve performance
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.float32ArrayPool = exports.bufferPool = exports.Float32ArrayPool = exports.BufferPool = void 0;
class BufferPool {
    static instance;
    pools = new Map();
    maxPoolSize = 50;
    maxBufferSize = 32768; // 32KB max buffer size
    constructor() { }
    static getInstance() {
        if (!BufferPool.instance) {
            BufferPool.instance = new BufferPool();
        }
        return BufferPool.instance;
    }
    /**
     * Get a buffer from the pool or create a new one
     */
    acquire(size) {
        // Don't pool buffers larger than max size
        if (size > this.maxBufferSize) {
            return Buffer.alloc(size);
        }
        const pool = this.pools.get(size);
        if (pool && pool.length > 0) {
            const buffer = pool.pop();
            buffer.fill(0); // Clear the buffer
            return buffer;
        }
        return Buffer.alloc(size);
    }
    /**
     * Return a buffer to the pool
     */
    release(buffer) {
        const size = buffer.length;
        // Don't pool buffers larger than max size
        if (size > this.maxBufferSize) {
            return;
        }
        let pool = this.pools.get(size);
        if (!pool) {
            pool = [];
            this.pools.set(size, pool);
        }
        // Don't exceed max pool size
        if (pool.length < this.maxPoolSize) {
            pool.push(buffer);
        }
    }
    /**
     * Get statistics about the buffer pool
     */
    getStats() {
        let totalBuffers = 0;
        const sizes = [];
        for (const [size, pool] of this.pools.entries()) {
            totalBuffers += pool.length;
            sizes.push(size);
        }
        return {
            poolCount: this.pools.size,
            totalBuffers,
            sizes
        };
    }
    /**
     * Clear all pools
     */
    clear() {
        this.pools.clear();
    }
}
exports.BufferPool = BufferPool;
// Float32Array pool for audio processing
class Float32ArrayPool {
    static instance;
    pools = new Map();
    maxPoolSize = 20;
    maxArraySize = 16384; // 16K samples max
    constructor() { }
    static getInstance() {
        if (!Float32ArrayPool.instance) {
            Float32ArrayPool.instance = new Float32ArrayPool();
        }
        return Float32ArrayPool.instance;
    }
    /**
     * Get a Float32Array from the pool or create a new one
     */
    acquire(length) {
        // Don't pool arrays larger than max size
        if (length > this.maxArraySize) {
            return new Float32Array(length);
        }
        const pool = this.pools.get(length);
        if (pool && pool.length > 0) {
            const array = pool.pop();
            array.fill(0); // Clear the array
            return array;
        }
        return new Float32Array(length);
    }
    /**
     * Return a Float32Array to the pool
     */
    release(array) {
        const length = array.length;
        // Don't pool arrays larger than max size
        if (length > this.maxArraySize) {
            return;
        }
        let pool = this.pools.get(length);
        if (!pool) {
            pool = [];
            this.pools.set(length, pool);
        }
        // Don't exceed max pool size
        if (pool.length < this.maxPoolSize) {
            pool.push(array);
        }
    }
    /**
     * Clear all pools
     */
    clear() {
        this.pools.clear();
    }
}
exports.Float32ArrayPool = Float32ArrayPool;
// Export singleton instances
exports.bufferPool = BufferPool.getInstance();
exports.float32ArrayPool = Float32ArrayPool.getInstance();
//# sourceMappingURL=buffer-pool.js.map