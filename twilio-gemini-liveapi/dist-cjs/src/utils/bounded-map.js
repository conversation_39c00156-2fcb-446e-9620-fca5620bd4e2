"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BoundedMap = void 0;
/**
 * BoundedMap provides a Map with a maximum size.
 * Old entries are cleaned up when capacity is exceeded.
 */
const logger_1 = require("./logger");
class BoundedMap extends Map {
    maxSize;
    lifecycleManagerRef = null;
    operationLock = false;
    constructor(maxSize = 2000) {
        super();
        this.maxSize = maxSize;
    }
    setLifecycleManager(lifecycleManagerRef) {
        this.lifecycleManagerRef = lifecycleManagerRef;
    }
    isConnectionData(value) {
        return value !== null &&
            typeof value === 'object' &&
            'sessionId' in value;
    }
    set(key, value) {
        // Simple mutex to prevent concurrent modifications
        if (this.operationLock) {
            // Wait briefly and retry if locked
            return this.setWithDelay(key, value);
        }
        this.operationLock = true;
        try {
            if (this.size >= this.maxSize && !this.has(key)) {
                const firstKey = this.keys().next().value;
                const oldValue = this.get(firstKey);
                if (oldValue && typeof oldValue === 'object') {
                    logger_1.logger.warn(`⚠️ BoundedMap: Forcing cleanup of oldest entry ${String(firstKey)} (capacity: ${this.maxSize})`, {
                        sessionId: firstKey,
                        mapSize: this.size,
                        maxSize: this.maxSize,
                        valueType: typeof oldValue,
                        timestamp: new Date().toISOString()
                    });
                    // Check if value has twilioWs property (for ConnectionData type)
                    if (this.isConnectionData(oldValue) && oldValue.twilioWs) {
                        try {
                            oldValue.twilioWs.close();
                        }
                        catch (e) {
                            logger_1.logger.error(`Error closing WebSocket for ${String(firstKey)}:`, e);
                        }
                    }
                    if (this.lifecycleManagerRef && typeof this.lifecycleManagerRef.endSession === 'function' && this.isConnectionData(oldValue)) {
                        this.lifecycleManagerRef
                            .endSession(String(firstKey), oldValue, 'capacity_limit')
                            .catch((err) => {
                            logger_1.logger.error(`Error ending session ${String(firstKey)}:`, err instanceof Error ? err : new Error(String(err)));
                        });
                    }
                }
                this.delete(firstKey);
            }
            return super.set(key, value);
        }
        finally {
            this.operationLock = false;
        }
    }
    setWithDelay(key, value) {
        // Simple retry mechanism with short delay
        setTimeout(() => {
            if (!this.operationLock) {
                this.set(key, value);
            }
            else {
                this.setWithDelay(key, value);
            }
        }, 1);
        return this;
    }
}
exports.BoundedMap = BoundedMap;
//# sourceMappingURL=bounded-map.js.map