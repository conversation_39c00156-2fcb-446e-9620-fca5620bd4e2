/**
 * Connection pooling for Gemini sessions to improve performance and reduce resource usage
 */
export interface PooledConnection<T> {
    connection: T;
    created: number;
    lastUsed: number;
    inUse: boolean;
    id: string;
}
export interface PoolConfig {
    minConnections: number;
    maxConnections: number;
    maxIdleTime: number;
    maxConnectionAge: number;
    acquireTimeout: number;
    cleanupInterval: number;
}
export declare class ConnectionPool<T> {
    private connections;
    private waitingQueue;
    private config;
    private createConnection;
    private destroyConnection;
    private validateConnection;
    private isCleanupRunning;
    constructor(createFn: () => Promise<T>, destroyFn: (connection: T) => Promise<void>, validateFn: (connection: T) => Promise<boolean>, config?: Partial<PoolConfig>);
    /**
     * Acquire a connection from the pool
     */
    acquire(): Promise<PooledConnection<T>>;
    /**
     * Release a connection back to the pool
     */
    release(pooledConnection: PooledConnection<T>): Promise<void>;
    /**
     * Get pool statistics
     */
    getStats(): {
        total: number;
        inUse: number;
        available: number;
        waiting: number;
        config: PoolConfig;
    };
    /**
     * Close all connections and clean up
     */
    destroy(): Promise<void>;
    /**
     * Get an available connection from the pool
     */
    private getAvailableConnection;
    /**
     * Create a new pooled connection
     */
    private createPooledConnection;
    /**
     * Remove a connection from the pool
     */
    private removeConnection;
    /**
     * Initialize minimum connections
     */
    private initializeMinConnections;
    /**
     * Start cleanup timer
     */
    private startCleanupTimer;
    /**
     * Clean up old and idle connections
     */
    private cleanup;
}
//# sourceMappingURL=connection-pool.d.ts.map