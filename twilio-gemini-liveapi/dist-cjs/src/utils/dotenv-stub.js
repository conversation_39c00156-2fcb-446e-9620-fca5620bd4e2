"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = config;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("./logger");
function config() {
    try {
        const cwd = process.cwd();
        const envPath = path_1.default.resolve(cwd, '.env');
        const examplePath = path_1.default.resolve(cwd, '.env.example');
        const fileToLoad = fs_1.default.existsSync(envPath) ? envPath : fs_1.default.existsSync(examplePath) ? examplePath : null;
        if (!fileToLoad) {
            return;
        }
        let data;
        try {
            data = fs_1.default.readFileSync(fileToLoad, 'utf8');
        }
        catch (readError) {
            logger_1.configLogger.error(`❌ Failed to read environment file ${fileToLoad}:`, readError instanceof Error ? readError : new Error(String(readError)));
            return;
        }
        for (const line of data.split('\n')) {
            const match = line.match(/^\s*([\w.-]+)\s*=\s*(.*)\s*$/);
            if (match) {
                const key = match[1];
                let value = match[2] || '';
                if (value.startsWith('"') && value.endsWith('"')) {
                    value = value.slice(1, -1);
                }
                if (!(key in process.env)) {
                    process.env[key] = value;
                }
            }
        }
    }
    catch (e) {
        logger_1.configLogger.warn('dotenv-stub: failed to load', { error: e instanceof Error ? e.message : String(e) });
    }
}
exports.default = { config };
//# sourceMappingURL=dotenv-stub.js.map