/**
 * Timer Management Utility
 * Prevents memory leaks by tracking and cleaning up timers and intervals
 */
export declare class TimerManager {
    private static instance;
    private timers;
    private intervals;
    private constructor();
    static getInstance(): TimerManager;
    /**
     * Create a managed timeout
     */
    setTimeout(id: string, callback: () => void, delay: number): NodeJS.Timeout;
    /**
     * Create a managed interval
     */
    setInterval(id: string, callback: () => void, delay: number): NodeJS.Timeout;
    /**
     * Clear a specific timeout
     */
    clearTimeout(id: string): void;
    /**
     * Clear a specific interval
     */
    clearInterval(id: string): void;
    /**
     * Clear all timers for a specific session
     */
    clearSessionTimers(sessionId: string): void;
    /**
     * Clear all timers and intervals
     */
    clearAll(): void;
    /**
     * Get statistics about active timers
     */
    getStats(): {
        timers: number;
        intervals: number;
        activeIds: string[];
    };
}
export declare const timerManager: TimerManager;
//# sourceMappingURL=timer-manager.d.ts.map