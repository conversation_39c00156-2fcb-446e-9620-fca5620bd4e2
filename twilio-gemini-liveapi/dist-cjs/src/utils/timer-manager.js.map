{"version": 3, "file": "timer-manager.js", "sourceRoot": "", "sources": ["../../../src/utils/timer-manager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,MAAa,YAAY;IACb,MAAM,CAAC,QAAQ,CAAe;IAC9B,MAAM,GAAgC,IAAI,GAAG,EAAE,CAAC;IAChD,SAAS,GAAgC,IAAI,GAAG,EAAE,CAAC;IAE3D,gBAAuB,CAAC;IAExB,MAAM,CAAC,WAAW;QACd,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YACzB,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,EAAU,EAAE,QAAoB,EAAE,KAAa;QACtD,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAEtB,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC1B,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAC3B,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,EAAU,EAAE,QAAoB,EAAE,KAAa;QACvD,uCAAuC;QACvC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAEvB,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,EAAU;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,KAAK,EAAE,CAAC;YACR,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,EAAU;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACxC,IAAI,QAAQ,EAAE,CAAC;YACX,aAAa,CAAC,QAAQ,CAAC,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC;IACL,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,SAAiB;QAChC,MAAM,aAAa,GAAG,GAAG,SAAS,GAAG,CAAC;QAEtC,eAAe;QACf,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9C,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC/B,YAAY,CAAC,KAAK,CAAC,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC3B,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,KAAK,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YACpD,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC/B,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ;QACJ,mBAAmB;QACnB,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9C,YAAY,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAEpB,sBAAsB;QACtB,KAAK,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YACpD,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,QAAQ;QACJ,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACxB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YAC9B,SAAS,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;SAC/D,CAAC;IACN,CAAC;CACJ;AAlHD,oCAkHC;AAED,4BAA4B;AACf,QAAA,YAAY,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC"}