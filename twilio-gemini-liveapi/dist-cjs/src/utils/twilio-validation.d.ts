import { FastifyRequest, FastifyReply } from 'fastify';
interface TwilioWebhookRequest extends FastifyRequest {
}
interface ValidationMetrics {
    totalValidations: number;
    successfulValidations: number;
    failedValidations: number;
    suspiciousRequests: number;
    lastReset: number;
}
/**
 * Validate Twilio webhook signatures to ensure requests are from Twilio
 */
export declare class TwilioWebhookValidator {
    private authToken;
    private skipValidation;
    private metrics;
    private suspiciousIPs;
    constructor();
    /**
     * Validate a Twilio webhook request
     * @param signature - The X-Twilio-Signature header value
     * @param url - The full URL of the webhook endpoint
     * @param params - The request body parameters
     * @returns Whether the signature is valid
     */
    validateRequest(signature: string | undefined, url: string, params: Record<string, any> | undefined, clientIP?: string): boolean;
    /**
     * Validate Twilio-specific parameters
     */
    private validateTwilioParams;
    /**
     * Mark IP as suspicious
     */
    private markSuspicious;
    /**
     * Get validation metrics
     */
    getMetrics(): ValidationMetrics;
    /**
     * Reset validation metrics
     */
    resetMetrics(): void;
    /**
     * Express/Fastify middleware for validating Twilio webhooks
     */
    middleware(): (request: TwilioWebhookRequest, reply: FastifyReply) => Promise<void>;
}
export declare const twilioValidator: TwilioWebhookValidator;
/**
 * Validate a Twilio webhook request
 * @param request - The HTTP request object
 * @returns Whether the request is valid
 */
export declare function validateTwilioWebhook(request: TwilioWebhookRequest): boolean;
export {};
//# sourceMappingURL=twilio-validation.d.ts.map