{"version": 3, "file": "connection-pool.js", "sourceRoot": "", "sources": ["../../../src/utils/connection-pool.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAEH,qCAAkC;AAClC,mDAA+C;AAmB/C,MAAa,cAAc;IACf,WAAW,GAAqC,IAAI,GAAG,EAAE,CAAC;IAC1D,YAAY,GAIf,EAAE,CAAC;IAEA,MAAM,CAAa;IACnB,gBAAgB,CAAmB;IACnC,iBAAiB,CAAmC;IACpD,kBAAkB,CAAsC;IACxD,gBAAgB,GAAG,KAAK,CAAC;IAEjC,YACI,QAA0B,EAC1B,SAA2C,EAC3C,UAA+C,EAC/C,SAA8B,EAAE;QAEhC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;QAErC,IAAI,CAAC,MAAM,GAAG;YACV,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,CAAC;YAC1C,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,EAAE;YAC3C,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,MAAM,EAAE,YAAY;YACvD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,OAAO,EAAE,SAAS;YAC/D,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,KAAK,EAAE,aAAa;YAC7D,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,WAAW;SAC/D,CAAC;QAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,qCAAqC;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChD,IAAI,SAAS,EAAE,CAAC;YACZ,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC;YACvB,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAChC,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YACrD,IAAI,CAAC;gBACD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC7D,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC;gBAC9B,OAAO,gBAAgB,CAAC;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAc,CAAC,CAAC;gBAC1E,MAAM,KAAK,CAAC;YAChB,CAAC;QACL,CAAC;QAED,4CAA4C;QAC5C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;gBAC5E,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACvC,CAAC;gBACD,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;YACpD,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAE/B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,CAAC,UAAU,EAAE,EAAE;oBACpB,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,OAAO,CAAC,UAAU,CAAC,CAAC;gBACxB,CAAC;gBACD,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;oBACd,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClB,CAAC;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,gBAAqC;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,eAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO;QACX,CAAC;QAED,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;QACzB,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEjC,qCAAqC;QACrC,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACrE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;gBAC3C,OAAO;YACX,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,IAAI,CAAC,iDAAiD,EAAE,KAAc,CAAC,CAAC;YAC/E,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC3C,OAAO;QACX,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YACzC,IAAI,MAAM,EAAE,CAAC;gBACT,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;gBACxB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ;QAOJ,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAChF,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAC5B,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,KAAK;YACxC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YACjC,MAAM,EAAE,IAAI,CAAC,MAAM;SACtB,CAAC;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAE7C,qBAAqB;QACrB,4BAAY,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QAEtD,8BAA8B;QAC9B,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YACzC,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,gBAAgB,EAAE,EAAE;YACvF,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAC9D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAc,CAAC,CAAC;YACnE,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC1B,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACpB,OAAO,UAAU,CAAC;YACtB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAChC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACjD,MAAM,gBAAgB,GAAwB;YAC1C,UAAU;YACV,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;YACnB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;YACpB,KAAK,EAAE,KAAK;YACZ,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;SACtE,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAC5D,eAAM,CAAC,KAAK,CAAC,oCAAoC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;QAExE,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,YAAoB;QAC/C,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAEtC,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAC1D,eAAM,CAAC,KAAK,CAAC,2BAA2B,YAAY,EAAE,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iCAAiC,YAAY,GAAG,EAAE,KAAc,CAAC,CAAC;QACnF,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB;QAClC,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACtD,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAc,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC,CAAC;QACR,CAAC;QAED,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnC,eAAM,CAAC,IAAI,CAAC,sCAAsC,IAAI,CAAC,WAAW,CAAC,IAAI,cAAc,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACK,iBAAiB;QACrB,4BAAY,CAAC,WAAW,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACrD,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,OAAO;QACjB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,mBAAmB,GAAa,EAAE,CAAC;QAEzC,IAAI,CAAC;YACD,KAAK,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;gBACxD,0BAA0B;gBAC1B,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;oBACnB,SAAS;gBACb,CAAC;gBAED,yBAAyB;gBACzB,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;oBAC1D,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC7B,SAAS;gBACb,CAAC;gBAED,6CAA6C;gBAC7C,IACI,GAAG,GAAG,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;oBACnD,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EACpD,CAAC;oBACC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC7B,SAAS;gBACb,CAAC;gBAED,6BAA6B;gBAC7B,IAAI,CAAC;oBACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;oBACrE,IAAI,CAAC,OAAO,EAAE,CAAC;wBACX,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE,KAAc,CAAC,CAAC;oBAC1E,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjC,CAAC;YACL,CAAC;YAED,gCAAgC;YAChC,KAAK,MAAM,EAAE,IAAI,mBAAmB,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,eAAM,CAAC,IAAI,CAAC,iBAAiB,mBAAmB,CAAC,MAAM,cAAc,CAAC,CAAC;YAC3E,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAc,CAAC,CAAC;QAC5E,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAClC,CAAC;IACL,CAAC;CACJ;AApTD,wCAoTC"}