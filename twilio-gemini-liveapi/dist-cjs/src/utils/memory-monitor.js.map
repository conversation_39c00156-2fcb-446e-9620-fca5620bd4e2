{"version": 3, "file": "memory-monitor.js", "sourceRoot": "", "sources": ["../../../src/utils/memory-monitor.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAEH,qCAAkC;AAClC,mDAA+C;AAgB/C,MAAa,aAAa;IACd,MAAM,CAAC,QAAQ,CAAgB;IAC/B,UAAU,CAAmB;IAC7B,YAAY,GAAG,KAAK,CAAC;IACrB,MAAM,GAAG,CAAC,CAAC;IACX,WAAW,GAAG,GAAG,CAAC,CAAC,KAAK;IACxB,kBAAkB,GAAG,KAAK,CAAC,CAAC,aAAa;IAEjD;QACI,IAAI,CAAC,UAAU,GAAG;YACd,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,KAAK,EAAE,EAAE,CAAC,EAAE,QAAQ;YAC9E,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,KAAK,EAAE,EAAE,CAAC,EAAE,QAAQ;YAChF,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,KAAK,EAAE,EAAE,CAAC,CAAC,QAAQ;SACrF,CAAC;IACN,CAAC;IAED,MAAM,CAAC,WAAW;QACd,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC1B,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,cAAc;QACV,MAAM,KAAK,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACpC,OAAO;YACH,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;YACxC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;YACpD,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;YAClD,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;YAClD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC;SAC7D,CAAC;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEpC,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YACvF,OAAO;gBACH,MAAM,EAAE,UAAU;gBAClB,KAAK;gBACL,OAAO,EAAE,8BAA8B,KAAK,CAAC,GAAG,YAAY,KAAK,CAAC,QAAQ,IAAI;aACjF,CAAC;QACN,CAAC;QAED,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACtC,OAAO;gBACH,MAAM,EAAE,SAAS;gBACjB,KAAK;gBACL,OAAO,EAAE,0BAA0B,KAAK,CAAC,GAAG,IAAI;aACnD,CAAC;QACN,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,sBAAsB;QAClB,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC1C,MAAM,CAAC,EAAE,EAAE,CAAC;gBACZ,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAEzC,MAAM,KAAK,GAAG,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;gBACzD,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;oBACzC,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,WAAW,CAAC,QAAQ;oBAChC,SAAS,EAAE,UAAU,CAAC,QAAQ;iBACjC,CAAC,CAAC;gBAEH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAc,CAAC,CAAC;gBACnE,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,eAAe;QACX,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;YACzC,QAAQ,EAAE,IAAI,CAAC,kBAAkB;YACjC,UAAU,EAAE,IAAI,CAAC,UAAU;SAC9B,CAAC,CAAC;QAEH,4BAAY,CAAC,WAAW,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAExC,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBAC/B,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,MAAM,CAAC,CAAC;gBAE1D,0CAA0C;gBAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;gBACjD,IAAI,eAAe,GAAG,KAAK,EAAE,CAAC,CAAC,WAAW;oBACtC,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAClC,CAAC;gBAED,uCAAuC;gBACvC,4BAA4B;gBAC5B,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAE1C,CAAC;iBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,MAAM,CAAC,CAAC;gBAErD,oCAAoC;gBACpC,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC3C,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;oBACjD,IAAI,eAAe,GAAG,MAAM,EAAE,CAAC,CAAC,YAAY;wBACxC,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBAClC,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,8CAA8C;gBAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;oBACtC,eAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClD,CAAC;YACL,CAAC;QACL,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,cAAc;QACV,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,4BAAY,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAC7C,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,gBAAgB;QAOZ,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxC,OAAO;YACH,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG;YAC1B,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ;YACjC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpC,UAAU,EAAE,IAAI,CAAC,UAAU;SAC9B,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,GAAQ;QAC/B,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,eAAe;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBACrB,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;gBACf,OAAO;YACX,CAAC;YAED,2DAA2D;YAC3D,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;gBACpB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC1B,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;oBAEvB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;wBACvB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;oBACrB,CAAC;yBAAM,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;wBAC5C,mCAAmC;wBACnC,aAAa,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;oBAC7C,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAc,CAAC,CAAC;QACnE,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB;QACZ,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAEtD,2BAA2B;QAC3B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,kDAAkD;QAClD,4BAA4B;QAC5B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAEvC,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;IACxD,CAAC;CACJ;AA3ND,sCA2NC;AAED,4BAA4B;AACf,QAAA,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;AAEzD,kDAAkD;AAClD,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,MAAW,EAAE,EAAE;IACxC,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;IAEnD,kEAAkE;IAClE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;QACxE,eAAM,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;QAE1E,sDAAsD;QACtD,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,qBAAa,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBAC1D,eAAM,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;gBACjE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;QACL,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,aAAa;IAC5B,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,+CAA+C;AAC/C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;IACxC,qBAAa,CAAC,eAAe,EAAE,CAAC;AACpC,CAAC"}