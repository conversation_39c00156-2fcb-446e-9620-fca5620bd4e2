"use strict";
/**
 * Structured Logger for Twilio Gemini Live API
 *
 * Provides consistent logging across all components with:
 * - CallSid/SessionId correlation
 * - Component-based categorization
 * - Performance timing
 * - Audio quality metrics
 * - Production-ready JSON output
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceTimer = exports.LoggingControl = exports.authLogger = exports.healthLogger = exports.recoveryLogger = exports.websocketLogger = exports.configLogger = exports.apiLogger = exports.sessionLogger = exports.audioLogger = exports.twilioLogger = exports.geminiLogger = exports.logger = exports.Logger = exports.Component = exports.LogLevel = void 0;
const perf_hooks_1 = require("perf_hooks");
/**
 * Log levels in order of severity
 */
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
/**
 * Component identifiers for structured logging
 */
var Component;
(function (Component) {
    Component["GEMINI"] = "GEMINI";
    Component["TWILIO"] = "TWILIO";
    Component["AUDIO"] = "AUDIO";
    Component["SESSION"] = "SESSION";
    Component["API"] = "API";
    Component["CONFIG"] = "CONFIG";
    Component["WEBSOCKET"] = "WEBSOCKET";
    Component["RECOVERY"] = "RECOVERY";
    Component["HEALTH"] = "HEALTH";
    Component["AUTH"] = "AUTH";
    Component["DEEPGRAM"] = "DEEPGRAM";
})(Component || (exports.Component = Component = {}));
/**
 * Performance timer for measuring operation duration
 */
class PerformanceTimer {
    operation;
    logger;
    callSid;
    component;
    startTime;
    constructor(operation, logger, callSid = null, component = null) {
        this.operation = operation;
        this.logger = logger;
        this.callSid = callSid;
        this.component = component;
        this.startTime = perf_hooks_1.performance.now();
    }
    end(additionalData = {}) {
        const duration = Math.round(perf_hooks_1.performance.now() - this.startTime);
        this.logger.debug(`${this.operation} completed`, {
            ...additionalData,
            duration_ms: duration,
            operation: this.operation
        }, this.callSid, this.component);
        return duration;
    }
}
exports.PerformanceTimer = PerformanceTimer;
/**
 * Main Logger class
 */
class Logger {
    level;
    enableJson;
    enableColors;
    component;
    performanceMetrics;
    audioMetrics;
    levelEmojis;
    componentEmojis;
    constructor(options = {}) {
        this.level = options.level || (process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG);
        this.enableJson = options.json || process.env.NODE_ENV === 'production';
        this.enableColors = options.colors !== false && process.env.NODE_ENV !== 'production';
        this.component = options.component || null;
        // Performance tracking
        this.performanceMetrics = new Map();
        this.audioMetrics = new Map();
        // Emoji mappings for console output
        this.levelEmojis = {
            [LogLevel.DEBUG]: '🔍',
            [LogLevel.INFO]: 'ℹ️',
            [LogLevel.WARN]: '⚠️',
            [LogLevel.ERROR]: '❌'
        };
        this.componentEmojis = {
            [Component.GEMINI]: '🤖',
            [Component.TWILIO]: '📞',
            [Component.AUDIO]: '🎤',
            [Component.SESSION]: '🔄',
            [Component.API]: '🌐',
            [Component.CONFIG]: '⚙️',
            [Component.WEBSOCKET]: '🔌',
            [Component.RECOVERY]: '🚑',
            [Component.HEALTH]: '🏥',
            [Component.AUTH]: '🔐',
            [Component.DEEPGRAM]: '📝'
        };
    }
    /**
     * Create a child logger with a specific component
     */
    child(component) {
        return new Logger({
            level: this.level,
            json: this.enableJson,
            colors: this.enableColors,
            component
        });
    }
    /**
     * Format log message for output
     */
    formatMessage(level, message, data = {}, callSid = null, component = null) {
        const timestamp = new Date().toISOString();
        const logComponent = component || this.component;
        const logLevel = LogLevel[level];
        const logEntry = {
            timestamp,
            level: logLevel,
            message,
            component: logComponent,
            callSid,
            ...data
        };
        if (this.enableJson) {
            return JSON.stringify(logEntry);
        }
        // Console formatting with emojis and colors
        const levelEmoji = this.levelEmojis[level] || '';
        const componentEmoji = logComponent ? this.componentEmojis[logComponent] || '' : '';
        const callSidStr = callSid ? `[${callSid}]` : '';
        const componentStr = logComponent ? `[${logComponent}]` : '';
        let formattedMessage = `${timestamp} ${levelEmoji} ${callSidStr} ${componentEmoji}${componentStr} ${message}`;
        if (Object.keys(data).length > 0) {
            formattedMessage += ` | ${JSON.stringify(data)}`;
        }
        return formattedMessage;
    }
    /**
     * Log at specified level
     */
    log(level, message, data = {}, callSid = null, component = null) {
        if (level < this.level) {
            return;
        }
        const formatted = this.formatMessage(level, message, data, callSid, component);
        switch (level) {
            case LogLevel.ERROR:
                console.error(formatted);
                break;
            case LogLevel.WARN:
                console.warn(formatted);
                break;
            case LogLevel.INFO:
                console.info(formatted);
                break;
            case LogLevel.DEBUG:
            default:
                console.log(formatted);
                break;
        }
    }
    /**
     * Debug level logging
     */
    debug(message, data = {}, callSid = null, component = null) {
        this.log(LogLevel.DEBUG, message, data, callSid, component);
    }
    /**
     * Info level logging
     */
    info(message, data = {}, callSid = null, component = null) {
        this.log(LogLevel.INFO, message, data, callSid, component);
    }
    /**
     * Warning level logging
     */
    warn(message, data = {}, callSid = null, component = null) {
        this.log(LogLevel.WARN, message, data, callSid, component);
    }
    /**
     * Error level logging
     */
    error(message, data = {}, callSid = null, component = null) {
        // Include stack trace for errors if available
        let errorData;
        if (data instanceof Error) {
            errorData = {
                error: data.message,
                stack: data.stack,
                name: data.name
            };
        }
        else if (data.error instanceof Error) {
            errorData = {
                ...data,
                error: {
                    message: data.error.message,
                    stack: data.error.stack,
                    name: data.error.name
                }
            };
        }
        else {
            errorData = data;
        }
        this.log(LogLevel.ERROR, message, errorData, callSid, component);
    }
    /**
     * Start performance timing
     */
    startTimer(operation, callSid = null, component = null) {
        return new PerformanceTimer(operation, this, callSid, component);
    }
    /**
     * Log API call performance
     */
    logApiCall(method, url, statusCode, duration, callSid = null, component = Component.API) {
        const level = statusCode >= 400 ? LogLevel.WARN : LogLevel.INFO;
        this.log(level, `API ${method} ${url}`, {
            method,
            url,
            statusCode,
            duration_ms: duration,
            type: 'api_call'
        }, callSid, component);
    }
    /**
     * Set log level dynamically
     */
    setLevel(level) {
        this.level = level;
        this.info('Log level changed', { level: LogLevel[level] });
    }
    /**
     * Get current log level
     */
    getLevel() {
        return this.level;
    }
    /**
     * Check if a level would be logged
     */
    isLevelEnabled(level) {
        return level >= this.level;
    }
}
exports.Logger = Logger;
// Create default logger instance
exports.logger = new Logger();
// Create component-specific loggers
exports.geminiLogger = exports.logger.child(Component.GEMINI);
exports.twilioLogger = exports.logger.child(Component.TWILIO);
exports.audioLogger = exports.logger.child(Component.AUDIO);
exports.sessionLogger = exports.logger.child(Component.SESSION);
exports.apiLogger = exports.logger.child(Component.API);
exports.configLogger = exports.logger.child(Component.CONFIG);
exports.websocketLogger = exports.logger.child(Component.WEBSOCKET);
exports.recoveryLogger = exports.logger.child(Component.RECOVERY);
exports.healthLogger = exports.logger.child(Component.HEALTH);
exports.authLogger = exports.logger.child(Component.AUTH);
/**
 * Global logging control utilities
 */
exports.LoggingControl = {
    /**
     * Set log level for all loggers
     */
    setGlobalLevel(level) {
        exports.logger.setLevel(level);
        exports.geminiLogger.setLevel(level);
        exports.twilioLogger.setLevel(level);
        exports.audioLogger.setLevel(level);
        exports.sessionLogger.setLevel(level);
        exports.apiLogger.setLevel(level);
        exports.configLogger.setLevel(level);
        exports.websocketLogger.setLevel(level);
        exports.recoveryLogger.setLevel(level);
        exports.healthLogger.setLevel(level);
        exports.authLogger.setLevel(level);
        exports.logger.info('Global log level changed', {
            level: LogLevel[level],
            affectedLoggers: 11
        });
    },
    /**
     * Set production mode (INFO level, JSON output)
     */
    setProductionMode() {
        this.setGlobalLevel(LogLevel.INFO);
        exports.logger.info('Switched to production logging mode');
    },
    /**
     * Set development mode (DEBUG level, console output)
     */
    setDevelopmentMode() {
        this.setGlobalLevel(LogLevel.DEBUG);
        exports.logger.info('Switched to development logging mode');
    },
    /**
     * Set quiet mode (ERROR level only)
     */
    setQuietMode() {
        this.setGlobalLevel(LogLevel.ERROR);
        exports.logger.error('Switched to quiet logging mode - only errors will be shown');
    },
    /**
     * Get current global log level
     */
    getGlobalLevel() {
        return exports.logger.getLevel();
    }
};
//# sourceMappingURL=logger.js.map