/**
 * WebSocket Utilities
 * Standardizes WebSocket property access across different flow handlers
 * Fixes inconsistent property naming issues identified in audit
 */
import { WebSocket } from 'ws';
import { ConnectionData } from '../types/global';
/**
 * Get the WebSocket connection from connection data
 * Handles various property names used across different handlers
 */
export declare function getWebSocket(connectionData: ConnectionData | null): WebSocket | null;
/**
 * Get the Twilio WebSocket specifically
 */
export declare function getTwilioWebSocket(connectionData: ConnectionData | null): WebSocket | null;
/**
 * Get the local testing WebSocket specifically
 */
export declare function getLocalWebSocket(connectionData: ConnectionData | null): WebSocket | null;
/**
 * Standardize connection data properties
 * Ensures all handlers use consistent property names
 */
export declare function standardizeConnectionData(connectionData: ConnectionData | null, ws: WebSocket | null, sessionType?: string): ConnectionData | null;
/**
 * Check if WebSocket is ready for communication
 */
export declare function isWebSocketReady(ws: WebSocket | null): boolean;
/**
 * Get WebSocket state as human-readable string
 */
export declare function getWebSocketState(ws: WebSocket | null): string;
/**
 * Check if WebSocket can accept messages (OPEN or CONNECTING)
 */
export declare function canSendToWebSocket(ws: WebSocket | null): boolean;
/**
 * Safely send data to WebSocket with improved state handling
 */
export declare function safeSendWebSocket(ws: WebSocket | null, data: any, sessionId?: string): boolean;
/**
 * Add item to bounded array to prevent memory leaks
 */
export declare function addToBoundedArray<T>(array: T[], item: T, maxSize: number, arrayName?: string): T[];
/**
 * Clean up connection data to prevent memory leaks
 */
export declare function cleanupConnectionData(connectionData: ConnectionData | null): void;
//# sourceMappingURL=websocket-utils.d.ts.map