"use strict";
/**
 * Production-safe logger to replace console.log statements
 * Prevents excessive logging in production while maintaining debug capabilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityLog = exports.perfLog = exports.systemLog = exports.errorLog = exports.warnLog = exports.infoLog = exports.debugLog = exports.productionLogger = exports.ProductionLogger = void 0;
const logger_1 = require("./logger");
class ProductionLogger {
    isDevelopment = process.env.NODE_ENV !== 'production';
    isDebugEnabled = process.env.DEBUG_LOGGING === 'true';
    /**
     * Info level logging - always shown
     */
    info(message, ...args) {
        logger_1.logger.info(message, ...args);
    }
    /**
     * Debug level logging - only in development or when debug enabled
     */
    debug(message, ...args) {
        if (this.isDevelopment || this.isDebugEnabled) {
            logger_1.logger.debug(message, ...args);
        }
    }
    /**
     * Warning level logging - always shown
     */
    warn(message, ...args) {
        logger_1.logger.warn(message, ...args);
    }
    /**
     * Error level logging - always shown
     */
    error(message, ...args) {
        logger_1.logger.error(message, ...args);
    }
    /**
     * Replace console.log with proper logging
     * @deprecated Use debug() instead
     */
    log(message, ...args) {
        this.debug(`[CONSOLE] ${message}`, ...args);
    }
    /**
     * Critical system events that should always be logged
     */
    system(message, ...args) {
        logger_1.logger.info(`[SYSTEM] ${message}`, ...args);
    }
    /**
     * Performance metrics that should be tracked
     */
    performance(message, metrics) {
        if (this.isDevelopment || process.env.PERFORMANCE_LOGGING === 'true') {
            logger_1.logger.info(`[PERF] ${message}`, metrics);
        }
    }
    /**
     * Security-related events that should always be logged
     */
    security(message, ...args) {
        logger_1.logger.warn(`[SECURITY] ${message}`, ...args);
    }
}
exports.ProductionLogger = ProductionLogger;
exports.productionLogger = new ProductionLogger();
// Convenience exports for common patterns
exports.debugLog = exports.productionLogger.debug.bind(exports.productionLogger);
exports.infoLog = exports.productionLogger.info.bind(exports.productionLogger);
exports.warnLog = exports.productionLogger.warn.bind(exports.productionLogger);
exports.errorLog = exports.productionLogger.error.bind(exports.productionLogger);
exports.systemLog = exports.productionLogger.system.bind(exports.productionLogger);
exports.perfLog = exports.productionLogger.performance.bind(exports.productionLogger);
exports.securityLog = exports.productionLogger.security.bind(exports.productionLogger);
//# sourceMappingURL=production-logger.js.map