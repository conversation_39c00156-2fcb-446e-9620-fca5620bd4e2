{"version": 3, "file": "bounded-map.js", "sourceRoot": "", "sources": ["../../../src/utils/bounded-map.ts"], "names": [], "mappings": ";;;AAAA;;;GAGG;AACH,qCAAkC;AAOlC,MAAa,UAAiB,SAAQ,GAAS;IACrC,OAAO,CAAS;IAChB,mBAAmB,GAA4B,IAAI,CAAC;IACpD,aAAa,GAAY,KAAK,CAAC;IAEvC,YAAY,UAAkB,IAAI;QAChC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,mBAAmB,CAAC,mBAAqC;QACvD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IACjD,CAAC;IAEO,gBAAgB,CAAC,KAAc;QACrC,OAAO,KAAK,KAAK,IAAI;YACd,OAAO,KAAK,KAAK,QAAQ;YACzB,WAAW,IAAI,KAAK,CAAC;IAC9B,CAAC;IAEQ,GAAG,CAAC,GAAM,EAAE,KAAQ;QAC3B,mDAAmD;QACnD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,mCAAmC;YACnC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAU,CAAC;gBAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAEpC,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBAC7C,eAAM,CAAC,IAAI,CACT,kDAAkD,MAAM,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,OAAO,GAAG,EAChG;wBACE,SAAS,EAAE,QAAQ;wBACnB,OAAO,EAAE,IAAI,CAAC,IAAI;wBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,SAAS,EAAE,OAAO,QAAQ;wBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC,CACF,CAAC;oBAEF,iEAAiE;oBACjE,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;wBACzD,IAAI,CAAC;4BACH,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;wBAC5B,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,eAAM,CAAC,KAAK,CAAC,+BAA+B,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAU,CAAC,CAAC;wBAC/E,CAAC;oBACH,CAAC;oBAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,KAAK,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC7H,IAAI,CAAC,mBAAmB;6BACrB,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,gBAAgB,CAAC;6BACxD,KAAK,CAAC,CAAC,GAAY,EAAE,EAAE;4BACtB,eAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBACjH,CAAC,CAAC,CAAC;oBACP,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACxB,CAAC;YACD,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC/B,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,GAAM,EAAE,KAAQ;QACnC,0CAA0C;QAC1C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxB,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAChC,CAAC;QACH,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAlFD,gCAkFC"}