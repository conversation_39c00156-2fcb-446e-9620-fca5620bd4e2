import type { ConnectionData } from '../types/global';
interface LifecycleManager {
    endSession(sessionId: string, connectionData: ConnectionData, reason: string): Promise<void>;
}
export declare class BoundedMap<K, V> extends Map<K, V> {
    private maxSize;
    private lifecycleManagerRef;
    private operationLock;
    constructor(maxSize?: number);
    setLifecycleManager(lifecycleManagerRef: LifecycleManager): void;
    private isConnectionData;
    set(key: K, value: V): this;
    private setWithDelay;
}
export {};
//# sourceMappingURL=bounded-map.d.ts.map