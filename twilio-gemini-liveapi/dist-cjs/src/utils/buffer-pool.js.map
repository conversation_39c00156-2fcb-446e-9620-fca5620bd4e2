{"version": 3, "file": "buffer-pool.js", "sourceRoot": "", "sources": ["../../../src/utils/buffer-pool.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,MAAa,UAAU;IACX,MAAM,CAAC,QAAQ,CAAa;IAC5B,KAAK,GAA0B,IAAI,GAAG,EAAE,CAAC;IACzC,WAAW,GAAW,EAAE,CAAC;IACzB,aAAa,GAAW,KAAK,CAAC,CAAC,uBAAuB;IAE9D,gBAAuB,CAAC;IAExB,MAAM,CAAC,WAAW;QACd,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACvB,UAAU,CAAC,QAAQ,GAAG,IAAI,UAAU,EAAE,CAAC;QAC3C,CAAC;QACD,OAAO,UAAU,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAY;QAChB,0CAA0C;QAC1C,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC5B,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAG,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB;YACnC,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,MAAc;QAClB,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;QAE3B,0CAA0C;QAC1C,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,IAAI,GAAG,EAAE,CAAC;YACV,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/B,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ;QACJ,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9C,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC;YAC5B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,OAAO;YACH,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YAC1B,YAAY;YACZ,KAAK;SACR,CAAC;IACN,CAAC;IAED;;OAEG;IACH,KAAK;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;CACJ;AAlFD,gCAkFC;AAED,yCAAyC;AACzC,MAAa,gBAAgB;IACjB,MAAM,CAAC,QAAQ,CAAmB;IAClC,KAAK,GAAgC,IAAI,GAAG,EAAE,CAAC;IAC/C,WAAW,GAAW,EAAE,CAAC;IACzB,YAAY,GAAW,KAAK,CAAC,CAAC,kBAAkB;IAExD,gBAAuB,CAAC;IAExB,MAAM,CAAC,WAAW;QACd,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAC7B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACvD,CAAC;QACD,OAAO,gBAAgB,CAAC,QAAQ,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,MAAc;QAClB,yCAAyC;QACzC,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAC7B,OAAO,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAG,CAAC;YAC1B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB;YACjC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,KAAmB;QACvB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAE5B,yCAAyC;QACzC,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,IAAI,GAAG,EAAE,CAAC;YACV,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;CACJ;AA/DD,4CA+DC;AAED,6BAA6B;AAChB,QAAA,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;AACtC,QAAA,gBAAgB,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAC"}