/**
 * Memory monitoring and management utilities for production stability
 */
export interface MemoryStats {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
}
export interface MemoryThresholds {
    warning: number;
    critical: number;
    maxHeapUsed: number;
}
export declare class MemoryMonitor {
    private static instance;
    private thresholds;
    private isMonitoring;
    private lastGC;
    private gcThreshold;
    private monitoringInterval;
    private constructor();
    static getInstance(): MemoryMonitor;
    /**
     * Get current memory usage statistics
     */
    getMemoryStats(): MemoryStats;
    /**
     * Check if memory usage is within safe limits
     */
    checkMemoryHealth(): {
        status: 'ok' | 'warning' | 'critical';
        stats: MemoryStats;
        message?: string;
    };
    /**
     * Force garbage collection if available
     */
    forceGarbageCollection(): boolean;
    /**
     * Start memory monitoring
     */
    startMonitoring(): void;
    /**
     * Stop memory monitoring
     */
    stopMonitoring(): void;
    /**
     * Get memory usage summary for health checks
     */
    getHealthSummary(): {
        status: string;
        memoryMB: number;
        heapUsedMB: number;
        uptime: number;
        thresholds: MemoryThresholds;
    };
    /**
     * Clean up large objects and arrays
     */
    static cleanupLargeObjects(obj: any): void;
    /**
     * Emergency memory cleanup
     */
    emergencyCleanup(): void;
}
export declare const memoryMonitor: MemoryMonitor;
//# sourceMappingURL=memory-monitor.d.ts.map