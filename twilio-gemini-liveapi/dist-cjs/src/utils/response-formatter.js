"use strict";
// Standardized API Response Formatter
// Provides consistent error and success response formats across all API endpoints
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSuccessResponse = createSuccessResponse;
exports.createErrorResponse = createErrorResponse;
exports.createValidationErrorResponse = createValidationErrorResponse;
exports.createPaginatedResponse = createPaginatedResponse;
exports.formatApiError = formatApiError;
exports.responseFormatterMiddleware = responseFormatterMiddleware;
exports.createHealthResponse = createHealthResponse;
/**
 * Creates a standardized success response
 */
function createSuccessResponse(data, message, requestId) {
    return {
        success: true,
        data,
        message,
        timestamp: new Date().toISOString(),
        requestId
    };
}
/**
 * Creates a standardized error response
 */
function createErrorResponse(error, code, requestId, statusCode) {
    const errorMessage = error instanceof Error ? error.message : error;
    return {
        success: false,
        error: errorMessage,
        code: code || 'INTERNAL_ERROR',
        timestamp: new Date().toISOString(),
        requestId
    };
}
/**
 * Creates a validation error response
 */
function createValidationErrorResponse(errors, requestId) {
    return {
        success: false,
        error: 'Validation failed',
        errors: errors.map(e => `${e.field}: ${e.message}`),
        code: 'VALIDATION_ERROR',
        timestamp: new Date().toISOString(),
        requestId
    };
}
/**
 * Creates a paginated response
 */
function createPaginatedResponse(data, page, limit, total, message, requestId) {
    return {
        success: true,
        data,
        message,
        pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
        },
        timestamp: new Date().toISOString(),
        requestId
    };
}
/**
 * Format error for API responses with proper HTTP status codes
 */
function formatApiError(error, defaultStatusCode = 500, requestId) {
    let statusCode = defaultStatusCode;
    let errorCode = 'INTERNAL_ERROR';
    let errorMessage = 'Internal server error';
    if (error instanceof Error) {
        errorMessage = error.message;
        // Map specific error types to HTTP status codes
        if (error.name === 'ValidationError') {
            statusCode = 400;
            errorCode = 'VALIDATION_ERROR';
        }
        else if (error.name === 'UnauthorizedError') {
            statusCode = 401;
            errorCode = 'UNAUTHORIZED';
        }
        else if (error.name === 'ForbiddenError') {
            statusCode = 403;
            errorCode = 'FORBIDDEN';
        }
        else if (error.name === 'NotFoundError') {
            statusCode = 404;
            errorCode = 'NOT_FOUND';
        }
        else if (error.name === 'ConflictError') {
            statusCode = 409;
            errorCode = 'CONFLICT';
        }
        else if (error.name === 'RateLimitError') {
            statusCode = 429;
            errorCode = 'RATE_LIMIT_EXCEEDED';
        }
    }
    // Check if error has custom status code
    if (typeof error.statusCode === 'number') {
        statusCode = error.statusCode;
    }
    const response = createErrorResponse(errorMessage, errorCode, requestId);
    return { response, statusCode };
}
/**
 * Middleware function to standardize all API responses
 */
function responseFormatterMiddleware() {
    return {
        success: createSuccessResponse,
        error: createErrorResponse,
        validationError: createValidationErrorResponse,
        paginated: createPaginatedResponse,
        formatError: formatApiError
    };
}
/**
 * Health check response format
 */
function createHealthResponse(status, checks, requestId) {
    return {
        success: status === 'healthy',
        data: {
            status,
            checks,
            uptime: process.uptime(),
            version: process.env.npm_package_version || '1.0.0'
        },
        timestamp: new Date().toISOString(),
        requestId
    };
}
//# sourceMappingURL=response-formatter.js.map