"use strict";
/**
 * Timer Management Utility
 * Prevents memory leaks by tracking and cleaning up timers and intervals
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.timerManager = exports.TimerManager = void 0;
class TimerManager {
    static instance;
    timers = new Map();
    intervals = new Map();
    constructor() { }
    static getInstance() {
        if (!TimerManager.instance) {
            TimerManager.instance = new TimerManager();
        }
        return TimerManager.instance;
    }
    /**
     * Create a managed timeout
     */
    setTimeout(id, callback, delay) {
        // Clear existing timer with same ID
        this.clearTimeout(id);
        const timer = setTimeout(() => {
            callback();
            this.timers.delete(id);
        }, delay);
        this.timers.set(id, timer);
        return timer;
    }
    /**
     * Create a managed interval
     */
    setInterval(id, callback, delay) {
        // Clear existing interval with same ID
        this.clearInterval(id);
        const interval = setInterval(callback, delay);
        this.intervals.set(id, interval);
        return interval;
    }
    /**
     * Clear a specific timeout
     */
    clearTimeout(id) {
        const timer = this.timers.get(id);
        if (timer) {
            clearTimeout(timer);
            this.timers.delete(id);
        }
    }
    /**
     * Clear a specific interval
     */
    clearInterval(id) {
        const interval = this.intervals.get(id);
        if (interval) {
            clearInterval(interval);
            this.intervals.delete(id);
        }
    }
    /**
     * Clear all timers for a specific session
     */
    clearSessionTimers(sessionId) {
        const sessionPrefix = `${sessionId}_`;
        // Clear timers
        for (const [id, timer] of this.timers.entries()) {
            if (id.startsWith(sessionPrefix)) {
                clearTimeout(timer);
                this.timers.delete(id);
            }
        }
        // Clear intervals
        for (const [id, interval] of this.intervals.entries()) {
            if (id.startsWith(sessionPrefix)) {
                clearInterval(interval);
                this.intervals.delete(id);
            }
        }
    }
    /**
     * Clear all timers and intervals
     */
    clearAll() {
        // Clear all timers
        for (const [id, timer] of this.timers.entries()) {
            clearTimeout(timer);
        }
        this.timers.clear();
        // Clear all intervals
        for (const [id, interval] of this.intervals.entries()) {
            clearInterval(interval);
        }
        this.intervals.clear();
    }
    /**
     * Get statistics about active timers
     */
    getStats() {
        return {
            timers: this.timers.size,
            intervals: this.intervals.size,
            activeIds: [...this.timers.keys(), ...this.intervals.keys()]
        };
    }
}
exports.TimerManager = TimerManager;
// Export singleton instance
exports.timerManager = TimerManager.getInstance();
//# sourceMappingURL=timer-manager.js.map