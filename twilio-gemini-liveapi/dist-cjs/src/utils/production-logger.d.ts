/**
 * Production-safe logger to replace console.log statements
 * Prevents excessive logging in production while maintaining debug capabilities
 */
export declare class ProductionLogger {
    private isDevelopment;
    private isDebugEnabled;
    /**
     * Info level logging - always shown
     */
    info(message: string, ...args: any[]): void;
    /**
     * Debug level logging - only in development or when debug enabled
     */
    debug(message: string, ...args: any[]): void;
    /**
     * Warning level logging - always shown
     */
    warn(message: string, ...args: any[]): void;
    /**
     * Error level logging - always shown
     */
    error(message: string, ...args: any[]): void;
    /**
     * Replace console.log with proper logging
     * @deprecated Use debug() instead
     */
    log(message: string, ...args: any[]): void;
    /**
     * Critical system events that should always be logged
     */
    system(message: string, ...args: any[]): void;
    /**
     * Performance metrics that should be tracked
     */
    performance(message: string, metrics: Record<string, any>): void;
    /**
     * Security-related events that should always be logged
     */
    security(message: string, ...args: any[]): void;
}
export declare const productionLogger: ProductionLogger;
export declare const debugLog: (message: string, ...args: any[]) => void;
export declare const infoLog: (message: string, ...args: any[]) => void;
export declare const warnLog: (message: string, ...args: any[]) => void;
export declare const errorLog: (message: string, ...args: any[]) => void;
export declare const systemLog: (message: string, ...args: any[]) => void;
export declare const perfLog: (message: string, metrics: Record<string, any>) => void;
export declare const securityLog: (message: string, ...args: any[]) => void;
//# sourceMappingURL=production-logger.d.ts.map