"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.twilioValidator = exports.TwilioWebhookValidator = void 0;
exports.validateTwilioWebhook = validateTwilioWebhook;
const crypto_1 = __importDefault(require("crypto"));
const config_1 = require("../config/config");
const security_utils_1 = require("../middleware/security-utils");
const logger_1 = require("../utils/logger");
/**
 * Validate Twilio webhook signatures to ensure requests are from Twilio
 */
class TwilioWebhookValidator {
    authToken;
    skipValidation;
    metrics;
    suspiciousIPs = new Set();
    constructor() {
        this.authToken = (0, config_1.getConfigValue)('twilio.authToken', '') || '';
        // Only allow skipping validation in test environment for security
        this.skipValidation = ((0, config_1.getConfigValue)('twilio.skipWebhookValidation', false) || false) && process.env.NODE_ENV === 'test';
        this.metrics = {
            totalValidations: 0,
            successfulValidations: 0,
            failedValidations: 0,
            suspiciousRequests: 0,
            lastReset: Date.now()
        };
    }
    /**
     * Validate a Twilio webhook request
     * @param signature - The X-Twilio-Signature header value
     * @param url - The full URL of the webhook endpoint
     * @param params - The request body parameters
     * @returns Whether the signature is valid
     */
    validateRequest(signature, url, params, clientIP) {
        this.metrics.totalValidations++;
        // Enhanced validation for production security
        if (!this.authToken) {
            logger_1.websocketLogger.error('Twilio auth token not configured');
            this.metrics.failedValidations++;
            return false;
        }
        // Only skip validation in test environment for security
        if (this.skipValidation && process.env.NODE_ENV === 'test') {
            console.warn('⚠️ Twilio webhook signature validation is disabled for testing');
            this.metrics.successfulValidations++;
            return true;
        }
        // Always validate in production and development for security
        if (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'development') {
            console.log('🔒 Enforcing Twilio webhook signature validation');
        }
        // Enhanced signature validation
        if (!signature || typeof signature !== 'string') {
            logger_1.websocketLogger.error('Missing or invalid Twilio signature header', { clientIP });
            this.metrics.failedValidations++;
            if (clientIP) {
                this.markSuspicious(clientIP);
            }
            return false;
        }
        if (!url || typeof url !== 'string') {
            logger_1.websocketLogger.error('Missing or invalid URL for Twilio validation', { clientIP });
            this.metrics.failedValidations++;
            return false;
        }
        if (!params || typeof params !== 'object') {
            logger_1.websocketLogger.error('Missing or invalid request parameters for Twilio validation', { clientIP });
            this.metrics.failedValidations++;
            if (clientIP) {
                this.markSuspicious(clientIP);
            }
            return false;
        }
        // Validate required Twilio parameters
        if (!this.validateTwilioParams(params, clientIP)) {
            return false;
        }
        try {
            // Sort the POST parameters alphabetically by key
            const sortedParams = Object.keys(params || {})
                .sort()
                .reduce((acc, key) => {
                acc[key] = params[key];
                return acc;
            }, {});
            // Build the validation string
            let validationString = url;
            for (const [key, value] of Object.entries(sortedParams)) {
                validationString += key + (value || '');
            }
            // Calculate the expected signature
            const expectedSignature = crypto_1.default
                .createHmac('sha1', this.authToken)
                .update(validationString)
                .digest('base64');
            const providedBuf = Buffer.from(signature);
            const expectedBuf = Buffer.from(expectedSignature);
            // If lengths differ timingSafeEqual would throw
            const isValid = providedBuf.length === expectedBuf.length &&
                crypto_1.default.timingSafeEqual(providedBuf, expectedBuf);
            if (!isValid) {
                logger_1.websocketLogger.error('Twilio webhook signature validation failed', {
                    clientIP,
                    expected: expectedSignature.substring(0, 10) + '...',
                    received: signature.substring(0, 10) + '...'
                });
                this.metrics.failedValidations++;
                if (clientIP) {
                    this.markSuspicious(clientIP);
                }
            }
            else {
                this.metrics.successfulValidations++;
            }
            return isValid;
        }
        catch (error) {
            logger_1.websocketLogger.error('Error validating Twilio webhook signature', error instanceof Error ? error : new Error(String(error)));
            this.metrics.failedValidations++;
            if (clientIP) {
                this.markSuspicious(clientIP);
            }
            return false;
        }
    }
    /**
     * Validate Twilio-specific parameters
     */
    validateTwilioParams(params, clientIP) {
        // Validate CallSid format
        if (params.CallSid && !security_utils_1.SecurityUtils.sanitizeCallSid(params.CallSid)) {
            logger_1.websocketLogger.warn('Invalid CallSid format in Twilio webhook', {
                callSid: params.CallSid?.substring(0, 10) + '...',
                clientIP
            });
            this.metrics.suspiciousRequests++;
            if (clientIP) {
                this.markSuspicious(clientIP);
            }
            return false;
        }
        // Validate phone numbers
        if (params.From && !security_utils_1.SecurityUtils.validatePhoneNumber(params.From)) {
            logger_1.websocketLogger.warn('Invalid From phone number in Twilio webhook', {
                from: params.From,
                clientIP
            });
            this.metrics.suspiciousRequests++;
            return false;
        }
        if (params.To && !security_utils_1.SecurityUtils.validatePhoneNumber(params.To)) {
            logger_1.websocketLogger.warn('Invalid To phone number in Twilio webhook', {
                to: params.To,
                clientIP
            });
            this.metrics.suspiciousRequests++;
            return false;
        }
        return true;
    }
    /**
     * Mark IP as suspicious
     */
    markSuspicious(clientIP) {
        this.suspiciousIPs.add(clientIP);
        this.metrics.suspiciousRequests++;
        if (this.suspiciousIPs.size > 100) {
            // Clear old suspicious IPs if too many
            this.suspiciousIPs.clear();
        }
    }
    /**
     * Get validation metrics
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Reset validation metrics
     */
    resetMetrics() {
        this.metrics = {
            totalValidations: 0,
            successfulValidations: 0,
            failedValidations: 0,
            suspiciousRequests: 0,
            lastReset: Date.now()
        };
    }
    /**
     * Express/Fastify middleware for validating Twilio webhooks
     */
    middleware() {
        return async (request, reply) => {
            // Skip validation for non-Twilio endpoints
            const twilioEndpoints = ['/incoming-call', '/call-status', '/voice-status'];
            if (!twilioEndpoints.includes(request.url)) {
                return;
            }
            const signature = request.headers['x-twilio-signature'];
            const fullUrl = `${request.protocol}://${request.headers.host}${request.url}`;
            if (!this.validateRequest(signature, fullUrl, request.body)) {
                reply.code(403).send({ error: 'Invalid Twilio signature' });
                return;
            }
        };
    }
}
exports.TwilioWebhookValidator = TwilioWebhookValidator;
// Export singleton instance
exports.twilioValidator = new TwilioWebhookValidator();
/**
 * Validate a Twilio webhook request
 * @param request - The HTTP request object
 * @returns Whether the request is valid
 */
function validateTwilioWebhook(request) {
    const signature = request.headers['x-twilio-signature'];
    const fullUrl = `${request.protocol}://${request.headers.host}${request.url}`;
    // Get client IP for enhanced security tracking
    const clientIP = request.socket.remoteAddress ||
        request.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
        'unknown';
    return exports.twilioValidator.validateRequest(signature, fullUrl, request.body, clientIP);
}
//# sourceMappingURL=twilio-validation.js.map