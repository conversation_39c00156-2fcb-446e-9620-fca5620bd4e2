/**
 * Structured Logger for Twilio Gemini Live API
 *
 * Provides consistent logging across all components with:
 * - CallSid/SessionId correlation
 * - Component-based categorization
 * - Performance timing
 * - Audio quality metrics
 * - Production-ready JSON output
 */
/**
 * Log levels in order of severity
 */
export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}
/**
 * Component identifiers for structured logging
 */
export declare enum Component {
    GEMINI = "GEMINI",
    TWILIO = "TWILIO",
    AUDIO = "AUDIO",
    SESSION = "SESSION",
    API = "API",
    CONFIG = "CONFIG",
    WEBSOCKET = "WEBSOCKET",
    RECOVERY = "RECOVERY",
    HEALTH = "HEALTH",
    AUTH = "AUTH",
    DEEPGRAM = "DEEPGRAM"
}
interface LoggerOptions {
    level?: LogLevel;
    json?: boolean;
    colors?: boolean;
    component?: Component | null;
}
interface ErrorData {
    error?: Error | {
        message: string;
        stack?: string;
        name?: string;
    };
    [key: string]: any;
}
/**
 * Performance timer for measuring operation duration
 */
declare class PerformanceTimer {
    private operation;
    private logger;
    private callSid;
    private component;
    private startTime;
    constructor(operation: string, logger: Logger, callSid?: string | null, component?: Component | null);
    end(additionalData?: Record<string, any>): number;
}
/**
 * Main Logger class
 */
export declare class Logger {
    private level;
    private enableJson;
    private enableColors;
    private component;
    private performanceMetrics;
    private audioMetrics;
    private levelEmojis;
    private componentEmojis;
    constructor(options?: LoggerOptions);
    /**
     * Create a child logger with a specific component
     */
    child(component: Component): Logger;
    /**
     * Format log message for output
     */
    private formatMessage;
    /**
     * Log at specified level
     */
    private log;
    /**
     * Debug level logging
     */
    debug(message: string, data?: Record<string, any>, callSid?: string | null, component?: Component | null): void;
    /**
     * Info level logging
     */
    info(message: string, data?: Record<string, any>, callSid?: string | null, component?: Component | null): void;
    /**
     * Warning level logging
     */
    warn(message: string, data?: Record<string, any>, callSid?: string | null, component?: Component | null): void;
    /**
     * Error level logging
     */
    error(message: string, data?: ErrorData | Error, callSid?: string | null, component?: Component | null): void;
    /**
     * Start performance timing
     */
    startTimer(operation: string, callSid?: string | null, component?: Component | null): PerformanceTimer;
    /**
     * Log API call performance
     */
    logApiCall(method: string, url: string, statusCode: number, duration: number, callSid?: string | null, component?: Component): void;
    /**
     * Set log level dynamically
     */
    setLevel(level: LogLevel): void;
    /**
     * Get current log level
     */
    getLevel(): LogLevel;
    /**
     * Check if a level would be logged
     */
    isLevelEnabled(level: LogLevel): boolean;
}
export declare const logger: Logger;
export declare const geminiLogger: Logger;
export declare const twilioLogger: Logger;
export declare const audioLogger: Logger;
export declare const sessionLogger: Logger;
export declare const apiLogger: Logger;
export declare const configLogger: Logger;
export declare const websocketLogger: Logger;
export declare const recoveryLogger: Logger;
export declare const healthLogger: Logger;
export declare const authLogger: Logger;
/**
 * Global logging control utilities
 */
export declare const LoggingControl: {
    /**
     * Set log level for all loggers
     */
    setGlobalLevel(level: LogLevel): void;
    /**
     * Set production mode (INFO level, JSON output)
     */
    setProductionMode(): void;
    /**
     * Set development mode (DEBUG level, console output)
     */
    setDevelopmentMode(): void;
    /**
     * Set quiet mode (ERROR level only)
     */
    setQuietMode(): void;
    /**
     * Get current global log level
     */
    getGlobalLevel(): LogLevel;
};
export { PerformanceTimer };
//# sourceMappingURL=logger.d.ts.map