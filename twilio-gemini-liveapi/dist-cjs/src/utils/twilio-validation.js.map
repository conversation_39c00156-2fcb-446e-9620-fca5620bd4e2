{"version": 3, "file": "twilio-validation.js", "sourceRoot": "", "sources": ["../../../src/utils/twilio-validation.ts"], "names": [], "mappings": ";;;;;;AA4PA,sDAUC;AAtQD,oDAA4B;AAC5B,6CAA0D;AAE1D,iEAA6D;AAC7D,4CAAkD;AAelD;;GAEG;AACH,MAAa,sBAAsB;IACzB,SAAS,CAAS;IAClB,cAAc,CAAU;IACxB,OAAO,CAAoB;IAC3B,aAAa,GAAgB,IAAI,GAAG,EAAE,CAAC;IAE/C;QACE,IAAI,CAAC,SAAS,GAAG,IAAA,uBAAc,EAAS,kBAAkB,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;QACtE,kEAAkE;QAClE,IAAI,CAAC,cAAc,GAAG,CAAC,IAAA,uBAAc,EAAU,8BAA8B,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC;QAEnI,IAAI,CAAC,OAAO,GAAG;YACb,gBAAgB,EAAE,CAAC;YACnB,qBAAqB,EAAE,CAAC;YACxB,iBAAiB,EAAE,CAAC;YACpB,kBAAkB,EAAE,CAAC;YACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,eAAe,CACb,SAA6B,EAC7B,GAAW,EACX,MAAuC,EACvC,QAAiB;QAEjB,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAEhC,8CAA8C;QAC9C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,wBAAe,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;YAC1D,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,wDAAwD;QACxD,IAAI,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;YAC/E,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,6DAA6D;QAC7D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YACpF,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAClE,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YAChD,wBAAe,CAAC,KAAK,CAAC,4CAA4C,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAClF,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACjC,IAAI,QAAQ,EAAE,CAAC;gBAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAAA,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YACpC,wBAAe,CAAC,KAAK,CAAC,8CAA8C,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACpF,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC1C,wBAAe,CAAC,KAAK,CAAC,6DAA6D,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACnG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACjC,IAAI,QAAQ,EAAE,CAAC;gBAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAAA,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,iDAAiD;YACjD,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;iBAC3C,IAAI,EAAE;iBACN,MAAM,CAAC,CAAC,GAAwB,EAAE,GAAG,EAAE,EAAE;gBACxC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAO,CAAC,GAAG,CAAC,CAAC;gBACxB,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC,CAAC;YAET,8BAA8B;YAC9B,IAAI,gBAAgB,GAAG,GAAG,CAAC;YAC3B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;gBACxD,gBAAgB,IAAI,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;YAC1C,CAAC;YAED,mCAAmC;YACnC,MAAM,iBAAiB,GAAG,gBAAM;iBAC7B,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC;iBAClC,MAAM,CAAC,gBAAgB,CAAC;iBACxB,MAAM,CAAC,QAAQ,CAAC,CAAC;YAEpB,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEnD,gDAAgD;YAChD,MAAM,OAAO,GACX,WAAW,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM;gBACzC,gBAAM,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAEnD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,wBAAe,CAAC,KAAK,CAAC,4CAA4C,EAAE;oBAClE,QAAQ;oBACR,QAAQ,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;oBACpD,QAAQ,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;iBAC7C,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACjC,IAAI,QAAQ,EAAE,CAAC;oBAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAAA,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACvC,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,wBAAe,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9H,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACjC,IAAI,QAAQ,EAAE,CAAC;gBAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAAA,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAA2B,EAAE,QAAiB;QACzE,0BAA0B;QAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,8BAAa,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACrE,wBAAe,CAAC,IAAI,CAAC,0CAA0C,EAAE;gBAC/D,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;gBACjD,QAAQ;aACT,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,QAAQ,EAAE,CAAC;gBAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAAA,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,yBAAyB;QACzB,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,8BAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,wBAAe,CAAC,IAAI,CAAC,6CAA6C,EAAE;gBAClE,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,QAAQ;aACT,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,8BAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;YAC/D,wBAAe,CAAC,IAAI,CAAC,2CAA2C,EAAE;gBAChE,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,QAAQ;aACT,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,QAAgB;QACrC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;YAClC,uCAAuC;YACvC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,OAAO,GAAG;YACb,gBAAgB,EAAE,CAAC;YACnB,qBAAqB,EAAE,CAAC;YACxB,iBAAiB,EAAE,CAAC;YACpB,kBAAkB,EAAE,CAAC;YACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,KAAK,EAAE,OAA6B,EAAE,KAAmB,EAAiB,EAAE;YACjF,2CAA2C;YAC3C,MAAM,eAAe,GAAG,CAAC,gBAAgB,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;YAC5E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3C,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAuB,CAAC;YAC9E,MAAM,OAAO,GAAG,GAAG,OAAO,CAAC,QAAQ,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YAE9E,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,IAA2B,CAAC,EAAE,CAAC;gBACnF,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;gBAC5D,OAAO;YACT,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;CACF;AA5ND,wDA4NC;AAED,4BAA4B;AACf,QAAA,eAAe,GAAG,IAAI,sBAAsB,EAAE,CAAC;AAE5D;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,OAA6B;IACjE,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAuB,CAAC;IAC9E,MAAM,OAAO,GAAG,GAAG,OAAO,CAAC,QAAQ,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAE9E,+CAA+C;IAC/C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,aAAa;QAC1C,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAY,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE;QACrE,SAAS,CAAC;IAEZ,OAAO,uBAAe,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,IAA2B,EAAE,QAAQ,CAAC,CAAC;AAC5G,CAAC"}