export interface StandardResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    errors?: string[];
    message?: string;
    timestamp: string;
    requestId?: string;
    code?: string;
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
}
export interface PaginatedResponse<T> extends StandardResponse<T[]> {
    pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
/**
 * Creates a standardized success response
 */
export declare function createSuccessResponse<T>(data: T, message?: string, requestId?: string): StandardResponse<T>;
/**
 * Creates a standardized error response
 */
export declare function createErrorResponse(error: string | Error, code?: string, requestId?: string, statusCode?: number): StandardResponse;
/**
 * Creates a validation error response
 */
export declare function createValidationErrorResponse(errors: ValidationError[], requestId?: string): StandardResponse;
/**
 * Creates a paginated response
 */
export declare function createPaginatedResponse<T>(data: T[], page: number, limit: number, total: number, message?: string, requestId?: string): PaginatedResponse<T>;
/**
 * Format error for API responses with proper HTTP status codes
 */
export declare function formatApiError(error: any, defaultStatusCode?: number, requestId?: string): {
    response: StandardResponse;
    statusCode: number;
};
/**
 * Middleware function to standardize all API responses
 */
export declare function responseFormatterMiddleware(): {
    success: typeof createSuccessResponse;
    error: typeof createErrorResponse;
    validationError: typeof createValidationErrorResponse;
    paginated: typeof createPaginatedResponse;
    formatError: typeof formatApiError;
};
/**
 * Health check response format
 */
export declare function createHealthResponse(status: 'healthy' | 'unhealthy' | 'degraded', checks: Record<string, {
    status: string;
    message?: string;
    timestamp: string;
}>, requestId?: string): StandardResponse;
//# sourceMappingURL=response-formatter.d.ts.map