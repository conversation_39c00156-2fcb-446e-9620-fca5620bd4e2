"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.voiceManager = exports.VoiceManager = void 0;
// Voice Management System for Gemini
const config_1 = require("../config/config");
class VoiceManager {
    defaultVoice;
    voiceSelectionEnabled;
    availableVoices;
    voiceMapping;
    constructor() {
        this.defaultVoice = (0, config_1.getConfigValue)('ai.gemini.defaultVoice', 'Kore');
        this.voiceSelectionEnabled = (0, config_1.getConfigValue)('ai.gemini.voiceSelectionEnabled', false);
        // Available Gemini voices with characteristics (Updated June 11, 2025)
        this.availableVoices = {
            'Aoede': {
                name: '<PERSON>oede',
                gender: 'Female',
                characteristics: 'bright, neutral narrator',
                pitch: 'neutral',
                timbre: 'bright',
                persona: 'narrator'
            },
            'Puck': {
                name: 'Puck',
                gender: 'Male',
                characteristics: 'lively, higher tenor',
                pitch: 'higher',
                timbre: 'lively',
                persona: 'energetic'
            },
            '<PERSON>ron': {
                name: '<PERSON><PERSON>',
                gender: 'Male',
                characteristics: 'deep, warm baritone',
                pitch: 'deep',
                timbre: 'warm',
                persona: 'authoritative'
            },
            '<PERSON>re': {
                name: '<PERSON>re',
                gender: 'Female',
                characteristics: 'soft alto, empathetic',
                pitch: 'alto',
                timbre: 'soft',
                persona: 'empathetic'
            },
            'Fenrir': {
                name: 'Fenrir',
                gender: 'Male',
                characteristics: 'assertive mid-range',
                pitch: 'mid-range',
                timbre: 'assertive',
                persona: 'confident'
            },
            'Leda': {
                name: 'Leda',
                gender: 'Female',
                characteristics: 'clear RP-style announcer',
                pitch: 'clear',
                timbre: 'professional',
                persona: 'announcer'
            },
            'Orus': {
                name: 'Orus',
                gender: 'Male',
                characteristics: 'relaxed, breathy tenor',
                pitch: 'tenor',
                timbre: 'breathy',
                persona: 'relaxed'
            },
            'Zephyr': {
                name: 'Zephyr',
                gender: 'Female',
                characteristics: 'airy, youthful soprano',
                pitch: 'soprano',
                timbre: 'airy',
                persona: 'youthful'
            }
        };
        // Voice mapping for different languages/accents and compatibility
        this.voiceMapping = {
            // OpenAI voice mappings (for compatibility)
            'shimmer': 'Orus', // shimmer → Gemini Orus (relaxed, breathy tenor)
            'alloy': 'Puck', // alloy → Gemini Puck (lively, higher tenor)
            'echo': 'Charon', // echo → Gemini Charon (deep, warm baritone)
            'fable': 'Kore', // fable → Gemini Kore (soft alto, empathetic)
            'onyx': 'Fenrir', // onyx → Gemini Fenrir (assertive mid-range)
            'nova': 'Aoede', // nova → Gemini Aoede (bright, neutral narrator)
            // Gender-based mappings
            'female': 'Kore', // Default female → empathetic
            'male': 'Orus', // Default male → relaxed
            'professional': 'Leda', // Professional → RP-style announcer
            'youthful': 'Zephyr', // Youthful → airy, youthful soprano
            'authoritative': 'Charon', // Authoritative → deep, warm baritone
            'energetic': 'Puck', // Energetic → lively, higher tenor
            'empathetic': 'Kore', // Empathetic → soft alto, empathetic
            'relaxed': 'Orus' // Relaxed → relaxed, breathy tenor
        };
    }
    /**
     * Get all available voices
     */
    getAvailableVoices() {
        return this.availableVoices;
    }
    /**
     * Get default voice
     */
    getDefaultVoice() {
        return this.defaultVoice;
    }
    /**
     * Check if voice selection is enabled
     */
    isVoiceSelectionEnabled() {
        return this.voiceSelectionEnabled;
    }
    /**
     * Get voice mapping
     */
    getVoiceMapping() {
        return this.voiceMapping;
    }
    /**
     * Validate and get valid Gemini voice
     */
    getValidGeminiVoice(requestedVoice) {
        if (!requestedVoice) {
            console.log(`🎤 No voice specified, using default: ${this.defaultVoice} (${this.availableVoices[this.defaultVoice]?.characteristics || 'unknown'})`);
            return this.defaultVoice;
        }
        // Check if it's already a valid Gemini voice
        if (this.availableVoices[requestedVoice]) {
            const voiceInfo = this.availableVoices[requestedVoice];
            console.log(`🎤 Using requested voice: ${requestedVoice} (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
            return requestedVoice;
        }
        // Check if it's an OpenAI voice or other mapping that needs conversion
        if (this.voiceMapping[requestedVoice]) {
            const mappedVoice = this.voiceMapping[requestedVoice];
            const voiceInfo = this.availableVoices[mappedVoice];
            console.log(`🎤 Mapped voice '${requestedVoice}' → '${mappedVoice}' (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
            return mappedVoice;
        }
        // Default fallback
        const defaultInfo = this.availableVoices[this.defaultVoice];
        console.log(`⚠️ Unknown voice '${requestedVoice}', using default: ${this.defaultVoice} (${defaultInfo?.characteristics || 'unknown'})`);
        return this.defaultVoice;
    }
    /**
     * Get voice information
     */
    getVoiceCharacteristics(voiceName) {
        return this.availableVoices[voiceName] || null;
    }
    /**
     * Set default voice
     */
    setDefaultVoice(voiceName) {
        const validVoice = this.getValidGeminiVoice(voiceName);
        if (validVoice && this.availableVoices[validVoice]) {
            this.defaultVoice = validVoice;
            console.log(`🎤 Default voice changed to: ${validVoice}`);
            return true;
        }
        return false;
    }
    /**
     * Get voice configuration for API response
     */
    getVoiceConfig() {
        return {
            defaultVoice: this.defaultVoice,
            availableVoices: this.availableVoices,
            voiceMapping: this.voiceMapping,
            voiceSelectionEnabled: this.voiceSelectionEnabled,
            totalVoices: Object.keys(this.availableVoices).length
        };
    }
    /**
     * Validate voice name
     */
    validateVoice(voiceName) {
        if (!voiceName) {
            return {
                isValid: false,
                error: 'Voice name is required',
                suggestion: this.defaultVoice
            };
        }
        if (this.availableVoices[voiceName]) {
            return {
                isValid: true,
                voice: voiceName,
                info: this.availableVoices[voiceName]
            };
        }
        if (this.voiceMapping[voiceName]) {
            const mappedVoice = this.voiceMapping[voiceName];
            return {
                isValid: true,
                voice: mappedVoice,
                mapped: true,
                originalVoice: voiceName,
                info: this.availableVoices[mappedVoice]
            };
        }
        return {
            isValid: false,
            error: `Unknown voice '${voiceName}'`,
            suggestion: this.defaultVoice,
            availableVoices: Object.keys(this.availableVoices),
            voiceMapping: Object.keys(this.voiceMapping)
        };
    }
}
exports.VoiceManager = VoiceManager;
// Create singleton instance
exports.voiceManager = new VoiceManager();
//# sourceMappingURL=voice-manager.js.map