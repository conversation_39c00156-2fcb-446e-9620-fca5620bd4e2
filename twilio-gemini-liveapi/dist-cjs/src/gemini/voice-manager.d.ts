import type { VoiceCharacteristics } from '../types/shared-types';
interface VoiceValidationResult {
    isValid: boolean;
    voice?: string;
    info?: VoiceCharacteristics;
    mapped?: boolean;
    originalVoice?: string;
    error?: string;
    suggestion?: string;
    availableVoices?: string[];
    voiceMapping?: string[];
}
interface VoiceManagerConfig {
    defaultVoice: string;
    availableVoices: Record<string, VoiceCharacteristics>;
    voiceMapping: Record<string, string>;
    voiceSelectionEnabled: boolean;
    totalVoices: number;
}
export declare class VoiceManager {
    private defaultVoice;
    private voiceSelectionEnabled;
    private availableVoices;
    private voiceMapping;
    constructor();
    /**
     * Get all available voices
     */
    getAvailableVoices(): Record<string, VoiceCharacteristics>;
    /**
     * Get default voice
     */
    getDefaultVoice(): string;
    /**
     * Check if voice selection is enabled
     */
    isVoiceSelectionEnabled(): boolean;
    /**
     * Get voice mapping
     */
    getVoiceMapping(): Record<string, string>;
    /**
     * Validate and get valid Gemini voice
     */
    getValidGeminiVoice(requestedVoice?: string): string;
    /**
     * Get voice information
     */
    getVoiceCharacteristics(voiceName: string): VoiceCharacteristics | null;
    /**
     * Set default voice
     */
    setDefaultVoice(voiceName: string): boolean;
    /**
     * Get voice configuration for API response
     */
    getVoiceConfig(): VoiceManagerConfig;
    /**
     * Validate voice name
     */
    validateVoice(voiceName: string): VoiceValidationResult;
}
export declare const voiceManager: VoiceManager;
export {};
//# sourceMappingURL=voice-manager.d.ts.map