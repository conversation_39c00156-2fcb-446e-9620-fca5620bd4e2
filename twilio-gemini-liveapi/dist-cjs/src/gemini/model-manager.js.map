{"version": 3, "file": "model-manager.js", "sourceRoot": "", "sources": ["../../../src/gemini/model-manager.ts"], "names": [], "mappings": ";;;AAAA,qCAAqC;AACrC,6CAA0D;AA4C1D,MAAa,YAAY;IACb,YAAY,CAAS;IACrB,qBAAqB,CAAU;IAC/B,gBAAgB,CAAkC;IAClD,eAAe,CAAkC;IACjD,YAAY,CAAS;IAE7B;QACI,IAAI,CAAC,YAAY,GAAG,IAAA,uBAAc,EAAC,wBAAwB,EAAE,8CAA8C,CAAW,CAAC;QACvH,IAAI,CAAC,qBAAqB,GAAG,IAAA,uBAAc,EAAC,iCAAiC,EAAE,KAAK,CAAY,CAAC;QAEjG,0DAA0D;QAC1D,IAAI,CAAC,gBAAgB,GAAG;YACpB,8CAA8C,EAAE;gBAC5C,IAAI,EAAE,wCAAwC;gBAC9C,WAAW,EAAE,wGAAwG;gBACrH,MAAM,EAAE,iBAAiB;gBACzB,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;aACnB;YACD,2BAA2B,EAAE;gBACzB,IAAI,EAAE,6BAA6B;gBACnC,WAAW,EAAE,6EAA6E;gBAC1F,MAAM,EAAE,6BAA6B;gBACrC,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,0BAA0B;aACrC;YACD,kBAAkB,EAAE;gBAChB,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,wDAAwD;gBACrE,MAAM,EAAE,sBAAsB;gBAC9B,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,QAAQ;aACnB;YACD,qCAAqC,EAAE;gBACnC,IAAI,EAAE,uCAAuC;gBAC7C,WAAW,EAAE,yDAAyD;gBACtE,MAAM,EAAE,gBAAgB;gBACxB,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,aAAa;aACxB;SACJ,CAAC;QAEF,wCAAwC;QACxC,MAAM,iBAAiB,GAAG,IAAA,uBAAc,EAAC,2BAA2B,EAChE,CAAC,8CAA8C,EAAE,2BAA2B,CAAC,CAAa,CAAC;QAE/F,+DAA+D;QAC/D,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAChC,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACjC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACnE,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,CAAC,mDAAmD,OAAO,EAAE,CAAC,CAAC;YAC/E,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,qDAAqD;QACrD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3C,OAAO,CAAC,KAAK,CAAC,oBAAoB,IAAI,CAAC,YAAY,kDAAkD,CAAC,CAAC;YACvG,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,8CAA8C,CAAC;YAC7G,OAAO,CAAC,GAAG,CAAC,4BAA4B,aAAa,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,kBAAkB;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,eAAe;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,eAAe;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,uBAAuB;QACnB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,cAAuB;QACvC,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,yCAAyC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B,CAAC;QAED,qCAAqC;QACrC,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,6BAA6B,cAAc,EAAE,CAAC,CAAC;YAC3D,OAAO,cAAc,CAAC;QAC1B,CAAC;QAED,mBAAmB;QACnB,OAAO,CAAC,GAAG,CAAC,qBAAqB,cAAc,qBAAqB,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QACzF,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAiB;QAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,SAAiB;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;YACjD,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC;YACxC,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,sBAAsB,aAAa,QAAQ,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;YAC7E,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;YACzH,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,cAAc;QACV,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM;YACrD,mBAAmB,EAAE;gBACjB,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW;gBAC5E,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW;aACrF;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,SAAiB;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB;gBAC/B,UAAU,EAAE,IAAI,CAAC,YAAY;aAChC,CAAC;QACN,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAClC,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;aACxC,CAAC;QACN,CAAC;QAED,OAAO;YACH,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,kBAAkB,SAAS,GAAG;YACrC,UAAU,EAAE,IAAI,CAAC,YAAY;YAC7B,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC;SACrD,CAAC;IACN,CAAC;IAED;;OAEG;IACH,aAAa;QACT,OAAO;YACH,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM;YACxD,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,oBAAoB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAC1D,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,aAAa,CACzD,CAAC,MAAM;YACR,cAAc,EAAE;gBACZ,sBAAsB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAC5D,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,sBAAsB,CAC7E,CAAC,MAAM;gBACR,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CACtD,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,gBAAgB,CAAC,CAC9E,CAAC,MAAM;gBACR,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CACvD,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,iBAAiB,CAAC,CAC/E,CAAC,MAAM;aACX;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,UAAkB,OAAO;QACzC,QAAQ,OAAO,EAAE,CAAC;YACd,KAAK,OAAO;gBACR,0CAA0C;gBAC1C,IAAI,IAAI,CAAC,eAAe,CAAC,8CAA8C,CAAC,EAAE,CAAC;oBACvE,OAAO,8CAA8C,CAAC;gBAC1D,CAAC;gBACD,IAAI,IAAI,CAAC,eAAe,CAAC,2BAA2B,CAAC,EAAE,CAAC;oBACpD,OAAO,2BAA2B,CAAC;gBACvC,CAAC;gBACD,MAAM;YACV,KAAK,MAAM;gBACP,qCAAqC;gBACrC,OAAO,IAAI,CAAC,YAAY,CAAC;YAC7B,KAAK,SAAS,CAAC;YACf;gBACI,OAAO,IAAI,CAAC,YAAY,CAAC;QACjC,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACZ,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpF,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;QAC1F,OAAO,CAAC,GAAG,CAAC,4BAA4B,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;IAC9G,CAAC;CACJ;AAtPD,oCAsPC;AAED,4BAA4B;AACf,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC"}