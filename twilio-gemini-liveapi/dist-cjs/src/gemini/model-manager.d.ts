interface ModelDefinition {
    name: string;
    description: string;
    status: string;
    supportsAudio: boolean;
    quality: string;
    region: string;
}
interface ModelValidationResult {
    isValid: boolean;
    model?: string;
    info?: ModelDefinition;
    error?: string;
    suggestion?: string;
    availableModels?: string[];
}
interface ModelConfig {
    defaultModel: string;
    currentModel: string;
    availableModels: Record<string, ModelDefinition>;
    modelSelectionEnabled: boolean;
    totalModels: number;
    configurationSource: {
        defaultModel: string;
        availableModels: string;
    };
}
interface ModelStats {
    totalAvailable: number;
    currentModel: string;
    defaultModel: string;
    audioSupportedModels: number;
    modelsByStatus: {
        'General availability': number;
        'Public preview': number;
        'Private preview': number;
    };
}
export declare class ModelManager {
    private defaultModel;
    private modelSelectionEnabled;
    private modelDefinitions;
    private availableModels;
    private currentModel;
    constructor();
    /**
     * Get all available models
     */
    getAvailableModels(): Record<string, ModelDefinition>;
    /**
     * Get default model
     */
    getDefaultModel(): string;
    /**
     * Get current model
     */
    getCurrentModel(): string;
    /**
     * Check if model selection is enabled
     */
    isModelSelectionEnabled(): boolean;
    /**
     * Validate and get valid Gemini model
     */
    getValidGeminiModel(requestedModel?: string): string;
    /**
     * Get model information
     */
    getModelInfo(modelName: string): ModelDefinition | null;
    /**
     * Set current model
     */
    setCurrentModel(modelName: string): boolean;
    /**
     * Get model configuration for API response
     */
    getModelConfig(): ModelConfig;
    /**
     * Validate model name
     */
    validateModel(modelName: string): ModelValidationResult;
    /**
     * Get model statistics
     */
    getModelStats(): ModelStats;
    /**
     * Get recommended model for specific use case
     */
    getRecommendedModel(useCase?: string): string;
    /**
     * Log current configuration
     */
    logConfiguration(): void;
}
export declare const modelManager: ModelManager;
export {};
//# sourceMappingURL=model-manager.d.ts.map