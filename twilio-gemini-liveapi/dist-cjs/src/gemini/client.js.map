{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../src/gemini/client.ts"], "names": [], "mappings": ";;;;;;AA4IA,kDA6BC;AAGD,kDAmBC;AAGD,wDAeC;AAjND,0DAAgC;AAChC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,eAAG,CAAC;AAkN7B,4BAAQ;AAjNjB,4CAAyD;AACzD,6CAA0D;AAc1D,gFAAgF;AACnE,QAAA,uBAAuB,GAAyC;IACzE,OAAO,EAAE;QACL,IAAI,EAAE,OAAO;QACb,MAAM,EAAE,QAAQ;QAChB,eAAe,EAAE,4CAA4C;QAC7D,WAAW,EAAE,mIAAmI;QAChJ,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,kBAAkB;QAC1B,OAAO,EAAE,uBAAuB;QAChC,OAAO,EAAE,0CAA0C;KACtD;IACD,MAAM,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,MAAM;QACd,eAAe,EAAE,2CAA2C;QAC5D,WAAW,EAAE,mHAAmH;QAChI,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,sBAAsB;QAC9B,OAAO,EAAE,2BAA2B;QACpC,OAAO,EAAE,0CAA0C;KACtD;IACD,QAAQ,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,MAAM,EAAE,MAAM;QACd,eAAe,EAAE,0CAA0C;QAC3D,WAAW,EAAE,+GAA+G;QAC5H,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,wBAAwB;QAChC,OAAO,EAAE,2BAA2B;QACpC,OAAO,EAAE,4CAA4C;KACxD;IACD,MAAM,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,QAAQ;QAChB,eAAe,EAAE,wCAAwC;QACzD,WAAW,EAAE,uHAAuH;QACpI,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,iBAAiB;QACzB,OAAO,EAAE,2BAA2B;QACpC,OAAO,EAAE,0CAA0C;KACtD;IACD,QAAQ,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,MAAM,EAAE,MAAM;QACd,eAAe,EAAE,sCAAsC;QACvD,WAAW,EAAE,iHAAiH;QAC9H,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,yBAAyB;QACjC,OAAO,EAAE,2BAA2B;QACpC,OAAO,EAAE,gDAAgD;KAC5D;IACD,MAAM,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,QAAQ;QAChB,eAAe,EAAE,mDAAmD;QACpE,WAAW,EAAE,0GAA0G;QACvH,KAAK,EAAE,sBAAsB;QAC7B,MAAM,EAAE,0BAA0B;QAClC,OAAO,EAAE,yBAAyB;QAClC,OAAO,EAAE,uDAAuD;KACnE;IACD,MAAM,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,MAAM;QACd,eAAe,EAAE,4CAA4C;QAC7D,WAAW,EAAE,6GAA6G;QAC1H,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,oBAAoB;QAC5B,OAAO,EAAE,2BAA2B;QACpC,OAAO,EAAE,wDAAwD;KACpE;IACD,QAAQ,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,MAAM,EAAE,QAAQ;QAChB,eAAe,EAAE,2CAA2C;QAC5D,WAAW,EAAE,mGAAmG;QAChH,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,mBAAmB;QAC3B,OAAO,EAAE,uBAAuB;QAChC,OAAO,EAAE,mDAAmD;KAC/D;CACJ,CAAC;AAEF,kEAAkE;AACrD,QAAA,aAAa,GAA2B;IACjD,4CAA4C;IAC5C,SAAS,EAAE,MAAM;IACjB,OAAO,EAAE,MAAM;IACf,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,MAAM;IACf,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,OAAO;IACf,wBAAwB;IACxB,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,MAAM;IACd,cAAc,EAAE,MAAM;IACtB,UAAU,EAAE,QAAQ;IACpB,eAAe,EAAE,QAAQ;IACzB,WAAW,EAAE,MAAM;CACtB,CAAC;AAEF,yCAAyC;AAC5B,QAAA,uBAAuB,GAA8B;IAC9D,8CAA8C,EAAE;QAC5C,IAAI,EAAE,wCAAwC;QAC9C,WAAW,EAAE,wGAAwG;QACrH,MAAM,EAAE,iBAAiB;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,MAAM,EAAE,QAAQ;KACnB;IACD,2BAA2B,EAAE;QACzB,IAAI,EAAE,6BAA6B;QACnC,WAAW,EAAE,6EAA6E;QAC1F,MAAM,EAAE,6BAA6B;QACrC,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,0BAA0B;KACrC;CACJ,CAAC;AAEF,2CAA2C;AAC3C,SAAgB,mBAAmB,CAAC,cAA8B,EAAE,YAA4B;IAC5F,IAAI,CAAC,YAAY,EAAE,CAAC;QAChB,YAAY,GAAG,IAAA,uBAAc,EAAC,wBAAwB,EAAE,MAAM,CAAW,CAAC;IAC9E,CAAC;IAED,IAAI,CAAC,cAAc,EAAE,CAAC;QAClB,qBAAM,CAAC,IAAI,CAAC,yCAAyC,YAAY,KAAK,+BAAuB,CAAC,YAAY,CAAC,EAAE,eAAe,IAAI,SAAS,GAAG,CAAC,CAAC;QAC9I,OAAO,YAAY,CAAC;IACxB,CAAC;IAED,6CAA6C;IAC7C,IAAI,+BAAuB,CAAC,cAAc,CAAC,EAAE,CAAC;QAC1C,MAAM,SAAS,GAAG,+BAAuB,CAAC,cAAc,CAAC,CAAC;QAC1D,qBAAM,CAAC,IAAI,CAAC,6BAA6B,cAAc,KAAK,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,eAAe,GAAG,CAAC,CAAC;QAC/G,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED,uEAAuE;IACvE,IAAI,qBAAa,CAAC,cAAc,CAAC,EAAE,CAAC;QAChC,MAAM,WAAW,GAAG,qBAAa,CAAC,cAAc,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,+BAAuB,CAAC,WAAW,CAAC,CAAC;QACvD,qBAAM,CAAC,IAAI,CAAC,oBAAoB,cAAc,QAAQ,WAAW,MAAM,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,eAAe,GAAG,CAAC,CAAC;QAC1H,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,mBAAmB;IACnB,MAAM,WAAW,GAAG,+BAAuB,CAAC,YAAY,CAAC,CAAC;IAC1D,qBAAM,CAAC,IAAI,CAAC,qBAAqB,cAAc,qBAAqB,YAAY,KAAK,WAAW,EAAE,eAAe,IAAI,SAAS,GAAG,CAAC,CAAC;IACnI,OAAO,YAAY,CAAC;AACxB,CAAC;AAED,4CAA4C;AAC5C,SAAgB,mBAAmB,CAAC,cAA8B,EAAE,YAA4B;IAC5F,IAAI,CAAC,YAAY,EAAE,CAAC;QAChB,YAAY,GAAG,IAAA,uBAAc,EAAC,wBAAwB,EAAE,8CAA8C,CAAW,CAAC;IACtH,CAAC;IAED,IAAI,CAAC,cAAc,EAAE,CAAC;QAClB,qBAAM,CAAC,IAAI,CAAC,yCAAyC,YAAY,EAAE,CAAC,CAAC;QACrE,OAAO,YAAY,CAAC;IACxB,CAAC;IAED,qCAAqC;IACrC,IAAI,+BAAuB,CAAC,cAAc,CAAC,EAAE,CAAC;QAC1C,qBAAM,CAAC,IAAI,CAAC,6BAA6B,cAAc,EAAE,CAAC,CAAC;QAC3D,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED,mBAAmB;IACnB,qBAAM,CAAC,IAAI,CAAC,qBAAqB,cAAc,qBAAqB,YAAY,EAAE,CAAC,CAAC;IACpF,OAAO,YAAY,CAAC;AACxB,CAAC;AAED,2BAA2B;AAC3B,SAAgB,sBAAsB,CAAC,MAAe;IAClD,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,qBAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,CAAC;QACD,iDAAiD;QACjD,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QACnD,qBAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACzD,OAAO,MAAM,CAAC;IAClB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,qBAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9G,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC"}