import pkg from '@google/genai';
declare const Modality: typeof pkg.Modality;
import type { VoiceCharacteristics } from '../types/shared-types';
interface ModelInfo {
    name: string;
    description: string;
    status: string;
    supportsAudio: boolean;
    quality: string;
    region: string;
}
export declare const AVAILABLE_GEMINI_VOICES: Record<string, VoiceCharacteristics>;
export declare const VOICE_MAPPING: Record<string, string>;
export declare const AVAILABLE_GEMINI_MODELS: Record<string, ModelInfo>;
export declare function getValidGeminiVoice(requestedVoice?: string | null, defaultVoice?: string | null): string;
export declare function getValidGeminiModel(requestedModel?: string | null, defaultModel?: string | null): string;
export declare function initializeGeminiClient(apiKey?: string): any | null;
export { Modality };
//# sourceMappingURL=client.d.ts.map