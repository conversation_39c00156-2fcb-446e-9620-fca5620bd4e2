"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Modality = exports.AVAILABLE_GEMINI_MODELS = exports.VOICE_MAPPING = exports.AVAILABLE_GEMINI_VOICES = void 0;
exports.getValidGeminiVoice = getValidGeminiVoice;
exports.getValidGeminiModel = getValidGeminiModel;
exports.initializeGeminiClient = initializeGeminiClient;
const genai_1 = __importDefault(require("@google/genai"));
const { GoogleGenAI, Modality } = genai_1.default;
exports.Modality = Modality;
const logger_1 = require("../utils/logger");
const config_1 = require("../config/config");
// Available Gemini voices with detailed characteristics (from Google AI Studio)
exports.AVAILABLE_GEMINI_VOICES = {
    'Aoede': {
        name: 'Aoede',
        gender: 'Female',
        characteristics: 'Bright, clear narrator with neutral accent',
        description: 'Professional female voice ideal for presentations, tutorials, and informational content. Natural-sounding with excellent clarity.',
        pitch: 'Mid-range',
        timbre: 'Bright and clear',
        persona: 'Professional narrator',
        useCase: 'Business calls, presentations, tutorials'
    },
    'Puck': {
        name: 'Puck',
        gender: 'Male',
        characteristics: 'Lively, energetic tenor with higher pitch',
        description: 'Upbeat male voice that conveys enthusiasm and energy. Great for engaging conversations and customer interactions.',
        pitch: 'Higher tenor',
        timbre: 'Lively and energetic',
        persona: 'Enthusiastic and friendly',
        useCase: 'Sales calls, customer service, marketing'
    },
    'Charon': {
        name: 'Charon',
        gender: 'Male',
        characteristics: 'Deep, authoritative baritone with warmth',
        description: 'Commanding male voice that projects authority and trustworthiness. Ideal for serious business communications.',
        pitch: 'Deep baritone',
        timbre: 'Warm and authoritative',
        persona: 'Executive and trustworthy',
        useCase: 'Leadership calls, formal business, finance'
    },
    'Kore': {
        name: 'Kore',
        gender: 'Female',
        characteristics: 'Soft, empathetic alto with caring tone',
        description: 'Gentle female voice that conveys empathy and understanding. Perfect for customer support and healthcare interactions.',
        pitch: 'Soft alto',
        timbre: 'Warm and caring',
        persona: 'Empathetic and supportive',
        useCase: 'Customer support, healthcare, counseling'
    },
    'Fenrir': {
        name: 'Fenrir',
        gender: 'Male',
        characteristics: 'Confident, assertive mid-range voice',
        description: 'Strong male voice that projects confidence and reliability. Excellent for professional business communications.',
        pitch: 'Mid-range',
        timbre: 'Assertive and confident',
        persona: 'Professional and decisive',
        useCase: 'Business development, negotiations, consulting'
    },
    'Leda': {
        name: 'Leda',
        gender: 'Female',
        characteristics: 'Clear, professional announcer with refined accent',
        description: 'Polished female voice with broadcast-quality clarity. Ideal for formal communications and announcements.',
        pitch: 'Clear and articulate',
        timbre: 'Professional and refined',
        persona: 'Sophisticated announcer',
        useCase: 'Corporate communications, announcements, formal calls'
    },
    'Orus': {
        name: 'Orus',
        gender: 'Male',
        characteristics: 'Relaxed, casual tenor with breathy quality',
        description: 'Laid-back male voice that creates a comfortable, approachable atmosphere. Great for informal conversations.',
        pitch: 'Relaxed tenor',
        timbre: 'Breathy and casual',
        persona: 'Friendly and approachable',
        useCase: 'Casual conversations, support calls, friendly outreach'
    },
    'Zephyr': {
        name: 'Zephyr',
        gender: 'Female',
        characteristics: 'Light, youthful soprano with airy quality',
        description: 'Fresh female voice that sounds young and optimistic. Perfect for modern, tech-savvy interactions.',
        pitch: 'Light soprano',
        timbre: 'Airy and youthful',
        persona: 'Modern and optimistic',
        useCase: 'Tech support, modern brands, youth-oriented calls'
    }
};
// Voice mapping for different languages/accents and compatibility
exports.VOICE_MAPPING = {
    // OpenAI voice mappings (for compatibility)
    'shimmer': 'Orus',
    'alloy': 'Puck',
    'echo': 'Charon',
    'fable': 'Kore',
    'onyx': 'Fenrir',
    'nova': 'Aoede',
    // Gender-based mappings
    'female': 'Kore',
    'male': 'Orus',
    'professional': 'Leda',
    'youthful': 'Zephyr',
    'authoritative': 'Charon',
    'energetic': 'Puck'
};
// Available Gemini models (configurable)
exports.AVAILABLE_GEMINI_MODELS = {
    'gemini-2.5-flash-preview-native-audio-dialog': {
        name: 'Gemini 2.5 Flash - Native Audio Dialog',
        description: 'Recommended default for voice apps. Outputs text and 24 kHz speech in 30 HD voices across 24 languages',
        status: 'Private preview',
        supportsAudio: true,
        quality: '★★★',
        region: 'global'
    },
    'gemini-2.0-flash-live-001': {
        name: 'Gemini 2.0 Flash Live (001)',
        description: 'GA/billing-enabled half-cascade audio. Native audio in, server-side TTS out',
        status: 'Public preview (billing on)',
        supportsAudio: true,
        quality: '★★',
        region: 'global, us-central1, EU4'
    }
};
// Function to map voice names and validate
function getValidGeminiVoice(requestedVoice, defaultVoice) {
    if (!defaultVoice) {
        defaultVoice = (0, config_1.getConfigValue)('ai.gemini.defaultVoice', 'Kore');
    }
    if (!requestedVoice) {
        logger_1.geminiLogger.info(`🎤 No voice specified, using default: ${defaultVoice} (${exports.AVAILABLE_GEMINI_VOICES[defaultVoice]?.characteristics || 'unknown'})`);
        return defaultVoice;
    }
    // Check if it's already a valid Gemini voice
    if (exports.AVAILABLE_GEMINI_VOICES[requestedVoice]) {
        const voiceInfo = exports.AVAILABLE_GEMINI_VOICES[requestedVoice];
        logger_1.geminiLogger.info(`🎤 Using requested voice: ${requestedVoice} (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
        return requestedVoice;
    }
    // Check if it's an OpenAI voice or other mapping that needs conversion
    if (exports.VOICE_MAPPING[requestedVoice]) {
        const mappedVoice = exports.VOICE_MAPPING[requestedVoice];
        const voiceInfo = exports.AVAILABLE_GEMINI_VOICES[mappedVoice];
        logger_1.geminiLogger.info(`🎤 Mapped voice '${requestedVoice}' → '${mappedVoice}' (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
        return mappedVoice;
    }
    // Default fallback
    const defaultInfo = exports.AVAILABLE_GEMINI_VOICES[defaultVoice];
    logger_1.geminiLogger.warn(`⚠️ Unknown voice '${requestedVoice}', using default: ${defaultVoice} (${defaultInfo?.characteristics || 'unknown'})`);
    return defaultVoice;
}
// Function to validate and get Gemini model
function getValidGeminiModel(requestedModel, defaultModel) {
    if (!defaultModel) {
        defaultModel = (0, config_1.getConfigValue)('ai.gemini.defaultModel', 'gemini-2.5-flash-preview-native-audio-dialog');
    }
    if (!requestedModel) {
        logger_1.geminiLogger.info(`🤖 No model specified, using default: ${defaultModel}`);
        return defaultModel;
    }
    // Check if it's a valid Gemini model
    if (exports.AVAILABLE_GEMINI_MODELS[requestedModel]) {
        logger_1.geminiLogger.info(`🤖 Using requested model: ${requestedModel}`);
        return requestedModel;
    }
    // Default fallback
    logger_1.geminiLogger.warn(`⚠️ Unknown model '${requestedModel}', using default: ${defaultModel}`);
    return defaultModel;
}
// Initialize Gemini client
function initializeGeminiClient(apiKey) {
    if (!apiKey) {
        logger_1.geminiLogger.error('❌ GEMINI_API_KEY is required');
        return null;
    }
    try {
        // Pass API key as object (like in working tests)
        const client = new GoogleGenAI({ apiKey: apiKey });
        logger_1.geminiLogger.info('🤖 Gemini client initialized successfully');
        return client;
    }
    catch (error) {
        logger_1.geminiLogger.error('❌ Error initializing Gemini client', error instanceof Error ? error : new Error(String(error)));
        return null;
    }
}
//# sourceMappingURL=client.js.map