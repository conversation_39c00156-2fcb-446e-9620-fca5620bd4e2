import { z } from 'zod';
export declare const phoneNumberSchema: z.ZodString;
export declare const callSidSchema: z.ZodString;
export declare const sessionIdSchema: z.ZodString;
export declare const localTestingMessageSchema: z.ZodObject<{
    type: z.<PERSON><["start-session", "start_session", "audio-data", "audio_data", "end-session", "end_session"]>;
    campaignId: z.<PERSON>od<PERSON>ptional<z.ZodNumber>;
    audioData: z.ZodOptional<z.ZodString>;
    mimeType: z.ZodOptional<z.ZodString>;
}, "strip", z.<PERSON>odTypeAny, {
    type: "audio-data" | "start-session" | "start_session" | "end-session" | "end_session" | "audio_data";
    mimeType?: string | undefined;
    campaignId?: number | undefined;
    audioData?: string | undefined;
}, {
    type: "audio-data" | "start-session" | "start_session" | "end-session" | "end_session" | "audio_data";
    mimeType?: string | undefined;
    campaignId?: number | undefined;
    audioData?: string | undefined;
}>;
export declare const voiceSchema: z.<PERSON><z.ZodEnum<["Aoede", "Charon", "Fenrir", "Kore", "Puck", "Leda", "Orus", "Zephyr"]>, "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr", "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr">;
export declare const modelSchema: z.ZodEffects<z.ZodEnum<["gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro", "gemini-2.5-flash-preview-native-audio-dialog", "gemini-2.0-flash-live-001", "gemini-2.0-flash", "gemini-2.0-flash-live-preview-04-09"]>, "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09", "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09">;
export declare const scriptTypeSchema: z.ZodEffects<z.ZodEnum<["incoming", "outbound"]>, "incoming" | "outbound", "incoming" | "outbound">;
export declare const languageSchema: z.ZodEnum<["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"]>;
export declare const sessionConfigSchema: z.ZodEffects<z.ZodObject<{
    voice: z.ZodOptional<z.ZodEffects<z.ZodEnum<["Aoede", "Charon", "Fenrir", "Kore", "Puck", "Leda", "Orus", "Zephyr"]>, "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr", "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr">>;
    model: z.ZodOptional<z.ZodEffects<z.ZodEnum<["gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro", "gemini-2.5-flash-preview-native-audio-dialog", "gemini-2.0-flash-live-001", "gemini-2.0-flash", "gemini-2.0-flash-live-preview-04-09"]>, "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09", "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09">>;
    aiInstructions: z.ZodOptional<z.ZodString>;
    task: z.ZodOptional<z.ZodString>;
    targetName: z.ZodOptional<z.ZodString>;
    targetPhoneNumber: z.ZodOptional<z.ZodString>;
    outputLanguage: z.ZodOptional<z.ZodEnum<["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"]>>;
}, "strip", z.ZodTypeAny, {
    voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
    model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
    aiInstructions?: string | undefined;
    task?: string | undefined;
    targetName?: string | undefined;
    targetPhoneNumber?: string | undefined;
    outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
}, {
    voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
    model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
    aiInstructions?: string | undefined;
    task?: string | undefined;
    targetName?: string | undefined;
    targetPhoneNumber?: string | undefined;
    outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
}>, {
    voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
    model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
    aiInstructions?: string | undefined;
    task?: string | undefined;
    targetName?: string | undefined;
    targetPhoneNumber?: string | undefined;
    outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
}, {
    voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
    model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
    aiInstructions?: string | undefined;
    task?: string | undefined;
    targetName?: string | undefined;
    targetPhoneNumber?: string | undefined;
    outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
}>;
export declare const webSocketMessageSchema: z.ZodDiscriminatedUnion<"type", [z.ZodObject<{
    type: z.ZodLiteral<"audio">;
    data: z.ZodString;
    callSid: z.ZodString;
}, "strip", z.ZodTypeAny, {
    callSid: string;
    type: "audio";
    data: string;
}, {
    callSid: string;
    type: "audio";
    data: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"text">;
    data: z.ZodString;
    callSid: z.ZodString;
}, "strip", z.ZodTypeAny, {
    callSid: string;
    type: "text";
    data: string;
}, {
    callSid: string;
    type: "text";
    data: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"control">;
    action: z.ZodEnum<["start", "stop", "pause", "resume"]>;
    callSid: z.ZodString;
}, "strip", z.ZodTypeAny, {
    callSid: string;
    type: "control";
    action: "start" | "stop" | "pause" | "resume";
}, {
    callSid: string;
    type: "control";
    action: "start" | "stop" | "pause" | "resume";
}>, z.ZodObject<{
    type: z.ZodLiteral<"config">;
    config: z.ZodEffects<z.ZodObject<{
        voice: z.ZodOptional<z.ZodEffects<z.ZodEnum<["Aoede", "Charon", "Fenrir", "Kore", "Puck", "Leda", "Orus", "Zephyr"]>, "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr", "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr">>;
        model: z.ZodOptional<z.ZodEffects<z.ZodEnum<["gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro", "gemini-2.5-flash-preview-native-audio-dialog", "gemini-2.0-flash-live-001", "gemini-2.0-flash", "gemini-2.0-flash-live-preview-04-09"]>, "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09", "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09">>;
        aiInstructions: z.ZodOptional<z.ZodString>;
        task: z.ZodOptional<z.ZodString>;
        targetName: z.ZodOptional<z.ZodString>;
        targetPhoneNumber: z.ZodOptional<z.ZodString>;
        outputLanguage: z.ZodOptional<z.ZodEnum<["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"]>>;
    }, "strip", z.ZodTypeAny, {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    }, {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    }>, {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    }, {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    }>;
    callSid: z.ZodString;
}, "strip", z.ZodTypeAny, {
    callSid: string;
    type: "config";
    config: {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    };
}, {
    callSid: string;
    type: "config";
    config: {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    };
}>]>;
export declare const makeCallSchema: z.ZodObject<{
    to: z.ZodString;
    from: z.ZodString;
    aiInstructions: z.ZodString;
    voice: z.ZodEffects<z.ZodEnum<["Aoede", "Charon", "Fenrir", "Kore", "Puck", "Leda", "Orus", "Zephyr"]>, "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr", "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr">;
    model: z.ZodEffects<z.ZodEnum<["gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro", "gemini-2.5-flash-preview-native-audio-dialog", "gemini-2.0-flash-live-001", "gemini-2.0-flash", "gemini-2.0-flash-live-preview-04-09"]>, "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09", "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09">;
    targetName: z.ZodOptional<z.ZodString>;
    outputLanguage: z.ZodOptional<z.ZodEnum<["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"]>>;
}, "strip", z.ZodTypeAny, {
    voice: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr";
    model: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09";
    aiInstructions: string;
    to: string;
    from: string;
    targetName?: string | undefined;
    outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
}, {
    voice: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr";
    model: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09";
    aiInstructions: string;
    to: string;
    from: string;
    targetName?: string | undefined;
    outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
}>;
export declare const updateSystemMessageSchema: z.ZodEffects<z.ZodObject<{
    systemMessage: z.ZodString;
    callSid: z.ZodOptional<z.ZodString>;
    sessionId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    systemMessage: string;
    callSid?: string | undefined;
    sessionId?: string | undefined;
}, {
    systemMessage: string;
    callSid?: string | undefined;
    sessionId?: string | undefined;
}>, {
    systemMessage: string;
    callSid?: string | undefined;
    sessionId?: string | undefined;
}, {
    systemMessage: string;
    callSid?: string | undefined;
    sessionId?: string | undefined;
}>;
export declare const saveConfigSchema: z.ZodObject<{
    name: z.ZodString;
    config: z.ZodEffects<z.ZodObject<{
        voice: z.ZodOptional<z.ZodEffects<z.ZodEnum<["Aoede", "Charon", "Fenrir", "Kore", "Puck", "Leda", "Orus", "Zephyr"]>, "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr", "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr">>;
        model: z.ZodOptional<z.ZodEffects<z.ZodEnum<["gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro", "gemini-2.5-flash-preview-native-audio-dialog", "gemini-2.0-flash-live-001", "gemini-2.0-flash", "gemini-2.0-flash-live-preview-04-09"]>, "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09", "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09">>;
        aiInstructions: z.ZodOptional<z.ZodString>;
        task: z.ZodOptional<z.ZodString>;
        targetName: z.ZodOptional<z.ZodString>;
        targetPhoneNumber: z.ZodOptional<z.ZodString>;
        outputLanguage: z.ZodOptional<z.ZodEnum<["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"]>>;
    }, "strip", z.ZodTypeAny, {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    }, {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    }>, {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    }, {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    name: string;
    config: {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    };
}, {
    name: string;
    config: {
        voice?: "Aoede" | "Charon" | "Fenrir" | "Kore" | "Puck" | "Leda" | "Orus" | "Zephyr" | undefined;
        model?: "gemini-2.0-flash-exp" | "gemini-1.5-flash" | "gemini-1.5-pro" | "gemini-2.5-flash-preview-native-audio-dialog" | "gemini-2.0-flash-live-001" | "gemini-2.0-flash" | "gemini-2.0-flash-live-preview-04-09" | undefined;
        aiInstructions?: string | undefined;
        task?: string | undefined;
        targetName?: string | undefined;
        targetPhoneNumber?: string | undefined;
        outputLanguage?: "en" | "es" | "fr" | "de" | "it" | "pt" | "ru" | "ja" | "ko" | "zh" | undefined;
    };
}>;
export declare const twilioWebhookSchema: z.ZodObject<{
    CallSid: z.ZodString;
    From: z.ZodString;
    To: z.ZodString;
    CallStatus: z.ZodEnum<["ringing", "in-progress", "completed", "busy", "failed", "no-answer", "canceled"]>;
    Direction: z.ZodEnum<["inbound", "outbound"]>;
    AccountSid: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    CallSid: string;
    From: string;
    To: string;
    CallStatus: "completed" | "failed" | "canceled" | "no-answer" | "busy" | "ringing" | "in-progress";
    Direction: "outbound" | "inbound";
    AccountSid?: string | undefined;
}, {
    CallSid: string;
    From: string;
    To: string;
    CallStatus: "completed" | "failed" | "canceled" | "no-answer" | "busy" | "ringing" | "in-progress";
    Direction: "outbound" | "inbound";
    AccountSid?: string | undefined;
}>;
export declare const audioDataSchema: z.ZodObject<{
    data: z.ZodUnion<[z.ZodType<Buffer<ArrayBufferLike>, z.ZodTypeDef, Buffer<ArrayBufferLike>>, z.ZodString]>;
    format: z.ZodOptional<z.ZodEnum<["mulaw", "pcm", "opus"]>>;
    sampleRate: z.ZodOptional<z.ZodNumber>;
    channels: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    data: string | Buffer<ArrayBufferLike>;
    sampleRate?: number | undefined;
    format?: "mulaw" | "pcm" | "opus" | undefined;
    channels?: number | undefined;
}, {
    data: string | Buffer<ArrayBufferLike>;
    sampleRate?: number | undefined;
    format?: "mulaw" | "pcm" | "opus" | undefined;
    channels?: number | undefined;
}>;
export declare const campaignScriptSchema: z.ZodObject<{
    agentPersona: z.ZodObject<{
        name: z.ZodString;
        role: z.ZodString;
        personality: z.ZodString;
        communicationStyle: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        name: string;
        role: string;
        personality: string;
        communicationStyle: string;
    }, {
        name: string;
        role: string;
        personality: string;
        communicationStyle: string;
    }>;
    campaign: z.ZodObject<{
        objective: z.ZodString;
        targetAudience: z.ZodString;
        keyMessages: z.ZodArray<z.ZodString, "many">;
        callToAction: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        objective: string;
        targetAudience: string;
        keyMessages: string[];
        callToAction: string;
    }, {
        objective: string;
        targetAudience: string;
        keyMessages: string[];
        callToAction: string;
    }>;
    conversationFlow: z.ZodObject<{
        opening: z.ZodString;
        mainPoints: z.ZodArray<z.ZodString, "many">;
        objectionHandling: z.ZodArray<z.ZodObject<{
            objection: z.ZodString;
            response: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            response: string;
            objection: string;
        }, {
            response: string;
            objection: string;
        }>, "many">;
        closing: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        opening: string;
        mainPoints: string[];
        objectionHandling: {
            response: string;
            objection: string;
        }[];
        closing: string;
    }, {
        opening: string;
        mainPoints: string[];
        objectionHandling: {
            response: string;
            objection: string;
        }[];
        closing: string;
    }>;
    complianceGuidelines: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    agentPersona: {
        name: string;
        role: string;
        personality: string;
        communicationStyle: string;
    };
    campaign: {
        objective: string;
        targetAudience: string;
        keyMessages: string[];
        callToAction: string;
    };
    conversationFlow: {
        opening: string;
        mainPoints: string[];
        objectionHandling: {
            response: string;
            objection: string;
        }[];
        closing: string;
    };
    complianceGuidelines: string[];
}, {
    agentPersona: {
        name: string;
        role: string;
        personality: string;
        communicationStyle: string;
    };
    campaign: {
        objective: string;
        targetAudience: string;
        keyMessages: string[];
        callToAction: string;
    };
    conversationFlow: {
        opening: string;
        mainPoints: string[];
        objectionHandling: {
            response: string;
            objection: string;
        }[];
        closing: string;
    };
    complianceGuidelines: string[];
}>;
export type SessionConfigValidation = z.infer<typeof sessionConfigSchema>;
export type WebSocketMessage = z.infer<typeof webSocketMessageSchema>;
export type MakeCallRequest = z.infer<typeof makeCallSchema>;
export type UpdateSystemMessageRequest = z.infer<typeof updateSystemMessageSchema>;
export type SaveConfigRequest = z.infer<typeof saveConfigSchema>;
export type TwilioWebhook = z.infer<typeof twilioWebhookSchema>;
export type AudioData = z.infer<typeof audioDataSchema>;
export type CampaignScript = z.infer<typeof campaignScriptSchema>;
//# sourceMappingURL=schemas.d.ts.map