"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.campaignScriptSchema = exports.audioDataSchema = exports.twilioWebhookSchema = exports.saveConfigSchema = exports.updateSystemMessageSchema = exports.makeCallSchema = exports.webSocketMessageSchema = exports.sessionConfigSchema = exports.languageSchema = exports.scriptTypeSchema = exports.modelSchema = exports.voiceSchema = exports.localTestingMessageSchema = exports.sessionIdSchema = exports.callSidSchema = exports.phoneNumberSchema = void 0;
const zod_1 = require("zod");
// Common validation schemas
exports.phoneNumberSchema = zod_1.z.string()
    .regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format. Must be in E.164 format (e.g., +1234567890)');
exports.callSidSchema = zod_1.z.string()
    .regex(/^CA[a-f0-9]{32}$/, 'Invalid CallSid format');
exports.sessionIdSchema = zod_1.z.string()
    .min(1, 'Session ID cannot be empty')
    .max(100, 'Session ID too long');
// Local testing message schema - supports both hyphen and underscore formats
exports.localTestingMessageSchema = zod_1.z.object({
    type: zod_1.z.enum(['start-session', 'start_session', 'audio-data', 'audio_data', 'end-session', 'end_session']),
    campaignId: zod_1.z.number().optional(),
    audioData: zod_1.z.string().optional(),
    mimeType: zod_1.z.string().optional(),
});
// Import centralized schemas
const shared_types_1 = require("../types/shared-types");
// Re-export with validation messages
exports.voiceSchema = shared_types_1.VoiceSchema.refine(() => true, {
    message: 'Invalid voice. Must be one of: Aoede, Charon, Fenrir, Kore, Puck, Leda, Orus, Zephyr'
});
exports.modelSchema = shared_types_1.ModelSchema.refine(() => true, {
    message: 'Invalid model. Must be one of the supported Gemini models'
});
exports.scriptTypeSchema = shared_types_1.ScriptTypeSchema.refine(() => true, {
    message: 'Invalid script type. Must be incoming or outbound'
});
// Language validation
exports.languageSchema = zod_1.z.enum([
    'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'
], {
    errorMap: () => ({ message: 'Invalid language code' })
});
// Session configuration schemas
exports.sessionConfigSchema = zod_1.z.object({
    voice: exports.voiceSchema.optional(),
    model: exports.modelSchema.optional(),
    aiInstructions: zod_1.z.string().max(50000, 'AI instructions too long').optional(),
    task: zod_1.z.string().max(50000, 'Task description too long').optional(), // Legacy field
    targetName: zod_1.z.string().max(100, 'Target name too long').optional(),
    targetPhoneNumber: exports.phoneNumberSchema.optional(),
    outputLanguage: exports.languageSchema.optional(),
}).refine(data => {
    // Ensure at least one of aiInstructions or task is provided if either is present
    if (data.aiInstructions && data.task) {
        return false; // Don't allow both
    }
    return true;
}, {
    message: 'Provide either aiInstructions or task, not both'
});
// WebSocket message schemas
exports.webSocketMessageSchema = zod_1.z.discriminatedUnion('type', [
    zod_1.z.object({
        type: zod_1.z.literal('audio'),
        data: zod_1.z.string(), // Base64 encoded audio
        callSid: exports.callSidSchema
    }),
    zod_1.z.object({
        type: zod_1.z.literal('text'),
        data: zod_1.z.string().max(1000, 'Text message too long'),
        callSid: exports.callSidSchema
    }),
    zod_1.z.object({
        type: zod_1.z.literal('control'),
        action: zod_1.z.enum(['start', 'stop', 'pause', 'resume']),
        callSid: exports.callSidSchema
    }),
    zod_1.z.object({
        type: zod_1.z.literal('config'),
        config: exports.sessionConfigSchema,
        callSid: exports.callSidSchema
    })
]);
// API endpoint schemas
exports.makeCallSchema = zod_1.z.object({
    to: exports.phoneNumberSchema,
    from: exports.phoneNumberSchema,
    aiInstructions: zod_1.z.string().min(1, 'AI instructions required').max(50000, 'AI instructions too long'),
    voice: exports.voiceSchema,
    model: exports.modelSchema,
    targetName: zod_1.z.string().max(100, 'Target name too long').optional(),
    outputLanguage: exports.languageSchema.optional(),
});
exports.updateSystemMessageSchema = zod_1.z.object({
    systemMessage: zod_1.z.string().max(10000, 'System message too long'),
    callSid: exports.callSidSchema.optional(),
    sessionId: exports.sessionIdSchema.optional(),
}).refine(data => data.callSid || data.sessionId, {
    message: 'Either callSid or sessionId must be provided'
});
exports.saveConfigSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Config name required').max(100, 'Config name too long'),
    config: exports.sessionConfigSchema,
});
// Twilio webhook schemas
exports.twilioWebhookSchema = zod_1.z.object({
    CallSid: exports.callSidSchema,
    From: exports.phoneNumberSchema,
    To: exports.phoneNumberSchema,
    CallStatus: zod_1.z.enum(['ringing', 'in-progress', 'completed', 'busy', 'failed', 'no-answer', 'canceled']),
    Direction: zod_1.z.enum(['inbound', 'outbound']),
    AccountSid: zod_1.z.string().regex(/^AC[a-f0-9]{32}$/, 'Invalid AccountSid format').optional(),
});
// Audio data validation
exports.audioDataSchema = zod_1.z.object({
    data: zod_1.z.instanceof(Buffer).or(zod_1.z.string()), // Buffer or base64 string
    format: zod_1.z.enum(['mulaw', 'pcm', 'opus']).optional(),
    sampleRate: zod_1.z.number().int().min(8000).max(48000).optional(),
    channels: zod_1.z.number().int().min(1).max(2).optional(),
});
// Campaign script validation
exports.campaignScriptSchema = zod_1.z.object({
    agentPersona: zod_1.z.object({
        name: zod_1.z.string().max(100),
        role: zod_1.z.string().max(100),
        personality: zod_1.z.string().max(500),
        communicationStyle: zod_1.z.string().max(500),
    }),
    campaign: zod_1.z.object({
        objective: zod_1.z.string().max(500),
        targetAudience: zod_1.z.string().max(500),
        keyMessages: zod_1.z.array(zod_1.z.string().max(200)).max(10),
        callToAction: zod_1.z.string().max(200),
    }),
    conversationFlow: zod_1.z.object({
        opening: zod_1.z.string().max(1000),
        mainPoints: zod_1.z.array(zod_1.z.string().max(500)).max(10),
        objectionHandling: zod_1.z.array(zod_1.z.object({
            objection: zod_1.z.string().max(200),
            response: zod_1.z.string().max(500),
        })).max(10),
        closing: zod_1.z.string().max(1000),
    }),
    complianceGuidelines: zod_1.z.array(zod_1.z.string().max(300)).max(10),
});
//# sourceMappingURL=schemas.js.map