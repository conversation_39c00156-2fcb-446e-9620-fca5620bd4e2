{"version": 3, "file": "audio-processor.js", "sourceRoot": "", "sources": ["../../../src/audio/audio-processor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yDAAwE;AACxE,qDAAiD;AACjD,mDAAmG;AAEnG,uCAAyB;AACzB,2CAA6B;AAE7B,MAAa,cAAc;IACvB,MAAM,CAAC,aAAa,GAAG,IAAI,8BAAa,EAAE,CAAC;IAC3C,MAAM,CAAC,mBAAmB,GAAG,mCAAmB,CAAC;IAEzC,SAAS,CAAiB;IAC1B,cAAc,CAAuB;IACrC,aAAa,CAAgB;IAErC;QACI,IAAI,CAAC,SAAS,GAAG,IAAI,iCAAc,EAAE,CAAC;QACtC,IAAI,CAAC,cAAc,GAAG,IAAI,oCAAoB,CAAC;YAC3C,aAAa,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;YAC/B,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC;YACrD,oBAAoB,EAAE,EAAE;YACxB,qBAAqB,EAAE,EAAE;YACzB,sBAAsB,EAAE,KAAK,CAAC,uDAAuD;SACxF,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,GAAG;YACjB,cAAc,EAAE,KAAK;YACrB,oBAAoB,EAAE,KAAK;YAC3B,iBAAiB,EAAE,KAAK;YACxB,SAAS,EAAE,KAAK;YAChB,gBAAgB,EAAE,GAAG;YACrB,cAAc,EAAE,IAAI;YACpB,cAAc,EAAE,GAAG;SACtB,CAAC;IACN,CAAC;IAED,gBAAgB,CAAC,WAAmB,EAAE,eAAe,GAAG,KAAK;QACzD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACzD,mDAAmD;QACnD,IAAI,eAAe,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC3D,OAAO,GAAG,CAAC;QACf,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QACpD,MAAM,QAAQ,GAAG,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC;QACvF,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACnD,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED,iBAAiB,CAAC,SAAiB;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IACvD,CAAC;IAED,eAAe,CAAC,IAAkB;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,iBAAiB,CAAC,IAAkB;QAChC,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED,qBAAqB,CAAC,IAAkB;QACpC,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB,CAAC,KAAsB;QACnC,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAClD,CAAC;IAED,SAAS,CAAC,GAAW;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC;IAED,YAAY,CAAC,MAAc;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,QAAgB,EAAE,gBAAgB,GAAG,KAAK;QAChE,OAAO,iCAAc,CAAC,kBAAkB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;IACzE,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,QAAgB;QACnC,OAAO,iCAAc,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,WAA4B,EAAE,gBAAwB;QACxE,OAAO,iCAAc,CAAC,cAAc,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;IACxE,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAAqB,EAAE,QAAgB,EAAE,UAAU,GAAG,IAAI;QAC3E,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChD,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,CAAC;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,aAAa,CAAC,CAAC;YAEpD,IAAI,CAAC;gBACD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACtB,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC3C,CAAC;YACL,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBAClB,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,UAAU,CAAC,CAAC;gBACrE,OAAO,CAAC,iDAAiD;YAC7D,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAEjE,IAAI,CAAC;gBACD,mDAAmD;gBACnD,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,UAAU,EAAE,EAAE;oBAC7C,IAAI,UAAU,EAAE,CAAC;wBACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,UAAU,CAAC,CAAC;wBAC/D,2CAA2C;oBAC/C,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBAClB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,UAAU,CAAC,CAAC;gBAC/D,2CAA2C;YAC/C,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,sDAAsD;QAC1D,CAAC;IACL,CAAC;IAED,mBAAmB,CAAC,WAAmC;QACnD,IAAI,CAAC,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,WAAW,EAAE,CAAC;IACnE,CAAC;IAED,gBAAgB;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;IACrC,CAAC;IAED,kBAAkB;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;IACpD,CAAC;IAED,oBAAoB;QAChB,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;IACrC,CAAC;IAED,mBAAmB,CAAC,MAAc,EAAE,SAAiB,MAAM,EAAE,SAAkB;QAC3E,OAAO,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IAC9E,CAAC;IAED,oBAAoB;QAMhB,OAAO;YACH,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,aAAa,EAAE,cAAc,CAAC,aAAa,CAAC,kBAAkB,EAAE;YAChE,cAAc,EAAE,cAAc,CAAC,mBAAmB,CAAC,UAAU,EAAE;YAC/D,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACvC,CAAC;IACN,CAAC;;AA/JL,wCAgKC;AAED,uDAAwD;AAA/C,sHAAA,kBAAkB,OAAA"}