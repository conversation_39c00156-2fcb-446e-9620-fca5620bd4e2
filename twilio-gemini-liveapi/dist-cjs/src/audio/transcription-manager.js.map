{"version": 3, "file": "transcription-manager.js", "sourceRoot": "", "sources": ["../../../src/audio/transcription-manager.ts"], "names": [], "mappings": ";;;AAAA,gCAAgC;AAChC,uCAAkF;AAsDlF,MAAa,oBAAoB;IACrB,cAAc,CAAM;IACpB,iBAAiB,CAAwD;IAEjF;QACI,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAA,kBAAY,EAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACvG,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,yCAAyC;IACjF,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,OAAe,EAAE,cAA8B;QAChF,MAAM,OAAO,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC3C,OAAO,MAAM,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,OAAe,EAAE,cAA8B;QACrE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,OAAO,OAAO,mDAAmD,CAAC,CAAC;YAChF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,OAAO,CAAC,IAAI,CAAC,OAAO,OAAO,+CAA+C,CAAC,CAAC;YAC5E,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC;YACD,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;gBACjD,KAAK,EAAE,QAAQ;gBACf,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;gBAClB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,OAAO;gBACjB,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,CAAC;aACd,CAAC,CAAC;YAEH,mCAAmC;YACnC,MAAM,aAAa,GAA4C,EAAE,CAAC;YAElE,aAAa,CAAC,IAAI,GAAG,GAAG,EAAE;gBACtB,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,4CAA4C,CAAC,CAAC;gBACxE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC,CAAC;YAC/F,CAAC,CAAC;YACF,YAAY,CAAC,EAAE,CAAC,6BAAuB,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;YAElE,aAAa,CAAC,UAAU,GAAG,CAAC,IAAS,EAAE,EAAE;gBACrC,IAAI,CAAC;oBACD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;oBAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,IAAI,GAAG,CAAC;oBAEtE,IAAI,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAC9B,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,iBAAiB,UAAU,GAAG,CAAC,CAAC;wBAE1D,kDAAkD;wBAClD,IAAI,cAAc,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;4BAClD,MAAM,mBAAmB,GAAG,IAAI,CAAC;4BACjC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC;gCAC/B,IAAI,EAAE,MAAM;gCACZ,OAAO,EAAE,UAAU;gCACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gCACrB,UAAU,EAAE,UAAU;6BACzB,CAAC,CAAC;4BAEH,wBAAwB;4BACxB,IAAI,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,mBAAmB,EAAE,CAAC;gCAC7D,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,mBAAmB,CAAC,CAAC;4BACxG,CAAC;wBACL,CAAC;wBAED,+CAA+C;wBAC/C,IAAI,cAAc,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;4BACnD,MAAM,qBAAqB,GAAG,GAAG,CAAC;4BAClC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC;gCAChC,IAAI,EAAE,MAAM;gCACZ,OAAO,EAAE,UAAU;gCACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gCACrB,MAAM,EAAE,UAAU;gCAClB,UAAU,EAAE,UAAU;6BACzB,CAAC,CAAC;4BAEH,wBAAwB;4BACxB,IAAI,cAAc,CAAC,eAAe,CAAC,MAAM,GAAG,qBAAqB,EAAE,CAAC;gCAChE,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,eAAe,CAAC,MAAM,GAAG,qBAAqB,CAAC,CAAC;4BAC5G,CAAC;wBACL,CAAC;wBAED,6CAA6C;wBAC7C,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE;4BACjC,UAAU;4BACV,UAAU,EAAE,UAAU;4BACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;4BACrB,OAAO,EAAE,IAAI,CAAC,QAAQ;yBACzB,CAAC,CAAC;oBACP,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,IAAI,CAAC,OAAO,OAAO,yCAAyC,EAAE,KAAK,CAAC,CAAC;gBACjF,CAAC;YACL,CAAC,CAAC;YACF,YAAY,CAAC,EAAE,CAAC,6BAAuB,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC;YAE9E,aAAa,CAAC,KAAK,GAAG,CAAC,KAAU,EAAE,EAAE;gBACjC,OAAO,CAAC,KAAK,CAAC,MAAM,OAAO,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBACrE,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC,CAAC;YACF,YAAY,CAAC,EAAE,CAAC,6BAAuB,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;YAEpE,aAAa,CAAC,KAAK,GAAG,GAAG,EAAE;gBACvB,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,4CAA4C,CAAC,CAAC;gBACxE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC3C,CAAC,CAAC;YACF,YAAY,CAAC,EAAE,CAAC,6BAAuB,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;YAEpE,OAAO,YAAY,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,MAAM,OAAO,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACpF,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,OAAe,EAAE,YAAwB;QAC/D,IAAI,YAAY,IAAI,OAAO,YAAY,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YAC5D,IAAI,CAAC;gBACD,YAAY,CAAC,MAAM,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,iCAAiC,CAAC,CAAC;YACjE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,OAAO,OAAO,yCAAyC,EAAE,KAAK,CAAC,CAAC;YACjF,CAAC;QACL,CAAC;IACL,CAAC;IAED,2CAA2C;IAC3C,wBAAwB,CAAC,OAAe,EAAE,WAAmB;QACzD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3D,MAAM,YAAY,GAAI,cAA8C,EAAE,UAAU,IAAI,cAA4B,CAAC;QACjH,IAAI,YAAY,IAAI,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACD,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,MAAM,OAAO,yCAAyC,EAAE,KAAK,CAAC,CAAC;YACjF,CAAC;QACL,CAAC;IACL,CAAC;IAED,2BAA2B;IAC3B,sBAAsB,CAAC,OAAe;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzD,OAAO;YACH,QAAQ,EAAE,CAAC,CAAC,YAAY;YACxB,eAAe,EAAE,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;YAC5D,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACN,CAAC;IAED,0CAA0C;IAC1C,kBAAkB,CAAC,OAAe;QAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,cAAc,EAAE,CAAC;YACjB,mCAAmC;YACnC,IAAI,UAAU,IAAI,cAAc,IAAI,cAAc,CAAC,QAAQ,IAAI,cAAc,CAAC,UAAU,EAAE,CAAC;gBACvF,MAAM,IAAI,GAAG,cAAc,CAAC,UAAU,CAAC;gBACvC,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;gBAEzC,wCAAwC;gBACxC,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAAA,IAAI,CAAC,cAAc,CAAC,6BAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAAA,CAAC;gBACtF,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;oBAAA,IAAI,CAAC,cAAc,CAAC,6BAAuB,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAAA,CAAC;gBACxG,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;oBAAA,IAAI,CAAC,cAAc,CAAC,6BAAuB,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAAA,CAAC;gBACzF,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;oBAAA,IAAI,CAAC,cAAc,CAAC,6BAAuB,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAAA,CAAC;YAC7F,CAAC;YAED,uBAAuB;YACvB,MAAM,UAAU,GAAG,YAAY,IAAI,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,cAA4B,CAAC;YAC7G,oBAAoB,CAAC,kBAAkB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YAC7D,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAED,kCAAkC;IAClC,sBAAsB;QAClB,KAAK,MAAM,CAAC,OAAO,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;YACvE,MAAM,UAAU,GAAG,YAAY,IAAI,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,cAA4B,CAAC;YAC7G,oBAAoB,CAAC,kBAAkB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAC3D,CAAC;IAED,sDAAsD;IACtD,OAAO;QACH,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;QACpD,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,uCAAuC,eAAe,mCAAmC,CAAC,CAAC;IAC3G,CAAC;IAED,iCAAiC;IACjC,2BAA2B;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;IACvC,CAAC;IAED,yCAAyC;IACzC,8BAA8B;QAC1B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,8DAA8D;IACtD,sBAAsB,CAAC,OAAe,EAAE,iBAAoC;QAChF,2DAA2D;QAC3D,+BAA+B;QAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,MAAM,EAAE,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,wBAAwB,EAAE;gBAChD,UAAU,EAAE,iBAAiB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;gBACjE,UAAU,EAAE,iBAAiB,CAAC,UAAU;gBACxC,SAAS,EAAE,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;aACjE,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED,+BAA+B;IAC/B,qBAAqB;QACjB,OAAO;YACH,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;YAC9C,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc;YACxC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;YACnD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACN,CAAC;IAED,yCAAyC;IACzC,KAAK,CAAC,WAAW;QACb,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvB,OAAO;oBACH,MAAM,EAAE,aAAa;oBACrB,MAAM,EAAE,iCAAiC;oBACzC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACxB,CAAC;YACN,CAAC;YAED,iDAAiD;YACjD,OAAO;gBACH,MAAM,EAAE,SAAS;gBACjB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;gBAC9C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC;QACN,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YAClB,OAAO;gBACH,MAAM,EAAE,OAAO;gBACf,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC;QACN,CAAC;IACL,CAAC;IAED,mDAAmD;IACnD,KAAK,CAAC,4BAA4B,CAAC,SAAiB,EAAE,cAA8B;QAChF,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,OAAO,SAAS,kDAAkD,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC;YACD,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;gBACjD,KAAK,EAAE,QAAQ;gBACf,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;gBAClB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,UAAU,EAAE,qCAAqC;gBAC3D,WAAW,EAAE,KAAK,EAAI,wCAAwC;gBAC9D,QAAQ,EAAE,CAAC;aACd,CAAC,CAAC;YAEH,YAAY,CAAC,EAAE,CAAC,6BAAuB,CAAC,IAAI,EAAE,GAAG,EAAE;gBAC/C,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,kDAAkD,CAAC,CAAC;gBAChF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,YAAY,CAAC,EAAE,CAAC,6BAAuB,CAAC,UAAU,EAAE,CAAC,IAAS,EAAE,EAAE;gBAC9D,IAAI,CAAC;oBACD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;oBAC/D,IAAI,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAC9B,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,uBAAuB,UAAU,GAAG,CAAC,CAAC;wBAElE,gDAAgD;wBAChD,IAAI,cAAc,EAAE,CAAC;4BACjB,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;gCAChC,MAAM,mBAAmB,GAAG,IAAI,CAAC;gCACjC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC;oCAC/B,IAAI,EAAE,MAAM;oCACZ,OAAO,EAAE,UAAU;oCACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oCACrB,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,GAAG;oCAC1D,MAAM,EAAE,gBAAgB;iCAC3B,CAAC,CAAC;gCAEH,wBAAwB;gCACxB,IAAI,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,mBAAmB,EAAE,CAAC;oCAC7D,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,mBAAmB,CAAC,CAAC;gCACxG,CAAC;4BACL,CAAC;4BAED,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;gCACjC,MAAM,qBAAqB,GAAG,GAAG,CAAC;gCAClC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC;oCAChC,IAAI,EAAE,MAAM;oCACZ,OAAO,EAAE,UAAU;oCACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oCACrB,MAAM,EAAE,gBAAgB;oCACxB,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,GAAG;iCAC7D,CAAC,CAAC;gCAEH,wBAAwB;gCACxB,IAAI,cAAc,CAAC,eAAe,CAAC,MAAM,GAAG,qBAAqB,EAAE,CAAC;oCAChE,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,eAAe,CAAC,MAAM,GAAG,qBAAqB,CAAC,CAAC;gCAC5G,CAAC;4BACL,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,IAAI,CAAC,OAAO,SAAS,+CAA+C,EAAE,KAAK,CAAC,CAAC;gBACzF,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,YAAY,CAAC,EAAE,CAAC,6BAAuB,CAAC,KAAK,EAAE,CAAC,KAAU,EAAE,EAAE;gBAC1D,OAAO,CAAC,KAAK,CAAC,MAAM,SAAS,uCAAuC,EAAE,KAAK,CAAC,CAAC;gBAC7E,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,YAAY,CAAC,EAAE,CAAC,6BAAuB,CAAC,KAAK,EAAE,GAAG,EAAE;gBAChD,OAAO,CAAC,GAAG,CAAC,OAAO,SAAS,kDAAkD,CAAC,CAAC;gBAChF,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,MAAM,SAAS,sDAAsD,EAAE,KAAK,CAAC,CAAC;YAC5F,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;CACJ;AArVD,oDAqVC"}