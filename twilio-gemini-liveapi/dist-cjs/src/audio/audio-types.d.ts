export interface AudioSettings {
    enableDeEssing: boolean;
    enableNoiseReduction: boolean;
    enableCompression: boolean;
    enableAGC: boolean;
    compressionRatio: number;
    noiseThreshold: number;
    agcTargetLevel: number;
}
export interface EnhancementOptions {
    noiseReduction?: boolean;
    compression?: boolean;
    agc?: boolean;
    voiceEnhancement?: boolean;
    deEssing?: boolean;
}
export interface AudioMetrics {
    peak: number;
    rms: number;
    clippingPercentage: number;
    silencePercentage: number;
    dynamicRange: number;
    sampleCount: number;
}
export interface AudioQualityMetrics {
    totalSamples: number;
    clippedSamples: number;
    silentSamples: number;
    peakLevel: number;
    averageLevel: number;
    dynamicRange: number;
    lastUpdate: number;
}
export interface AudioQualitySummary {
    totalSamples: number;
    peakLevel: string;
    averageLevel: string;
    clippingPercentage: string;
    silencePercentage: string;
    dynamicRange: string;
    lastUpdate: string;
}
export interface MockAudioBuffer {
    sampleRate: number;
    numberOfChannels: number;
    length: number;
    duration: number;
    getChannelData(channel: number): Float32Array;
}
//# sourceMappingURL=audio-types.d.ts.map