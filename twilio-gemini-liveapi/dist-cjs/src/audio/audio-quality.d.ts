export interface AudioValidationResult {
    isValid: boolean;
    error?: string;
    warnings?: string[];
    metadata?: AudioBufferMetadata;
}
export interface AudioBufferMetadata {
    size: number;
    format: string;
    sampleRate?: number;
    channels?: number;
    duration?: number;
    isEmpty: boolean;
    hasClipping: boolean;
    hasSilence: boolean;
    qualityScore: number;
}
export interface AudioValidationConfig {
    maxBufferSize: number;
    minBufferSize: number;
    allowedFormats: string[];
    maxSilencePercentage: number;
    maxClippingPercentage: number;
    enableDetailedAnalysis: boolean;
}
/**
 * Enhanced audio buffer validator with comprehensive error handling
 */
export declare class AudioBufferValidator {
    private config;
    private validationStats;
    constructor(config?: Partial<AudioValidationConfig>);
    /**
     * Comprehensive audio buffer validation
     */
    validateAudioBuffer(buffer: unknown, format?: string, sessionId?: string): AudioValidationResult;
    /**
     * Validate basic buffer type and existence
     */
    private validateBasicType;
    /**
     * Validate buffer size constraints
     */
    private validateBufferSize;
    /**
     * Validate audio format
     */
    private validateFormat;
    /**
     * Analyze buffer content for quality metrics
     */
    private analyzeBufferContent;
    /**
     * Analyze μ-law audio content
     */
    private analyzeUlawContent;
    /**
     * Analyze PCM audio content
     */
    private analyzePcmContent;
    /**
     * Calculate overall quality score
     */
    private calculateQualityScore;
    /**
     * Validate content based on metadata
     */
    private validateContent;
    /**
     * Increment validation statistics
     */
    private incrementValidationStat;
    /**
     * Get validation statistics
     */
    getValidationStats(): Record<string, number>;
    /**
     * Reset validation statistics
     */
    resetStats(): void;
}
export interface AudioQualityMetrics {
    totalSamples: number;
    clippedSamples: number;
    silentSamples: number;
    peakLevel: number;
    averageLevel: number;
    dynamicRange: number;
    lastUpdate: number;
}
export interface AudioMetrics {
    peak: number;
    rms: number;
    silencePercentage: number;
    clippingPercentage: number;
    dynamicRange: number;
    qualityScore: number;
    sampleCount?: number;
}
export interface AudioQualitySummary {
    totalAnalyzed: number;
    averageQuality: number;
    clippingRate: number;
    silenceRate: number;
    averageDynamicRange: number;
    lastAnalysis: number;
    totalSamples?: number;
}
export declare const audioQualityMonitor: {
    metrics: AudioQualityMetrics;
    /**
     * Analyze audio quality metrics
     * @param samples - Float32Array of audio samples
     * @param label - Label for logging (e.g., 'input', 'output')
     */
    analyze(samples: Float32Array, label?: string): AudioMetrics | null;
    /**
     * Calculate detailed audio metrics
     * @param samples - Float32Array of audio samples
     * @returns Object with audio quality metrics
     */
    calculateMetrics(samples: Float32Array): AudioMetrics;
    /**
     * Update global metrics tracking
     * @param metrics - Current metrics object
     */
    updateGlobalMetrics(metrics: AudioMetrics): void;
    /**
     * Get current quality summary
     * @returns Object with quality summary
     */
    getSummary(): AudioQualitySummary;
    /**
     * Reset metrics
     */
    reset(): void;
};
//# sourceMappingURL=audio-quality.d.ts.map