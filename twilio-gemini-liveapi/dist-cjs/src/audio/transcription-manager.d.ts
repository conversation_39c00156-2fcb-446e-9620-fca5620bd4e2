import { LiveClient } from '@deepgram/sdk';
interface TranscriptEntry {
    role: string;
    content: string;
    timestamp: number;
    confidence?: number;
    source?: string;
}
interface ConnectionData {
    fullTranscript?: TranscriptEntry[];
    conversationLog?: TranscriptEntry[];
}
interface TranscriptionStatus {
    isActive: boolean;
    connectionState: string;
    timestamp: number;
}
interface TranscriptionStats {
    activeConnections: number;
    deepgramAvailable: boolean;
    callSids: string[];
    timestamp: number;
}
interface HealthCheckResult {
    status: string;
    reason?: string;
    activeConnections?: number;
    error?: string;
    timestamp: number;
}
export declare class TranscriptionManager {
    private deepgramClient;
    private activeConnections;
    constructor();
    static initializeTranscription(callSid: string, connectionData: ConnectionData): Promise<LiveClient | null>;
    createTranscription(callSid: string, connectionData: ConnectionData): Promise<LiveClient | null>;
    static closeTranscription(callSid: string, dgConnection: LiveClient): void;
    sendAudioToTranscription(callSid: string, audioBuffer: Buffer): void;
    getTranscriptionStatus(callSid: string): TranscriptionStatus;
    closeTranscription(callSid: string): void;
    closeAllTranscriptions(): void;
    cleanup(): void;
    getActiveTranscriptionCount(): number;
    getActiveTranscriptionCallSids(): string[];
    private emitTranscriptionEvent;
    getTranscriptionStats(): TranscriptionStats;
    healthCheck(): Promise<HealthCheckResult>;
    initializeLocalTranscription(sessionId: string, connectionData: ConnectionData): Promise<LiveClient | null>;
}
export {};
//# sourceMappingURL=transcription-manager.d.ts.map