/**
 * Audio Forwarding Utilities
 * Handles proper audio forwarding with sequence numbers for <PERSON><PERSON><PERSON> calls
 * Fixes the missing audio forwarding issues identified in the audit
 */
import { AudioProcessor } from './audio-processor';
import { ConnectionData } from '../types/global';
interface AudioData {
    data: string;
    mimeType?: string;
}
interface AudioForwardingStats {
    sessionId: string;
    sessionType?: string;
    audioForwardingEnabled: boolean;
    lastAudioSent: number;
    timeSinceLastAudio: number | null;
    sequenceNumber?: number;
    streamSid?: string | null;
    hasTwilioWs?: boolean;
    hasLocalWs?: boolean;
}
/**
 * Forward audio to Twilio WebSocket with proper sequence number handling
 */
export declare function forwardAudioToTwilio(sessionId: string, audio: AudioData, connectionData: ConnectionData, audioProcessor: AudioProcessor): Promise<boolean>;
/**
 * Forward audio to local testing WebSocket
 */
export declare function forwardAudioToLocal(sessionId: string, audio: AudioData, connectionData: ConnectionData): Promise<boolean>;
/**
 * Main audio forwarding function that routes to appropriate destination
 */
export declare function forwardAudio(sessionId: string, audio: AudioData, connectionData: ConnectionData, audioProcessor: AudioProcessor): Promise<boolean>;
/**
 * Initialize audio forwarding for a session
 */
export declare function initializeAudioForwarding(sessionId: string, connectionData: ConnectionData, sessionType: string): void;
/**
 * Get audio forwarding statistics
 */
export declare function getAudioForwardingStats(sessionId: string, connectionData: ConnectionData): AudioForwardingStats | null;
/**
 * Cleanup audio forwarding resources
 */
export declare function cleanupAudioForwarding(sessionId: string, connectionData: ConnectionData): void;
/**
 * Clean up audio buffer for a session (thread-safe)
 */
export declare function cleanupAudioBuffer(sessionId: string): void;
export {};
//# sourceMappingURL=audio-forwarding.d.ts.map