"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendAudioBufferToGemini = sendAudioBufferToGemini;
const logger_1 = require("../utils/logger");
/**
 * Send raw PCM audio to Gemini using connection data.
 * The buffer is converted to base64 before sending.
 */
async function sendAudioBufferToGemini(callSid, connectionData, pcmBuffer, mimeType = 'audio/pcm;rate=16000') {
    try {
        if (!connectionData || !connectionData.geminiSession) {
            logger_1.sessionLogger.warn(`⚠️ [${callSid}] No Gemini session available for audio send`);
            return;
        }
        const base64Audio = pcmBuffer.toString('base64');
        // Use sendRealtimeInput for audio data
        await connectionData.geminiSession.sendRealtimeInput({
            media: {
                data: base64Audio,
                mimeType: mimeType
            }
        });
        logger_1.sessionLogger.info(`✅ [${callSid}] Audio sent to Gemini successfully`);
    }
    catch (error) {
        logger_1.sessionLogger.error(`❌ [${callSid}] Error sending audio to Gemini`, error instanceof Error ? error : new Error(String(error)));
    }
}
//# sourceMappingURL=gemini-sender.js.map