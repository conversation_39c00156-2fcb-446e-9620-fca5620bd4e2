"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranscriptionManager = void 0;
// Deepgram transcription helper
const sdk_1 = require("@deepgram/sdk");
class TranscriptionManager {
    deepgramClient;
    activeConnections;
    constructor() {
        this.deepgramClient = process.env.DEEPGRAM_API_KEY ? (0, sdk_1.createClient)(process.env.DEEPGRAM_API_KEY) : null;
        this.activeConnections = new Map(); // Track active transcription connections
    }
    static async initializeTranscription(callSid, connectionData) {
        const manager = new TranscriptionManager();
        return await manager.createTranscription(callSid, connectionData);
    }
    async createTranscription(callSid, connectionData) {
        if (!this.deepgramClient) {
            console.warn(`⚠️ [${callSid}] Deepgram not available - transcription disabled`);
            return null;
        }
        if (!connectionData) {
            console.warn(`⚠️ [${callSid}] No connection data - transcription disabled`);
            return null;
        }
        try {
            const dgConnection = this.deepgramClient.listen.live({
                model: 'nova-2',
                language: 'en',
                smart_format: true,
                interim_results: true,
                utterance_end_ms: 1000,
                vad_events: true,
                encoding: 'mulaw',
                sample_rate: 8000,
                channels: 1
            });
            // Store event handlers for cleanup
            const eventHandlers = {};
            eventHandlers.open = () => {
                console.log(`🎤 [${callSid}] Deepgram transcription connection opened`);
                this.activeConnections.set(callSid, { connection: dgConnection, handlers: eventHandlers });
            };
            dgConnection.on(sdk_1.LiveTranscriptionEvents.Open, eventHandlers.open);
            eventHandlers.transcript = (data) => {
                try {
                    const transcript = data.channel?.alternatives?.[0]?.transcript;
                    const confidence = data.channel?.alternatives?.[0]?.confidence || 0.9;
                    if (transcript && data.is_final) {
                        console.log(`📝 [${callSid}] User said: "${transcript}"`);
                        // Add to full transcript log with bounds checking
                        if (connectionData && connectionData.fullTranscript) {
                            const MAX_TRANSCRIPT_SIZE = 1000;
                            connectionData.fullTranscript.push({
                                role: 'user',
                                content: transcript,
                                timestamp: Date.now(),
                                confidence: confidence
                            });
                            // Trim if exceeds limit
                            if (connectionData.fullTranscript.length > MAX_TRANSCRIPT_SIZE) {
                                connectionData.fullTranscript.splice(0, connectionData.fullTranscript.length - MAX_TRANSCRIPT_SIZE);
                            }
                        }
                        // Add to conversation log with bounds checking
                        if (connectionData && connectionData.conversationLog) {
                            const MAX_CONVERSATION_SIZE = 500;
                            connectionData.conversationLog.push({
                                role: 'user',
                                content: transcript,
                                timestamp: Date.now(),
                                source: 'deepgram',
                                confidence: confidence
                            });
                            // Trim if exceeds limit
                            if (connectionData.conversationLog.length > MAX_CONVERSATION_SIZE) {
                                connectionData.conversationLog.splice(0, connectionData.conversationLog.length - MAX_CONVERSATION_SIZE);
                            }
                        }
                        // Emit transcription event for other systems
                        this.emitTranscriptionEvent(callSid, {
                            transcript,
                            confidence: confidence,
                            timestamp: Date.now(),
                            isFinal: data.is_final
                        });
                    }
                }
                catch (error) {
                    console.warn(`⚠️ [${callSid}] Deepgram transcript processing error:`, error);
                }
            };
            dgConnection.on(sdk_1.LiveTranscriptionEvents.Transcript, eventHandlers.transcript);
            eventHandlers.error = (error) => {
                console.error(`❌ [${callSid}] Deepgram transcription error:`, error);
                this.closeTranscription(callSid);
            };
            dgConnection.on(sdk_1.LiveTranscriptionEvents.Error, eventHandlers.error);
            eventHandlers.close = () => {
                console.log(`🔌 [${callSid}] Deepgram transcription connection closed`);
                this.activeConnections.delete(callSid);
            };
            dgConnection.on(sdk_1.LiveTranscriptionEvents.Close, eventHandlers.close);
            return dgConnection;
        }
        catch (error) {
            console.error(`❌ [${callSid}] Failed to initialize Deepgram transcription:`, error);
            return null;
        }
    }
    static closeTranscription(callSid, dgConnection) {
        if (dgConnection && typeof dgConnection.finish === 'function') {
            try {
                dgConnection.finish();
                console.log(`🔌 [${callSid}] Deepgram transcription closed`);
            }
            catch (error) {
                console.warn(`⚠️ [${callSid}] Error closing Deepgram transcription:`, error);
            }
        }
    }
    // Send audio data to transcription service
    sendAudioToTranscription(callSid, audioBuffer) {
        const connectionData = this.activeConnections.get(callSid);
        const dgConnection = connectionData?.connection || connectionData;
        if (dgConnection && audioBuffer) {
            try {
                dgConnection.send(audioBuffer);
            }
            catch (error) {
                console.error(`❌ [${callSid}] Error sending audio to transcription:`, error);
            }
        }
    }
    // Get transcription status
    getTranscriptionStatus(callSid) {
        const dgConnection = this.activeConnections.get(callSid);
        return {
            isActive: !!dgConnection,
            connectionState: dgConnection ? 'connected' : 'disconnected',
            timestamp: Date.now()
        };
    }
    // Close transcription for a specific call
    closeTranscription(callSid) {
        const connectionData = this.activeConnections.get(callSid);
        if (connectionData) {
            // Remove all event listeners first
            if ('handlers' in connectionData && connectionData.handlers && connectionData.connection) {
                const conn = connectionData.connection;
                const handlers = connectionData.handlers;
                // Remove all registered event listeners
                if (handlers.open) {
                    conn.removeListener(sdk_1.LiveTranscriptionEvents.Open, handlers.open);
                }
                if (handlers.transcript) {
                    conn.removeListener(sdk_1.LiveTranscriptionEvents.Transcript, handlers.transcript);
                }
                if (handlers.error) {
                    conn.removeListener(sdk_1.LiveTranscriptionEvents.Error, handlers.error);
                }
                if (handlers.close) {
                    conn.removeListener(sdk_1.LiveTranscriptionEvents.Close, handlers.close);
                }
            }
            // Close the connection
            const connection = 'connection' in connectionData ? connectionData.connection : connectionData;
            TranscriptionManager.closeTranscription(callSid, connection);
            this.activeConnections.delete(callSid);
        }
    }
    // Close all active transcriptions
    closeAllTranscriptions() {
        for (const [callSid, connectionData] of this.activeConnections.entries()) {
            const connection = 'connection' in connectionData ? connectionData.connection : connectionData;
            TranscriptionManager.closeTranscription(callSid, connection);
        }
        this.activeConnections.clear();
        console.log('🔌 All transcription connections closed');
    }
    // Clean up all transcription resources (for shutdown)
    cleanup() {
        const connectionCount = this.activeConnections.size;
        this.closeAllTranscriptions();
        console.log(`🧹 TranscriptionManager: Cleaned up ${connectionCount} active transcription connections`);
    }
    // Get active transcription count
    getActiveTranscriptionCount() {
        return this.activeConnections.size;
    }
    // Get all active transcription call SIDs
    getActiveTranscriptionCallSids() {
        return Array.from(this.activeConnections.keys());
    }
    // Emit transcription event (can be extended for event system)
    emitTranscriptionEvent(callSid, transcriptionData) {
        // This could be extended to emit events to an event system
        // For now, just log debug info
        if (process.env.TRANSCRIPTION_DEBUG === 'true') {
            console.log(`📝 [${callSid}] Transcription event:`, {
                transcript: transcriptionData.transcript.substring(0, 50) + '...',
                confidence: transcriptionData.confidence,
                timestamp: new Date(transcriptionData.timestamp).toISOString()
            });
        }
    }
    // Get transcription statistics
    getTranscriptionStats() {
        return {
            activeConnections: this.activeConnections.size,
            deepgramAvailable: !!this.deepgramClient,
            callSids: Array.from(this.activeConnections.keys()),
            timestamp: Date.now()
        };
    }
    // Health check for transcription service
    async healthCheck() {
        try {
            if (!this.deepgramClient) {
                return {
                    status: 'unavailable',
                    reason: 'Deepgram API key not configured',
                    timestamp: Date.now()
                };
            }
            // Basic health check - just verify client exists
            return {
                status: 'healthy',
                activeConnections: this.activeConnections.size,
                timestamp: Date.now()
            };
        }
        catch (error) {
            return {
                status: 'error',
                error: error.message,
                timestamp: Date.now()
            };
        }
    }
    // Initialize transcription for local audio session
    async initializeLocalTranscription(sessionId, connectionData) {
        if (!this.deepgramClient) {
            console.warn(`⚠️ [${sessionId}] Deepgram not available for local transcription`);
            return null;
        }
        try {
            const dgConnection = this.deepgramClient.listen.live({
                model: 'nova-2',
                language: 'en',
                smart_format: true,
                interim_results: true,
                utterance_end_ms: 1000,
                vad_events: true,
                encoding: 'linear16', // Different encoding for local audio
                sample_rate: 16000, // Different sample rate for local audio
                channels: 1
            });
            dgConnection.on(sdk_1.LiveTranscriptionEvents.Open, () => {
                console.log(`🎤 [${sessionId}] Local Deepgram transcription connection opened`);
                this.activeConnections.set(sessionId, dgConnection);
            });
            dgConnection.on(sdk_1.LiveTranscriptionEvents.Transcript, (data) => {
                try {
                    const transcript = data.channel?.alternatives?.[0]?.transcript;
                    if (transcript && data.is_final) {
                        console.log(`📝 [${sessionId}] Local user said: "${transcript}"`);
                        // Add to conversation data with bounds checking
                        if (connectionData) {
                            if (connectionData.fullTranscript) {
                                const MAX_TRANSCRIPT_SIZE = 1000;
                                connectionData.fullTranscript.push({
                                    role: 'user',
                                    content: transcript,
                                    timestamp: Date.now(),
                                    confidence: data.channel.alternatives[0].confidence || 0.9,
                                    source: 'local_deepgram'
                                });
                                // Trim if exceeds limit
                                if (connectionData.fullTranscript.length > MAX_TRANSCRIPT_SIZE) {
                                    connectionData.fullTranscript.splice(0, connectionData.fullTranscript.length - MAX_TRANSCRIPT_SIZE);
                                }
                            }
                            if (connectionData.conversationLog) {
                                const MAX_CONVERSATION_SIZE = 500;
                                connectionData.conversationLog.push({
                                    role: 'user',
                                    content: transcript,
                                    timestamp: Date.now(),
                                    source: 'local_deepgram',
                                    confidence: data.channel.alternatives[0].confidence || 0.9
                                });
                                // Trim if exceeds limit
                                if (connectionData.conversationLog.length > MAX_CONVERSATION_SIZE) {
                                    connectionData.conversationLog.splice(0, connectionData.conversationLog.length - MAX_CONVERSATION_SIZE);
                                }
                            }
                        }
                    }
                }
                catch (error) {
                    console.warn(`⚠️ [${sessionId}] Local Deepgram transcript processing error:`, error);
                }
            });
            dgConnection.on(sdk_1.LiveTranscriptionEvents.Error, (error) => {
                console.error(`❌ [${sessionId}] Local Deepgram transcription error:`, error);
                this.activeConnections.delete(sessionId);
            });
            dgConnection.on(sdk_1.LiveTranscriptionEvents.Close, () => {
                console.log(`🔌 [${sessionId}] Local Deepgram transcription connection closed`);
                this.activeConnections.delete(sessionId);
            });
            return dgConnection;
        }
        catch (error) {
            console.error(`❌ [${sessionId}] Failed to initialize local Deepgram transcription:`, error);
            return null;
        }
    }
}
exports.TranscriptionManager = TranscriptionManager;
//# sourceMappingURL=transcription-manager.js.map