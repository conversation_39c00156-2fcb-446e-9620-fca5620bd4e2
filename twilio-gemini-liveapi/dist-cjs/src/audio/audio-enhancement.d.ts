import { AudioEnhancer } from './audio-enhancer';
/**
 * Audio Enhancement Module
 * Provides audio quality improvement and filtering functions
 * Extracted from audio-processor.ts for better modularity
 */
export declare class AudioEnhancement {
    private audioEnhancer;
    constructor();
    /**
     * Basic audio quality improvement with noise reduction and normalization (fallback)
     * @param pcmBuffer - Raw PCM audio buffer
     * @returns Enhanced PCM audio buffer
     */
    enhanceAudioQuality(pcmBuffer: Buffer): Buffer;
    /**
     * Advanced audio quality improvement using sophisticated algorithms
     * @param pcmBuffer - Raw PCM audio buffer
     * @returns Enhanced PCM audio buffer
     */
    enhanceAudioQualityAdvanced(pcmBuffer: Buffer): Buffer;
    /**
     * Enhance Gemini output audio quality using advanced processing
     * @param samples - Float32Array of audio samples from Gemini
     * @returns Enhanced audio samples
     */
    enhanceGeminiOutput(samples: Float32Array): Float32Array;
    /**
     * Basic Gemini output enhancement (fallback)
     * @param samples - Float32Array of audio samples from Gemini
     * @returns Enhanced audio samples
     */
    enhanceGeminiOutputBasic(samples: Float32Array): Float32Array;
    /**
     * Apply gentle compression to even out audio levels
     * @param samples - Input audio samples
     * @returns Compressed audio samples
     */
    applyCompression(samples: Float32Array): Float32Array;
    /**
     * Apply de-essing to reduce harsh sibilant sounds
     * @param samples - Input audio samples
     * @returns De-essed audio samples
     */
    applyDeEssing(samples: Float32Array): Float32Array;
    /**
     * Apply simple high-pass filter to remove low-frequency noise
     * @param samples - Float32Array of audio samples
     * @param cutoffFreq - Cutoff frequency in Hz
     * @param sampleRate - Sample rate in Hz
     * @returns Filtered audio samples
     */
    applyHighPassFilter(samples: Float32Array, cutoffFreq: number, sampleRate: number): Float32Array;
    /**
     * Normalize audio levels to prevent clipping and improve consistency
     * @param samples - Float32Array of audio samples
     * @returns Normalized audio samples
     */
    normalizeAudio(samples: Float32Array): Float32Array;
    /**
     * Prepare audio for Twilio transmission
     * @param samples - Input audio samples
     * @returns Processed audio samples optimized for Twilio
     */
    prepareForTwilio(samples: Float32Array): Float32Array;
    /**
     * Apply EQ optimized for phone transmission
     * @param samples - Input audio samples
     * @returns EQ'd audio samples
     */
    applyPhoneEQ(samples: Float32Array): Float32Array;
    /**
     * Apply gentle limiting to prevent clipping
     * @param samples - Input audio samples
     * @returns Limited audio samples
     */
    applyLimiter(samples: Float32Array): Float32Array;
    /**
     * Get the audio enhancer instance for external use
     * @returns AudioEnhancer instance
     */
    getAudioEnhancer(): AudioEnhancer;
}
//# sourceMappingURL=audio-enhancement.d.ts.map