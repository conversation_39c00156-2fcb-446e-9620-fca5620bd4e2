"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioEnhancement = void 0;
const audio_enhancer_1 = require("./audio-enhancer");
/**
 * Audio Enhancement Module
 * Provides audio quality improvement and filtering functions
 * Extracted from audio-processor.ts for better modularity
 */
class AudioEnhancement {
    audioEnhancer;
    constructor() {
        this.audioEnhancer = new audio_enhancer_1.AudioEnhancer();
    }
    /**
     * Basic audio quality improvement with noise reduction and normalization (fallback)
     * @param pcmBuffer - Raw PCM audio buffer
     * @returns Enhanced PCM audio buffer
     */
    enhanceAudioQuality(pcmBuffer) {
        try {
            // Convert to float32 for processing
            const samples = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < samples.length; i++) {
                samples[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }
            // Apply noise gate (remove very quiet samples that are likely noise)
            const noiseThreshold = 0.01; // Adjust based on testing
            for (let i = 0; i < samples.length; i++) {
                if (Math.abs(samples[i]) < noiseThreshold) {
                    samples[i] *= 0.1; // Reduce noise rather than eliminate completely
                }
            }
            // Apply simple high-pass filter to remove low-frequency noise
            const filteredSamples = this.applyHighPassFilter(samples, 80, 8000);
            // Normalize audio levels
            const normalizedSamples = this.normalizeAudio(filteredSamples);
            // Convert back to int16 PCM
            const enhancedBuffer = Buffer.alloc(pcmBuffer.length);
            for (let i = 0; i < normalizedSamples.length; i++) {
                const sample = Math.max(-1, Math.min(1, normalizedSamples[i]));
                enhancedBuffer.writeInt16LE(Math.round(sample * 32767), i * 2);
            }
            return enhancedBuffer;
        }
        catch (error) {
            console.error('❌ Error in audio enhancement:', error);
            return pcmBuffer; // Return original if enhancement fails
        }
    }
    /**
     * Advanced audio quality improvement using sophisticated algorithms
     * @param pcmBuffer - Raw PCM audio buffer
     * @returns Enhanced PCM audio buffer
     */
    enhanceAudioQualityAdvanced(pcmBuffer) {
        try {
            // Convert to float32 for processing
            const samples = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < samples.length; i++) {
                samples[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }
            // Apply advanced audio enhancement
            const enhancementOptions = {
                noiseReduction: process.env.AUDIO_NOISE_REDUCTION !== 'false',
                compression: process.env.AUDIO_COMPRESSION !== 'false',
                agc: process.env.AUDIO_AGC !== 'false',
                voiceEnhancement: process.env.AUDIO_VOICE_ENHANCEMENT !== 'false'
            };
            const enhancedSamples = this.audioEnhancer.enhance(samples, enhancementOptions);
            // Convert back to int16 PCM
            const enhancedBuffer = Buffer.alloc(pcmBuffer.length);
            for (let i = 0; i < enhancedSamples.length; i++) {
                const sample = Math.max(-1, Math.min(1, enhancedSamples[i]));
                enhancedBuffer.writeInt16LE(Math.round(sample * 32767), i * 2);
            }
            return enhancedBuffer;
        }
        catch (error) {
            console.error('❌ Error in advanced audio enhancement:', error);
            return this.enhanceAudioQuality(pcmBuffer); // Fallback to basic enhancement
        }
    }
    /**
     * Enhance Gemini output audio quality using advanced processing
     * @param samples - Float32Array of audio samples from Gemini
     * @returns Enhanced audio samples
     */
    enhanceGeminiOutput(samples) {
        try {
            // Use advanced audio enhancer for Gemini output
            const enhancementOptions = {
                noiseReduction: true,
                compression: true,
                agc: true,
                voiceEnhancement: true,
                deEssing: this.audioEnhancer.isDeEssingEnabled() // Use configurable de-essing
            };
            const enhanced = this.audioEnhancer.enhance(samples, enhancementOptions);
            // Apply additional Gemini-specific processing
            const compressed = this.applyCompression(enhanced);
            // Apply de-essing only if enabled (for backward compatibility with existing method)
            const deEssed = this.audioEnhancer.isDeEssingEnabled()
                ? this.applyDeEssing(compressed)
                : compressed;
            // Final normalization
            return this.normalizeAudio(deEssed);
        }
        catch (error) {
            console.error('❌ Error in advanced Gemini output enhancement:', error);
            // Fallback to basic enhancement
            return this.enhanceGeminiOutputBasic(samples);
        }
    }
    /**
     * Basic Gemini output enhancement (fallback)
     * @param samples - Float32Array of audio samples from Gemini
     * @returns Enhanced audio samples
     */
    enhanceGeminiOutputBasic(samples) {
        try {
            // Apply gentle compression to even out levels
            const compressed = this.applyCompression(samples);
            // Apply de-essing only if enabled
            const deEssed = this.audioEnhancer.isDeEssingEnabled()
                ? this.applyDeEssing(compressed)
                : compressed;
            // Final normalization
            return this.normalizeAudio(deEssed);
        }
        catch (error) {
            console.error('❌ Error in basic Gemini output enhancement:', error);
            return samples;
        }
    }
    /**
     * Apply gentle compression to even out audio levels
     * @param samples - Input audio samples
     * @returns Compressed audio samples
     */
    applyCompression(samples) {
        const threshold = 0.6;
        const ratio = 3.0;
        const compressed = new Float32Array(samples.length);
        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            const absSample = Math.abs(sample);
            if (absSample > threshold) {
                const excess = absSample - threshold;
                const compressedExcess = excess / ratio;
                const newLevel = threshold + compressedExcess;
                compressed[i] = sample >= 0 ? newLevel : -newLevel;
            }
            else {
                compressed[i] = sample;
            }
        }
        return compressed;
    }
    /**
     * Apply de-essing to reduce harsh sibilant sounds
     * @param samples - Input audio samples
     * @returns De-essed audio samples
     */
    applyDeEssing(samples) {
        // Simple de-essing using frequency-selective compression
        const deEssed = new Float32Array(samples.length);
        const lookAhead = 5; // samples
        for (let i = 0; i < samples.length; i++) {
            let highFreqEnergy = 0;
            // Calculate high-frequency energy in a small window
            for (let j = Math.max(0, i - lookAhead); j < Math.min(samples.length, i + lookAhead); j++) {
                if (j > 0) {
                    const diff = samples[j] - samples[j - 1];
                    highFreqEnergy += diff * diff;
                }
            }
            // Apply reduction if high-frequency energy is excessive
            const threshold = 0.1;
            if (highFreqEnergy > threshold) {
                const reduction = Math.min(0.7, threshold / highFreqEnergy);
                deEssed[i] = samples[i] * reduction;
            }
            else {
                deEssed[i] = samples[i];
            }
        }
        return deEssed;
    }
    /**
     * Apply simple high-pass filter to remove low-frequency noise
     * @param samples - Float32Array of audio samples
     * @param cutoffFreq - Cutoff frequency in Hz
     * @param sampleRate - Sample rate in Hz
     * @returns Filtered audio samples
     */
    applyHighPassFilter(samples, cutoffFreq, sampleRate) {
        const rc = 1.0 / (cutoffFreq * 2 * Math.PI);
        const dt = 1.0 / sampleRate;
        const alpha = rc / (rc + dt);
        const filtered = new Float32Array(samples.length);
        filtered[0] = samples[0];
        for (let i = 1; i < samples.length; i++) {
            filtered[i] = alpha * (filtered[i - 1] + samples[i] - samples[i - 1]);
        }
        return filtered;
    }
    /**
     * Normalize audio levels to prevent clipping and improve consistency
     * @param samples - Float32Array of audio samples
     * @returns Normalized audio samples
     */
    normalizeAudio(samples) {
        // Find peak amplitude
        let peak = 0;
        for (let i = 0; i < samples.length; i++) {
            peak = Math.max(peak, Math.abs(samples[i]));
        }
        // Avoid division by zero and over-normalization
        if (peak < 0.001) {
            return samples;
        }
        // Apply gentle normalization (don't normalize to full scale to avoid harshness)
        const targetLevel = 0.7;
        const gain = Math.min(targetLevel / peak, 3.0); // Limit gain to prevent over-amplification
        const normalized = new Float32Array(samples.length);
        for (let i = 0; i < samples.length; i++) {
            normalized[i] = samples[i] * gain;
        }
        return normalized;
    }
    /**
     * Prepare audio for Twilio transmission
     * @param samples - Input audio samples
     * @returns Processed audio samples optimized for Twilio
     */
    prepareForTwilio(samples) {
        // Apply final EQ to optimize for phone transmission
        const eqed = this.applyPhoneEQ(samples);
        // Apply gentle limiting to prevent clipping
        return this.applyLimiter(eqed);
    }
    /**
     * Apply EQ optimized for phone transmission
     * @param samples - Input audio samples
     * @returns EQ'd audio samples
     */
    applyPhoneEQ(samples) {
        // Simple EQ boost in the 1-3kHz range for better intelligibility
        const eqed = new Float32Array(samples.length);
        const boost = 1.2; // 20% boost
        // This is a simplified EQ - in production, you'd use proper filter design
        for (let i = 1; i < samples.length - 1; i++) {
            // Simple high-mid boost
            const highMid = (samples[i] - (samples[i - 1] + samples[i + 1]) / 2) * boost;
            eqed[i] = samples[i] + highMid * 0.3;
        }
        // Handle edges
        eqed[0] = samples[0];
        if (samples.length > 1) {
            eqed[samples.length - 1] = samples[samples.length - 1];
        }
        return eqed;
    }
    /**
     * Apply gentle limiting to prevent clipping
     * @param samples - Input audio samples
     * @returns Limited audio samples
     */
    applyLimiter(samples) {
        const limit = 0.95;
        const limited = new Float32Array(samples.length);
        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            if (Math.abs(sample) > limit) {
                // Soft limiting
                limited[i] = sample >= 0 ? limit : -limit;
            }
            else {
                limited[i] = sample;
            }
        }
        return limited;
    }
    /**
     * Get the audio enhancer instance for external use
     * @returns AudioEnhancer instance
     */
    getAudioEnhancer() {
        return this.audioEnhancer;
    }
}
exports.AudioEnhancement = AudioEnhancement;
//# sourceMappingURL=audio-enhancement.js.map