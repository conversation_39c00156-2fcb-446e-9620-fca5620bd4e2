interface EnhancementOptions {
    noiseReduction?: boolean;
    compression?: boolean;
    agc?: boolean;
    voiceEnhancement?: boolean;
    deEssing?: boolean;
}
interface AudioSettings {
    enableDeEssing: boolean;
    enableNoiseReduction: boolean;
    enableCompression: boolean;
    enableAGC: boolean;
    compressionRatio: number;
    noiseThreshold: number;
    agcTargetLevel: number;
}
export declare class AudioEnhancer {
    private settings;
    constructor();
    /**
     * Enhance audio samples with various processing options
     * @param samples - Float32Array of audio samples
     * @param options - Enhancement options
     * @returns Enhanced audio samples
     */
    enhance(samples: Float32Array, options?: EnhancementOptions): Float32Array;
    /**
     * Check if de-essing is enabled
     * @returns boolean indicating if de-essing is enabled
     */
    isDeEssingEnabled(): boolean;
    /**
     * Apply noise reduction to audio samples
     * @param samples - Input audio samples
     * @returns Processed audio samples
     */
    private applyNoiseReduction;
    /**
     * Apply compression to audio samples
     * @param samples - Input audio samples
     * @returns Compressed audio samples
     */
    private applyCompression;
    /**
     * Apply Automatic Gain Control (AGC)
     * @param samples - Input audio samples
     * @returns AGC-processed audio samples
     */
    private applyAGC;
    /**
     * Apply voice enhancement
     * @param samples - Input audio samples
     * @returns Enhanced audio samples
     */
    private applyVoiceEnhancement;
    /**
     * Apply de-essing to reduce harsh sibilant sounds
     * @param samples - Input audio samples
     * @returns De-essed audio samples
     */
    private applyDeEssing;
    /**
     * Update enhancement settings
     * @param newSettings - Partial settings to update
     */
    updateSettings(newSettings: Partial<AudioSettings>): void;
    /**
     * Get current enhancement settings
     * @returns Current audio settings
     */
    getSettings(): AudioSettings;
    /**
     * Get processing statistics
     * @returns Processing statistics object
     */
    getProcessingStats(): any;
}
export {};
//# sourceMappingURL=audio-enhancer.d.ts.map