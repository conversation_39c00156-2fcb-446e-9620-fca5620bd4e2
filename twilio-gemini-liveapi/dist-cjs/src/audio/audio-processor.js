"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertWebmToPCM16 = exports.AudioProcessor = void 0;
const audio_conversion_1 = require("./audio-conversion");
const audio_enhancer_1 = require("./audio-enhancer");
const audio_quality_1 = require("./audio-quality");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class AudioProcessor {
    static audioEnhancer = new audio_enhancer_1.AudioEnhancer();
    static audioQualityMonitor = audio_quality_1.audioQualityMonitor;
    converter;
    audioValidator;
    audioSettings;
    constructor() {
        this.converter = new audio_conversion_1.AudioConverter();
        this.audioValidator = new audio_quality_1.AudioBufferValidator({
            maxBufferSize: 10 * 1024 * 1024,
            minBufferSize: 1,
            allowedFormats: ['ulaw', 'pcm16', 'pcm32', 'float32'],
            maxSilencePercentage: 95,
            maxClippingPercentage: 10,
            enableDetailedAnalysis: false // Disable detailed analysis for production performance
        });
        this.audioSettings = {
            enableDeEssing: false,
            enableNoiseReduction: false,
            enableCompression: false,
            enableAGC: false,
            compressionRatio: 2.0,
            noiseThreshold: 0.01,
            agcTargetLevel: 0.7
        };
    }
    convertUlawToPCM(audioBuffer, skipEnhancement = false) {
        const pcm = this.converter.convertUlawToPCM(audioBuffer);
        // Skip enhancement in production for lower latency
        if (skipEnhancement || process.env.NODE_ENV === 'production') {
            return pcm;
        }
        const float = this.converter.pcmToFloat32Array(pcm);
        const enhanced = AudioProcessor.audioEnhancer.enhance(float, { noiseReduction: true });
        const out = Buffer.alloc(enhanced.length * 2);
        for (let i = 0; i < enhanced.length; i++) {
            const s = Math.max(-1, Math.min(1, enhanced[i]));
            out.writeInt16LE(Math.round(s * 32767), i * 2);
        }
        return out;
    }
    pcmToFloat32Array(pcmBuffer) {
        return this.converter.pcmToFloat32Array(pcmBuffer);
    }
    upsample8kTo16k(data) {
        return this.converter.upsample8kTo16k(data);
    }
    downsample24kTo8k(data) {
        return this.converter.downsample24kTo8k(data);
    }
    createGeminiAudioBlob(data) {
        return this.converter.createGeminiAudioBlob(data);
    }
    convertPCMToUlaw(audio) {
        return this.converter.convertPCMToUlaw(audio);
    }
    pcmToUlaw(pcm) {
        return this.converter.pcmToUlaw(pcm);
    }
    linearToUlaw(sample) {
        return this.converter.linearToUlaw(sample);
    }
    static convertWebmToPCM16(webmData, targetSampleRate = 16000) {
        return audio_conversion_1.AudioConverter.convertWebmToPCM16(webmData, targetSampleRate);
    }
    static decodeAudioData(webmData) {
        return audio_conversion_1.AudioConverter.decodeAudioData(webmData);
    }
    static convertToPCM16(audioBuffer, targetSampleRate) {
        return audio_conversion_1.AudioConverter.convertToPCM16(audioBuffer, targetSampleRate);
    }
    async saveAudioDebug(samples, filename, sampleRate = 8000) {
        if (process.env.AUDIO_DEBUG !== 'true') {
            return;
        }
        try {
            const pcmBuffer = Buffer.alloc(samples.length * 2);
            for (let i = 0; i < samples.length; i++) {
                const s = Math.max(-1, Math.min(1, samples[i]));
                pcmBuffer.writeInt16LE(Math.round(s * 32767), i * 2);
            }
            const dir = path.join(process.cwd(), 'audio-debug');
            try {
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
            }
            catch (mkdirError) {
                console.error('Failed to create audio debug directory:', mkdirError);
                return; // Don't crash if we can't create debug directory
            }
            const filepath = path.join(dir, `${filename}_${Date.now()}.pcm`);
            try {
                // Use async write to avoid blocking the event loop
                fs.writeFile(filepath, pcmBuffer, (writeError) => {
                    if (writeError) {
                        console.error('Failed to write audio debug file:', writeError);
                        // Don't crash if we can't write debug file
                    }
                });
            }
            catch (writeError) {
                console.error('Failed to write audio debug file:', writeError);
                // Don't crash if we can't write debug file
            }
        }
        catch (error) {
            console.error('Error in saveAudioDebug:', error);
            // Don't let debug functionality crash the application
        }
    }
    updateAudioSettings(newSettings) {
        this.audioSettings = { ...this.audioSettings, ...newSettings };
    }
    getAudioSettings() {
        return { ...this.audioSettings };
    }
    getValidationStats() {
        return this.audioValidator.getValidationStats();
    }
    resetValidationStats() {
        this.audioValidator.resetStats();
    }
    validateAudioBuffer(buffer, format = 'ulaw', sessionId) {
        return this.audioValidator.validateAudioBuffer(buffer, format, sessionId);
    }
    getAudioHealthReport() {
        return {
            validationStats: this.getValidationStats(),
            enhancerStats: AudioProcessor.audioEnhancer.getProcessingStats(),
            qualityMetrics: AudioProcessor.audioQualityMonitor.getSummary(),
            lastUpdate: new Date().toISOString()
        };
    }
}
exports.AudioProcessor = AudioProcessor;
var audio_conversion_2 = require("./audio-conversion");
Object.defineProperty(exports, "convertWebmToPCM16", { enumerable: true, get: function () { return audio_conversion_2.convertWebmToPCM16; } });
//# sourceMappingURL=audio-processor.js.map