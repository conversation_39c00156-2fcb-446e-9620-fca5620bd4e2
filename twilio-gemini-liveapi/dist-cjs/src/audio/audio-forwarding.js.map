{"version": 3, "file": "audio-forwarding.js", "sourceRoot": "", "sources": ["../../../src/audio/audio-forwarding.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;AA+CH,oDA8HC;AAKD,kDAqEC;AAKD,oCAyEC;AAKD,8DAqBC;AAKD,0DA0BC;AAKD,wDAiBC;AAoHD,gDAyBC;AA/hBD,8DAAuH;AACvH,4CAA8C;AAI9C,yDAAyD;AACzD,MAAM,WAAW,GAAG,IAAI,GAAG,EAA8E,CAAC;AAC1G,MAAM,eAAe,GAAG,EAAE,CAAC;AAC3B,MAAM,kBAAkB,GAAG,CAAC,CAAC;AAC7B,MAAM,cAAc,GAAG,IAAI,CAAC;AAE5B,0CAA0C;AAC1C,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAyB,CAAC;AA8B1D;;GAEG;AACI,KAAK,UAAU,oBAAoB,CACtC,SAAiB,EACjB,KAAgB,EAChB,cAA8B,EAC9B,cAA8B;IAE9B,IAAI,CAAC;QACD,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACxB,oBAAW,CAAC,IAAI,CAAC,uCAAuC,EAAE;gBACtD,QAAQ,EAAE,CAAC,CAAC,KAAK;gBACjB,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC;aAChC,EAAE,SAAS,CAAC,CAAC;YACd,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,QAAQ,GAAG,IAAA,oCAAkB,EAAC,cAAc,CAAC,CAAC;QACpD,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAA,mCAAiB,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAEhE,oBAAW,CAAC,IAAI,CAAC,uCAAuC,EAAE;YACtD,WAAW,EAAE,CAAC,CAAC,QAAQ;YACvB,OAAO,EAAE,OAAO;YAChB,YAAY,EAAE,CAAC,CAAC,cAAc,CAAC,SAAS;YACxC,cAAc,EAAE,cAAc,CAAC,cAAc;YAC7C,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;SACrC,EAAE,SAAS,CAAC,CAAC;QAEd,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,oBAAW,CAAC,KAAK,CAAC,sDAAsD,EAAE;gBACtE,aAAa,EAAE;oBACX,QAAQ,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ;oBACnC,SAAS,EAAE,CAAC,CAAC,cAAc,CAAC,EAAE;oBAC9B,QAAQ,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,IAAI,cAAc,CAAC,EAAE,CAAC;iBAC7D;aACJ,EAAE,SAAS,CAAC,CAAC;YACd,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;YACrB,oBAAW,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACtD,YAAY,EAAE,OAAO;gBACrB,UAAU,EAAE,QAAQ,CAAC,UAAU;aAClC,EAAE,SAAS,CAAC,CAAC;YACd,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;YAC5B,oBAAW,CAAC,KAAK,CAAC,sDAAsD,EAAE;gBACtE,YAAY,EAAE,CAAC,CAAC,cAAc,CAAC,SAAS;gBACxC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC;aAC9C,EAAE,SAAS,CAAC,CAAC;YACd,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,sDAAsD;QACtD,wFAAwF;QACxF,IAAI,cAA+B,CAAC;QACpC,IAAI,oBAA4B,CAAC;QAEjC,IAAI,CAAC;YACD,uCAAuC;YACvC,cAAc,GAAG,cAAc,EAAE,gBAAgB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEhE,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,oBAAW,CAAC,KAAK,CAAC,gCAAgC,EAAE;oBAChD,aAAa,EAAE,OAAO,KAAK,CAAC,IAAI;oBAChC,eAAe,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;oBACxC,aAAa,EAAE,KAAK,CAAC,QAAQ;iBAChC,EAAE,SAAS,CAAC,CAAC;gBACd,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,oEAAoE;YACpE,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;gBAClD,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACnC,CAAC,CAAC,cAAc,CAAC;YAErB,oBAAW,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC7C,kBAAkB,EAAE,oBAAoB,CAAC,MAAM;aAClD,EAAE,SAAS,CAAC,CAAC;QAElB,CAAC;QAAC,OAAO,eAAe,EAAE,CAAC;YACvB,oBAAW,CAAC,KAAK,CAAC,wBAAwB,EAAE,eAAwB,EAAE,SAAS,CAAC,CAAC;YACjF,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,2CAA2C;QAC3C,IAAI,OAAO,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;YACpD,cAAc,CAAC,cAAc,GAAG,CAAC,CAAC;YAClC,oBAAW,CAAC,KAAK,CAAC,yCAAyC,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;QAChF,CAAC;QAED,mDAAmD;QACnD,MAAM,UAAU,GAAuB;YACnC,KAAK,EAAE,OAAO;YACd,cAAc,EAAE,cAAc,CAAC,cAAc,CAAC,QAAQ,EAAE;YACxD,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,KAAK,EAAE;gBACH,OAAO,EAAE,oBAAoB;aAChC;SACJ,CAAC;QAEF,2BAA2B;QAC3B,MAAM,OAAO,GAAG,IAAA,mCAAiB,EAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAEnE,IAAI,OAAO,EAAE,CAAC;YACV,qEAAqE;YACrE,cAAc,CAAC,cAAc,GAAG,CAAC,cAAc,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,gBAAgB,CAAC;YAC9F,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE1C,oBAAW,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACtC,cAAc,EAAE,cAAc,CAAC,cAAc,GAAG,CAAC;gBACjD,SAAS,EAAE,oBAAoB,CAAC,MAAM;aACzC,EAAE,SAAS,CAAC,CAAC;YAEd,sEAAsE;YAEtE,OAAO,IAAI,CAAC;QAChB,CAAC;aAAM,CAAC;YACJ,oBAAW,CAAC,KAAK,CAAC,0CAA0C,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;YAC7E,OAAO,KAAK,CAAC;QACjB,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,oBAAW,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;QACjF,OAAO,KAAK,CAAC;IACjB,CAAC;AACL,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,mBAAmB,CACrC,SAAiB,EACjB,KAAgB,EAChB,cAA8B;IAE9B,IAAI,CAAC;QACD,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACxB,oBAAW,CAAC,IAAI,CAAC,gDAAgD,EAAE;gBAC/D,QAAQ,EAAE,CAAC,CAAC,KAAK;gBACjB,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC;aAChC,EAAE,SAAS,CAAC,CAAC;YACd,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,OAAO,GAAG,IAAA,mCAAiB,EAAC,cAAc,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,IAAA,mCAAiB,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAE9D,oBAAW,CAAC,IAAI,CAAC,sCAAsC,EAAE;YACrD,UAAU,EAAE,CAAC,CAAC,OAAO;YACrB,OAAO,EAAE,OAAO;YAChB,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;YAClC,QAAQ,EAAE,KAAK,CAAC,QAAQ;SAC3B,EAAE,SAAS,CAAC,CAAC;QAEd,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,oBAAW,CAAC,KAAK,CAAC,qDAAqD,EAAE;gBACrE,aAAa,EAAE;oBACX,OAAO,EAAE,CAAC,CAAC,cAAc,CAAC,OAAO;oBACjC,SAAS,EAAE,CAAC,CAAC,cAAc,CAAC,EAAE;oBAC9B,QAAQ,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,EAAE,CAAC;iBAC5D;aACJ,EAAE,SAAS,CAAC,CAAC;YACd,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;YACrB,oBAAW,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACrD,YAAY,EAAE,OAAO;gBACrB,UAAU,EAAE,OAAO,CAAC,UAAU;aACjC,EAAE,SAAS,CAAC,CAAC;YACd,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,oEAAoE;QACpE,MAAM,YAAY,GAAiB;YAC/B,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,sCAAsC;YACzD,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,sBAAsB,EAAE,wCAAwC;YAC5F,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS,EAAE,SAAS;SACvB,CAAC;QAEF,MAAM,OAAO,GAAG,IAAA,mCAAiB,EAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QAEpE,IAAI,OAAO,EAAE,CAAC;YACV,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC1C,oBAAW,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC/C,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;aAC/B,EAAE,SAAS,CAAC,CAAC;YACd,OAAO,IAAI,CAAC;QAChB,CAAC;aAAM,CAAC;YACJ,oBAAW,CAAC,KAAK,CAAC,yCAAyC,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;YAC5E,OAAO,KAAK,CAAC;QACjB,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,oBAAW,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;QAC1F,OAAO,KAAK,CAAC;IACjB,CAAC;AACL,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,YAAY,CAC9B,SAAiB,EACjB,KAAgB,EAChB,cAA8B,EAC9B,cAA8B;IAE9B,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5B,oBAAW,CAAC,IAAI,CAAC,oDAAoD,EAAE;YACnE,QAAQ,EAAE,CAAC,CAAC,KAAK;YACjB,iBAAiB,EAAE,CAAC,CAAC,cAAc;YACnC,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;SAC5C,EAAE,SAAS,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,wCAAwC;IACxC,MAAM,QAAQ,GAAG,IAAA,oCAAkB,EAAC,cAAc,CAAC,CAAC;IACpD,MAAM,OAAO,GAAG,IAAA,mCAAiB,EAAC,cAAc,CAAC,CAAC;IAClD,MAAM,SAAS,GAAG,cAAc,CAAC,EAAE,CAAC;IAEpC,oBAAW,CAAC,IAAI,CAAC,oCAAoC,EAAE;QACnD,WAAW,EAAE,cAAc,CAAC,WAAW;QACvC,YAAY,EAAE,cAAc,CAAC,YAAY;QACzC,SAAS,EAAE,cAAc,CAAC,SAAS;QACnC,sBAAsB,EAAE,cAAc,CAAC,sBAAsB;QAC7D,WAAW,EAAE,CAAC,CAAC,QAAQ;QACvB,UAAU,EAAE,CAAC,CAAC,OAAO;QACrB,YAAY,EAAE,CAAC,CAAC,SAAS;QACzB,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAA,mCAAiB,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM;QAC9D,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,IAAA,mCAAiB,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM;QAC3D,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC,IAAA,mCAAiB,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM;QACjE,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;QAClC,aAAa,EAAE,KAAK,CAAC,QAAQ;KAChC,EAAE,SAAS,CAAC,CAAC;IAEd,+CAA+C;IAC/C,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,IAAI,SAAS,CAAC;IAC5D,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,IAAI,WAAW,KAAK,aAAa,CAAC;IAElF,oCAAoC;IACpC,oBAAW,CAAC,IAAI,CAAC,2BAA2B,EAAE;QAC1C,WAAW;QACX,YAAY;QACZ,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;KAC/C,EAAE,SAAS,CAAC,CAAC;IAEd,wDAAwD;IACxD,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAA,mCAAiB,EAAC,cAAc,CAAC,CAAC;IACxG,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,IAAA,mCAAiB,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAElE,IAAI,CAAC,SAAS,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;QACnC,oBAAW,CAAC,IAAI,CAAC,2BAA2B,OAAO,uCAAuC,EAAE;YACxF,YAAY;YACZ,OAAO;YACP,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;SACrC,EAAE,SAAS,CAAC,CAAC;QAEd,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC9B,OAAO,KAAK,CAAC,CAAC,uCAAuC;IACzD,CAAC;IAED,qDAAqD;IACrD,MAAM,oBAAoB,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAEtE,IAAI,YAAY,EAAE,CAAC;QACf,MAAM,MAAM,GAAG,MAAM,oBAAoB,CAAC,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;QAC5F,oBAAW,CAAC,IAAI,CAAC,sCAAsC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;QACvG,OAAO,MAAM,CAAC;IAClB,CAAC;SAAM,CAAC;QACJ,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,SAAS,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;QAC3E,oBAAW,CAAC,IAAI,CAAC,qCAAqC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;QACtG,OAAO,MAAM,CAAC;IAClB,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CACrC,SAAiB,EACjB,cAA8B,EAC9B,WAAmB;IAEnB,IAAI,CAAC,cAAc,EAAE,CAAC;QAClB,oBAAW,CAAC,IAAI,CAAC,wDAAwD,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;QAC1F,OAAO;IACX,CAAC;IAED,wDAAwD;IACxD,IAAI,WAAW,KAAK,aAAa,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;QAC/D,cAAc,CAAC,sBAAsB,GAAG,IAAI,CAAC;QAC7C,cAAc,CAAC,cAAc,GAAG,CAAC,CAAC;QAClC,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC;QACjC,oBAAW,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;IAC3E,CAAC;SAAM,CAAC;QACJ,cAAc,CAAC,sBAAsB,GAAG,IAAI,CAAC;QAC7C,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC;QACjC,oBAAW,CAAC,IAAI,CAAC,oCAAoC,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;IAC1E,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CACnC,SAAiB,EACjB,cAA8B;IAE9B,IAAI,CAAC,cAAc,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,KAAK,GAAyB;QAChC,SAAS;QACT,WAAW,EAAE,cAAc,CAAC,WAAW;QACvC,sBAAsB,EAAE,cAAc,CAAC,sBAAsB,IAAI,KAAK;QACtE,aAAa,EAAE,cAAc,CAAC,aAAa,IAAI,CAAC;QAChD,kBAAkB,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;KACtG,CAAC;IAEF,4BAA4B;IAC5B,IAAI,cAAc,CAAC,YAAY,IAAI,cAAc,CAAC,WAAW,KAAK,aAAa,EAAE,CAAC;QAC9E,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,IAAI,CAAC,CAAC;QAC1D,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC;QACnD,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,IAAI,cAAc,CAAC,EAAE,CAAC,CAAC;IACzE,CAAC;SAAM,CAAC;QACJ,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,EAAE,CAAC,CAAC;IACvE,CAAC;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CAClC,SAAiB,EACjB,cAA8B;IAE9B,IAAI,CAAC,cAAc,EAAE,CAAC;QAClB,OAAO;IACX,CAAC;IAED,+BAA+B;IAC/B,cAAc,CAAC,sBAAsB,GAAG,KAAK,CAAC;IAC9C,cAAc,CAAC,cAAc,GAAG,CAAC,CAAC;IAClC,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC;IAEjC,yCAAyC;IACzC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAE9B,oBAAW,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;AAC3E,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,SAAiB,EAAE,KAAgB;IACpD,kDAAkD;IAClD,MAAM,OAAO,GAAG,gBAAgB,SAAS,EAAE,CAAC;IAC5C,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;IAEvE,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE;QAClC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;QAE3C,0CAA0C;QAC1C,OAAO,MAAM,CAAC,MAAM,IAAI,eAAe,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;YAC/B,oBAAW,CAAC,IAAI,CAAC,0CAA0C,EAAE;gBACzD,gBAAgB,EAAE,OAAO,EAAE,SAAS;gBACpC,UAAU,EAAE,MAAM,CAAC,MAAM;aAC5B,EAAE,SAAS,CAAC,CAAC;QAClB,CAAC;QAED,MAAM,CAAC,IAAI,CAAC;YACR,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,UAAU,EAAE,CAAC;SAChB,CAAC,CAAC;QAEH,oBAAW,CAAC,IAAI,CAAC,sCAAsC,EAAE;YACrD,UAAU,EAAE,MAAM,CAAC,MAAM;YACzB,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;SACrC,EAAE,SAAS,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,oBAAW,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAEvC,mDAAmD;IACnD,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE;QACjB,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,OAAO,EAAE,CAAC;YAC5C,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,oBAAoB,CAC/B,SAAiB,EACjB,cAA8B,EAC9B,cAA8B;IAE9B,4DAA4D;IAC5D,MAAM,OAAO,GAAG,gBAAgB,SAAS,EAAE,CAAC;IAC5C,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;IAEvE,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;QAC5C,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QAED,oBAAW,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC7C,UAAU,EAAE,MAAM,CAAC,MAAM;SAC5B,EAAE,SAAS,CAAC,CAAC;QAEd,kEAAkE;QAClE,MAAM,cAAc,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;QACnC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,eAAe;QAElC,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;YAChC,IAAI,CAAC;gBACD,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;gBAC1F,IAAI,CAAC,OAAO,EAAE,CAAC;oBACX,kEAAkE;oBAClE,IAAI,IAAI,CAAC,UAAU,GAAG,kBAAkB,EAAE,CAAC;wBACvC,IAAI,CAAC,UAAU,EAAE,CAAC;wBAClB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClB,oBAAW,CAAC,IAAI,CAAC,+BAA+B,EAAE;4BAC9C,UAAU,EAAE,IAAI,CAAC,UAAU;4BAC3B,UAAU,EAAE,kBAAkB;yBACjC,EAAE,SAAS,CAAC,CAAC;oBAClB,CAAC;yBAAM,CAAC;wBACJ,oBAAW,CAAC,KAAK,CAAC,0CAA0C,EAAE;4BAC1D,UAAU,EAAE,IAAI,CAAC,UAAU;4BAC3B,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;yBACrC,EAAE,SAAS,CAAC,CAAC;oBAClB,CAAC;gBACL,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,oBAAW,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;YACtF,CAAC;QACL,CAAC;IACL,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,oBAAW,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;IACrF,CAAC,CAAC,CAAC;IAEH,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IAE3C,mDAAmD;IACnD,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QACrB,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;YAChD,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,SAAiB;IAChD,2EAA2E;IAC3E,MAAM,OAAO,GAAG,gBAAgB,SAAS,EAAE,CAAC;IAC5C,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;IAEvE,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE;QACtC,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,MAAM,EAAE,CAAC;YACT,oBAAW,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBAC5C,eAAe,EAAE,MAAM,CAAC,MAAM;aACjC,EAAE,SAAS,CAAC,CAAC;YACd,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;IACL,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,oBAAW,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;IACnF,CAAC,CAAC,CAAC;IAEH,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IAE3C,mDAAmD;IACnD,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QACrB,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;YAChD,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC"}