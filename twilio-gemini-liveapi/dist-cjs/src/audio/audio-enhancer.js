"use strict";
// Audio Enhancement Module
// Provides advanced audio processing capabilities for voice quality improvement
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioEnhancer = void 0;
class AudioEnhancer {
    settings;
    constructor() {
        this.settings = {
            enableDeEssing: process.env.AUDIO_DE_ESSING !== 'false',
            enableNoiseReduction: process.env.AUDIO_NOISE_REDUCTION !== 'false',
            enableCompression: process.env.AUDIO_COMPRESSION !== 'false',
            enableAGC: process.env.AUDIO_AGC !== 'false',
            compressionRatio: parseFloat(process.env.AUDIO_COMPRESSION_RATIO || '2.0'),
            noiseThreshold: parseFloat(process.env.AUDIO_NOISE_THRESHOLD || '0.01'),
            agcTargetLevel: parseFloat(process.env.AUDIO_AGC_TARGET || '0.7')
        };
    }
    /**
     * Enhance audio samples with various processing options
     * @param samples - Float32Array of audio samples
     * @param options - Enhancement options
     * @returns Enhanced audio samples
     */
    enhance(samples, options = {}) {
        let enhanced = new Float32Array(samples);
        try {
            // Apply noise reduction if enabled
            if (options.noiseReduction && this.settings.enableNoiseReduction) {
                enhanced = this.applyNoiseReduction(enhanced);
            }
            // Apply compression if enabled
            if (options.compression && this.settings.enableCompression) {
                enhanced = this.applyCompression(enhanced);
            }
            // Apply AGC if enabled
            if (options.agc && this.settings.enableAGC) {
                enhanced = this.applyAGC(enhanced);
            }
            // Apply voice enhancement if enabled
            if (options.voiceEnhancement) {
                enhanced = this.applyVoiceEnhancement(enhanced);
            }
            // Apply de-essing if enabled
            if (options.deEssing && this.settings.enableDeEssing) {
                enhanced = this.applyDeEssing(enhanced);
            }
            return enhanced;
        }
        catch (error) {
            console.error('❌ Error in audio enhancement:', error);
            return samples; // Return original samples on error
        }
    }
    /**
     * Check if de-essing is enabled
     * @returns boolean indicating if de-essing is enabled
     */
    isDeEssingEnabled() {
        return this.settings.enableDeEssing;
    }
    /**
     * Apply noise reduction to audio samples
     * @param samples - Input audio samples
     * @returns Processed audio samples
     */
    applyNoiseReduction(samples) {
        const processed = new Float32Array(samples.length);
        const threshold = this.settings.noiseThreshold;
        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            const magnitude = Math.abs(sample);
            if (magnitude < threshold) {
                // Reduce low-level noise
                processed[i] = sample * 0.1;
            }
            else {
                processed[i] = sample;
            }
        }
        return processed;
    }
    /**
     * Apply compression to audio samples
     * @param samples - Input audio samples
     * @returns Compressed audio samples
     */
    applyCompression(samples) {
        const processed = new Float32Array(samples.length);
        const ratio = this.settings.compressionRatio;
        const threshold = 0.5;
        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            const magnitude = Math.abs(sample);
            if (magnitude > threshold) {
                // Apply compression above threshold
                const excess = magnitude - threshold;
                const compressedExcess = excess / ratio;
                const newMagnitude = threshold + compressedExcess;
                processed[i] = sample >= 0 ? newMagnitude : -newMagnitude;
            }
            else {
                processed[i] = sample;
            }
        }
        return processed;
    }
    /**
     * Apply Automatic Gain Control (AGC)
     * @param samples - Input audio samples
     * @returns AGC-processed audio samples
     */
    applyAGC(samples) {
        const processed = new Float32Array(samples.length);
        const targetLevel = this.settings.agcTargetLevel;
        // Calculate RMS level
        let rmsSum = 0;
        for (let i = 0; i < samples.length; i++) {
            rmsSum += samples[i] * samples[i];
        }
        const rmsLevel = Math.sqrt(rmsSum / samples.length);
        if (rmsLevel > 0) {
            const gain = targetLevel / rmsLevel;
            const limitedGain = Math.min(gain, 4.0); // Limit maximum gain
            for (let i = 0; i < samples.length; i++) {
                processed[i] = Math.max(-1, Math.min(1, samples[i] * limitedGain));
            }
        }
        else {
            processed.set(samples);
        }
        return processed;
    }
    /**
     * Apply voice enhancement
     * @param samples - Input audio samples
     * @returns Enhanced audio samples
     */
    applyVoiceEnhancement(samples) {
        // Simple voice enhancement - boost mid frequencies slightly
        const processed = new Float32Array(samples.length);
        for (let i = 0; i < samples.length; i++) {
            // Apply gentle boost to voice frequencies
            processed[i] = samples[i] * 1.1;
            // Ensure no clipping
            processed[i] = Math.max(-1, Math.min(1, processed[i]));
        }
        return processed;
    }
    /**
     * Apply de-essing to reduce harsh sibilant sounds
     * @param samples - Input audio samples
     * @returns De-essed audio samples
     */
    applyDeEssing(samples) {
        const processed = new Float32Array(samples.length);
        const sibilantThreshold = 0.7;
        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            const magnitude = Math.abs(sample);
            if (magnitude > sibilantThreshold) {
                // Reduce harsh sibilants
                processed[i] = sample * 0.8;
            }
            else {
                processed[i] = sample;
            }
        }
        return processed;
    }
    /**
     * Update enhancement settings
     * @param newSettings - Partial settings to update
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
    }
    /**
     * Get current enhancement settings
     * @returns Current audio settings
     */
    getSettings() {
        return { ...this.settings };
    }
    /**
     * Get processing statistics
     * @returns Processing statistics object
     */
    getProcessingStats() {
        return {
            settings: this.getSettings(),
            enhancementEnabled: true,
            supportedFeatures: [
                'noiseReduction',
                'compression',
                'agc',
                'voiceEnhancement',
                'deEssing'
            ]
        };
    }
}
exports.AudioEnhancer = AudioEnhancer;
//# sourceMappingURL=audio-enhancer.js.map