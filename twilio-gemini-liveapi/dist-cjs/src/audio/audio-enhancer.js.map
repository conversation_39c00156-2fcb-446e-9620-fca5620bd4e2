{"version": 3, "file": "audio-enhancer.js", "sourceRoot": "", "sources": ["../../../src/audio/audio-enhancer.ts"], "names": [], "mappings": ";AAAA,2BAA2B;AAC3B,gFAAgF;;;AAoBhF,MAAa,aAAa;IACd,QAAQ,CAAgB;IAEhC;QACI,IAAI,CAAC,QAAQ,GAAG;YACZ,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,OAAO;YACvD,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,OAAO;YACnE,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,OAAO;YAC5D,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,KAAK,OAAO;YAC5C,gBAAgB,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,KAAK,CAAC;YAC1E,cAAc,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,MAAM,CAAC;YACvE,cAAc,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,KAAK,CAAC;SACpE,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACH,OAAO,CAAC,OAAqB,EAAE,UAA8B,EAAE;QAC3D,IAAI,QAAQ,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC;YACD,mCAAmC;YACnC,IAAI,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;gBAC/D,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAClD,CAAC;YAED,+BAA+B;YAC/B,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;gBACzD,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC/C,CAAC;YAED,uBAAuB;YACvB,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACzC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACvC,CAAC;YAED,qCAAqC;YACrC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YACpD,CAAC;YAED,6BAA6B;YAC7B,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;gBACnD,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5C,CAAC;YAED,OAAO,QAAQ,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,OAAO,CAAC,CAAC,mCAAmC;QACvD,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACK,mBAAmB,CAAC,OAAqB;QAC7C,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;QAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAEnC,IAAI,SAAS,GAAG,SAAS,EAAE,CAAC;gBACxB,yBAAyB;gBACzB,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACJ,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;YAC1B,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACK,gBAAgB,CAAC,OAAqB;QAC1C,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAC7C,MAAM,SAAS,GAAG,GAAG,CAAC;QAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAEnC,IAAI,SAAS,GAAG,SAAS,EAAE,CAAC;gBACxB,oCAAoC;gBACpC,MAAM,MAAM,GAAG,SAAS,GAAG,SAAS,CAAC;gBACrC,MAAM,gBAAgB,GAAG,MAAM,GAAG,KAAK,CAAC;gBACxC,MAAM,YAAY,GAAG,SAAS,GAAG,gBAAgB,CAAC;gBAClD,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACJ,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;YAC1B,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACK,QAAQ,CAAC,OAAqB;QAClC,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;QAEjD,sBAAsB;QACtB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAEpD,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,GAAG,WAAW,GAAG,QAAQ,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,qBAAqB;YAE9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;YACvE,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACK,qBAAqB,CAAC,OAAqB;QAC/C,4DAA4D;QAC5D,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAEnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,0CAA0C;YAC1C,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAChC,qBAAqB;YACrB,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,OAAqB;QACvC,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACnD,MAAM,iBAAiB,GAAG,GAAG,CAAC;QAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAEnC,IAAI,SAAS,GAAG,iBAAiB,EAAE,CAAC;gBAChC,yBAAyB;gBACzB,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACJ,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;YAC1B,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,WAAmC;QAC9C,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,EAAE,CAAC;IACzD,CAAC;IAED;;;OAGG;IACH,WAAW;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,kBAAkB;QACd,OAAO;YACH,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,kBAAkB,EAAE,IAAI;YACxB,iBAAiB,EAAE;gBACf,gBAAgB;gBAChB,aAAa;gBACb,KAAK;gBACL,kBAAkB;gBAClB,UAAU;aACb;SACJ,CAAC;IACN,CAAC;CACJ;AA/ND,sCA+NC"}