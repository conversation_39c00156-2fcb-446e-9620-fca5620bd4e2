import { AudioEnhancer } from './audio-enhancer';
import { AudioValidationResult } from './audio-quality';
import { AudioSettings, MockAudioBuffer } from './audio-types';
export declare class AudioProcessor {
    static audioEnhancer: AudioEnhancer;
    static audioQualityMonitor: {
        metrics: import("./audio-quality").AudioQualityMetrics;
        analyze(samples: Float32Array, label?: string): import("./audio-quality").AudioMetrics | null;
        calculateMetrics(samples: Float32Array): import("./audio-quality").AudioMetrics;
        updateGlobalMetrics(metrics: import("./audio-quality").AudioMetrics): void;
        getSummary(): import("./audio-quality").AudioQualitySummary;
        reset(): void;
    };
    private converter;
    private audioValidator;
    private audioSettings;
    constructor();
    convertUlawToPCM(audioBuffer: Buffer, skipEnhancement?: boolean): Buffer;
    pcmToFloat32Array(pcmBuffer: Buffer): Float32Array;
    upsample8kTo16k(data: Float32Array): Float32Array;
    downsample24kTo8k(data: Float32Array): Float32Array;
    createGeminiAudioBlob(data: Float32Array): {
        data: string;
        mimeType: string;
    };
    convertPCMToUlaw(audio: string | Buffer): string | Buffer;
    pcmToUlaw(pcm: Buffer): Buffer;
    linearToUlaw(sample: number): number;
    static convertWebmToPCM16(webmData: Buffer, targetSampleRate?: number): Buffer;
    static decodeAudioData(webmData: Buffer): MockAudioBuffer;
    static convertToPCM16(audioBuffer: MockAudioBuffer, targetSampleRate: number): Buffer;
    saveAudioDebug(samples: Float32Array, filename: string, sampleRate?: number): Promise<void>;
    updateAudioSettings(newSettings: Partial<AudioSettings>): void;
    getAudioSettings(): AudioSettings;
    getValidationStats(): Record<string, number>;
    resetValidationStats(): void;
    validateAudioBuffer(buffer: Buffer, format?: string, sessionId?: string): AudioValidationResult;
    getAudioHealthReport(): {
        validationStats: Record<string, number>;
        enhancerStats: any;
        qualityMetrics: any;
        lastUpdate: string;
    };
}
export { convertWebmToPCM16 } from './audio-conversion';
//# sourceMappingURL=audio-processor.d.ts.map