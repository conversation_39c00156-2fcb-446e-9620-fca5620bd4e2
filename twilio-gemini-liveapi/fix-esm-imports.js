#!/usr/bin/env node

import { readdir, readFile, writeFile } from 'fs/promises';
import { join, extname } from 'path';

async function fixImports(dir) {
  const files = await readdir(dir, { withFileTypes: true });
  
  for (const file of files) {
    const fullPath = join(dir, file.name);
    
    if (file.isDirectory()) {
      await fixImports(fullPath);
    } else if (file.isFile() && extname(file.name) === '.js') {
      let content = await readFile(fullPath, 'utf8');
      
      // Fix relative imports without .js extension
      content = content.replace(
        /from\s+['"](\.\.?\/[^'"]+)(?<!\.js)['"]/g,
        (match, importPath) => {
          // Skip if it's already a .js file or external module
          if (importPath.endsWith('.js') || !importPath.startsWith('.')) {
            return match;
          }
          // Check if it's a directory import that should point to index.js
          if (importPath.includes('/websocket') || importPath.includes('/types')) {
            // For directory imports, add /index.js
            return match.replace(importPath, `${importPath}/index.js`);
          }
          return match.replace(importPath, `${importPath}.js`);
        }
      );
      
      // Fix dynamic imports
      content = content.replace(
        /import\(['"](\.\.?\/[^'"]+)(?<!\.js)['"]\)/g,
        (match, importPath) => {
          if (importPath.endsWith('.js') || !importPath.startsWith('.')) {
            return match;
          }
          // Check if it's a directory import that should point to index.js
          if (importPath.includes('/websocket') || importPath.includes('/types')) {
            return match.replace(importPath, `${importPath}/index.js`);
          }
          return match.replace(importPath, `${importPath}.js`);
        }
      );
      
      await writeFile(fullPath, content);
    }
  }
}

console.log('Fixing ESM imports in dist folder...');
await fixImports('./dist');
console.log('Done!');