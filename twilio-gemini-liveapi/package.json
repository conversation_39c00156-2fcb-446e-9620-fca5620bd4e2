{"name": "call_node_gemini", "version": "1.0.0", "type": "module", "engines": {"node": ">=18 <19"}, "imports": {"dotenv": "./src/utils/dotenv-stub.ts"}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start": "node --es-module-specifier-resolution=node dist/index.js", "start:ts": "tsx index.ts", "dev": "tsx watch index.ts", "debug": "tsx --inspect index.ts", "type-check": "tsc --noEmit", "test": "NODE_ENV=test npx tsx --test test/", "test:session-fixes": "node test-session-fixes.js", "test:4-flows": "NODE_ENV=test node --test test/session-handling-fixes.test.js", "test:real-calls": "node test-real-calls.js", "test:validate": "node validate-session-fixes.js", "test:workflow": "NODE_ENV=test node --test test/workflow-integration.test.js", "test:real-gemini": "NODE_ENV=test node --test test/real-gemini-integration.test.js", "test:production-fixes": "NODE_ENV=test node --test test/production-fixes-validation.test.js", "test:integration": "npm run test:real-gemini && npm run test:production-fixes", "test:all": "npm run test && npm run test:session-fixes && npm run test:integration", "lint": "eslint *.ts src/**/*.ts", "lint:fix": "eslint *.ts src/**/*.ts --fix", "audit": "pnpm audit", "update": "pnpm update", "clean": "rm -rf node_modules audio-debug data/*.json dist", "clean:audio": "rm -rf audio-debug", "clean:build": "rm -rf dist", "health": "curl -s http://localhost:${PORT:-3101}/health | jq", "audio-quality": "curl -s http://localhost:${PORT:-3101}/api/audio-quality | jq", "monitor": "concurrently \"pnpm run dev\" \"pnpm run health\" \"pnpm run audio-quality\""}, "dependencies": {"@deepgram/sdk": "^4.7.0", "@fastify/compress": "^8.1.0", "@fastify/cors": "^11.0.1", "@fastify/formbody": "latest", "@fastify/helmet": "^13.0.1", "@fastify/rate-limit": "^10.3.0", "@fastify/static": "^8.1.1", "@fastify/websocket": "latest", "@google/genai": "^0.9.0", "@google/generative-ai": "^0.21.0", "@supabase/supabase-js": "^2.50.2", "@tensorflow/tfjs-node": "^4.22.0", "@types/node": "^22.15.32", "audio-buffer-utils": "^5.1.2", "audio-context-polyfill": "^1.0.0", "audio-decode": "^2.2.3", "audio-lena": "^2.3.0", "compression": "^1.8.0", "countries-and-timezones": "^3.7.2", "dotenv": "latest", "express-rate-limit": "^8.0.1", "fastify": "latest", "fastify-rate-limit": "^5.8.0", "form-data": "^4.0.1", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.6", "ngrok": "^5.0.0-beta.2", "node-fetch": "^3.3.2", "node-wav": "^0.0.2", "pcm-convert": "^1.6.5", "speex-resampler": "^3.0.1", "twilio": "latest", "web-audio-api": "^0.2.2", "ws": "latest", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.31.0", "@fastify/type-provider-typebox": "^5.2.0", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.7", "@types/mime-types": "^2.1.4", "@types/node-wav": "^0.0.4", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "concurrently": "^9.2.0", "eslint": "^9.29.0", "jsdom": "^25.0.1", "nodemon": "^3.1.10", "sinon": "^21.0.0", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.8.3"}}