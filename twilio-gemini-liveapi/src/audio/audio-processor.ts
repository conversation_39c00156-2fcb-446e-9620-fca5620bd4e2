import { AudioConverter, convertWebmToPCM16 } from './audio-conversion';
import { AudioEnhancer } from './audio-enhancer';
import { AudioBufferValidator, AudioValidationResult, audioQualityMonitor } from './audio-quality';
import { AudioSettings, MockAudioBuffer } from './audio-types';
import * as fs from 'fs';
import * as path from 'path';

export class AudioProcessor {
    static audioEnhancer = new AudioEnhancer();
    static audioQualityMonitor = audioQualityMonitor;

    private converter: AudioConverter;
    private audioValidator: AudioBufferValidator;
    private audioSettings: AudioSettings;

    constructor() {
        this.converter = new AudioConverter();
        this.audioValidator = new AudioBufferValidator({
            maxBufferSize: 10 * 1024 * 1024,
            minBufferSize: 1,
            allowedFormats: ['ulaw', 'pcm16', 'pcm32', 'float32'],
            maxSilencePercentage: 95,
            maxClippingPercentage: 10,
            enableDetailedAnalysis: false // Disable detailed analysis for production performance
        });
        this.audioSettings = {
            enableDeEssing: false,
            enableNoiseReduction: false,
            enableCompression: false,
            enableAGC: false,
            compressionRatio: 2.0,
            noiseThreshold: 0.01,
            agcTargetLevel: 0.7
        };
    }

    convertUlawToPCM(audioBuffer: Buffer, skipEnhancement = false): Buffer {
        const pcm = this.converter.convertUlawToPCM(audioBuffer);
        // Skip enhancement in production for lower latency
        if (skipEnhancement || process.env.NODE_ENV === 'production') {
            return pcm;
        }
        const float = this.converter.pcmToFloat32Array(pcm);
        const enhanced = AudioProcessor.audioEnhancer.enhance(float, { noiseReduction: true });
        const out = Buffer.alloc(enhanced.length * 2);
        for (let i = 0; i < enhanced.length; i++) {
            const s = Math.max(-1, Math.min(1, enhanced[i]));
            out.writeInt16LE(Math.round(s * 32767), i * 2);
        }
        return out;
    }

    pcmToFloat32Array(pcmBuffer: Buffer): Float32Array {
        return this.converter.pcmToFloat32Array(pcmBuffer);
    }

    upsample8kTo16k(data: Float32Array): Float32Array {
        return this.converter.upsample8kTo16k(data);
    }

    downsample24kTo8k(data: Float32Array): Float32Array {
        return this.converter.downsample24kTo8k(data);
    }

    createGeminiAudioBlob(data: Float32Array): { data: string; mimeType: string } {
        return this.converter.createGeminiAudioBlob(data);
    }

    convertPCMToUlaw(audio: string | Buffer): string | Buffer {
        return this.converter.convertPCMToUlaw(audio);
    }

    pcmToUlaw(pcm: Buffer): Buffer {
        return this.converter.pcmToUlaw(pcm);
    }

    linearToUlaw(sample: number): number {
        return this.converter.linearToUlaw(sample);
    }

    static convertWebmToPCM16(webmData: Buffer, targetSampleRate = 16000): Buffer {
        return AudioConverter.convertWebmToPCM16(webmData, targetSampleRate);
    }

    static decodeAudioData(webmData: Buffer): MockAudioBuffer {
        return AudioConverter.decodeAudioData(webmData);
    }

    static convertToPCM16(audioBuffer: MockAudioBuffer, targetSampleRate: number): Buffer {
        return AudioConverter.convertToPCM16(audioBuffer, targetSampleRate);
    }

    async saveAudioDebug(samples: Float32Array, filename: string, sampleRate = 8000): Promise<void> {
        if (process.env.AUDIO_DEBUG !== 'true') {
            return;
        }
        
        try {
            const pcmBuffer = Buffer.alloc(samples.length * 2);
            for (let i = 0; i < samples.length; i++) {
                const s = Math.max(-1, Math.min(1, samples[i]));
                pcmBuffer.writeInt16LE(Math.round(s * 32767), i * 2);
            }
            const dir = path.join(process.cwd(), 'audio-debug');
            
            try {
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
            } catch (mkdirError) {
                console.error('Failed to create audio debug directory:', mkdirError);
                return; // Don't crash if we can't create debug directory
            }
            
            const filepath = path.join(dir, `${filename}_${Date.now()}.pcm`);
            
            try {
                // Use async write to avoid blocking the event loop
                fs.writeFile(filepath, pcmBuffer, (writeError) => {
                    if (writeError) {
                        console.error('Failed to write audio debug file:', writeError);
                        // Don't crash if we can't write debug file
                    }
                });
            } catch (writeError) {
                console.error('Failed to write audio debug file:', writeError);
                // Don't crash if we can't write debug file
            }
        } catch (error) {
            console.error('Error in saveAudioDebug:', error);
            // Don't let debug functionality crash the application
        }
    }

    updateAudioSettings(newSettings: Partial<AudioSettings>): void {
        this.audioSettings = { ...this.audioSettings, ...newSettings };
    }

    getAudioSettings(): AudioSettings {
        return { ...this.audioSettings };
    }

    getValidationStats(): Record<string, number> {
        return this.audioValidator.getValidationStats();
    }

    resetValidationStats(): void {
        this.audioValidator.resetStats();
    }

    validateAudioBuffer(buffer: Buffer, format: string = 'ulaw', sessionId?: string): AudioValidationResult {
        return this.audioValidator.validateAudioBuffer(buffer, format, sessionId);
    }

    getAudioHealthReport(): {
        validationStats: Record<string, number>;
        enhancerStats: any;
        qualityMetrics: any;
        lastUpdate: string;
    } {
        return {
            validationStats: this.getValidationStats(),
            enhancerStats: AudioProcessor.audioEnhancer.getProcessingStats(),
            qualityMetrics: AudioProcessor.audioQualityMonitor.getSummary(),
            lastUpdate: new Date().toISOString()
        };
    }
}

export { convertWebmToPCM16 } from './audio-conversion';
