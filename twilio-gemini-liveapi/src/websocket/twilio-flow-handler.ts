import { endSession, scheduleRecovery } from './session-utils';
import { websocketLogger } from '../utils/logger';
import { globalHeartbeatManager } from './heartbeat-manager';
import type {
    WebSocketConnection,
    FlowDependencies,
    ConnectionData,
    TwilioStartMessage,
    TwilioMediaMessage,
    InternalMessageType
} from '../types/websocket';
import { TWILIO_EVENT_MAP } from '../types/websocket';
import type { WebSocket } from 'ws';
import {
    extractTwilioStreamInfo,
    getValidSessionConfig,
    createTwilioConnectionData,
    logTwilioStreamInfo,
    logSessionConfiguration,
    startLifecycleManagement,
    startTwilioHeartbeat,
    sendSessionStartedMessage,
    handleGeminiSessionFailure,
    handleSessionStartError
} from './twilio-session-helpers';
import {
    SessionStartupOptimizer,
    FastConfigLoader,
    ConnectionQualityMonitor
} from './performance-optimizations';
import {
    validateTwilioMessage,
    formatValidationError
} from './message-schemas';

const startupOptimizer = new SessionStartupOptimizer({
    enableFastStart: true,
    skipNonEssentialChecks: false,
    preloadGeminiSession: false,
    parallelInitialization: true,
    timeoutMs: 8000
});

const configLoader = new FastConfigLoader();
const qualityMonitor = new ConnectionQualityMonitor();

// Twilio flow handler for both inbound and outbound calls
export function handleTwilioFlow(connection: WebSocketConnection, deps: FlowDependencies): void {
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        recoveryManager,
        transcriptionManager,
        flowType,
        getSessionConfig,
        isIncomingCall,
        config
    } = deps;

    const rawCallSid = connection.query?.CallSid;
    const callSid = Array.isArray(rawCallSid) ? rawCallSid[0] : (rawCallSid || `twilio-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`);
    websocketLogger.info(`Twilio ${flowType} call started`, { callSid, flowType, isIncomingCall });

    const ws = (connection.socket || connection) as WebSocket;

    // CRITICAL FIX: Don't use persistent session variables - always get fresh state from activeConnections
    // This prevents stale state issues between multiple calls
    websocketLogger.debug('Starting fresh Twilio flow handler', { callSid });

    // Store event listeners for cleanup
    const eventListeners = new Map<string, (...args: any[]) => void>();

    const messageHandler = async (message: Buffer | string) => {
        try {
            const rawData = JSON.parse(message.toString());

            // Validate message with Zod schema
            const validation = validateTwilioMessage(rawData);
            if (!validation.success) {
                websocketLogger.warn('❌ Invalid Twilio message format', {
                    callSid,
                    error: formatValidationError(validation.error!),
                    rawData
                });
                return;
            }

            const data = validation.data!;

            // Twilio uses 'event' field for message type. Map it to our
            // internal message name so downstream handlers see consistent types.
            const rawType = data.event as string;
            const messageType: InternalMessageType =
                (TWILIO_EVENT_MAP[rawType] as InternalMessageType) || (rawType as InternalMessageType);

            websocketLogger.debug(`✅ Twilio ${flowType} message validated`, {
                callSid,
                event: data.event,
                messageType
            });

            switch (messageType) {
                case 'connected': {
                    // CRITICAL FIX: Handle 'connected' event that Twilio sends for some calls
                    websocketLogger.debug('Twilio CONNECTED event received', { callSid, data });
                    websocketLogger.info('Twilio connected event received', { callSid });

                    // Update connection state to indicate Twilio WebSocket is ready
                    const connectionData = activeConnections.get(callSid);
                    if (connectionData) {
                        connectionData.twilioConnected = true;
                        connectionData.lastActivity = Date.now();
                        websocketLogger.debug('Twilio WebSocket connection confirmed', { callSid });

                        // If we have both Twilio connection and Gemini session, mark as fully ready
                        if (connectionData.geminiSession && connectionData.isSessionActive) {
                            // Mark as fully ready in session manager
                            (connectionData as any).fullyReady = true;
                            websocketLogger.info('Session fully ready - both Twilio and Gemini connected', { callSid });
                        }
                    } else {
                        websocketLogger.warn('Received connected event but no connection data found', { callSid });
                    }
                    break;
                }

                case 'start-session':
                    // Twilio sends 'start' event when stream begins
                    websocketLogger.debug('Twilio START event received', { callSid, data });
                    try {
                        await handleTwilioStartSession(callSid, data as TwilioStartMessage, deps, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                    } catch (error) {
                        websocketLogger.error(`Failed to handle Twilio start session for ${callSid}`, error instanceof Error ? error : new Error(String(error)));
                        // Send error message to client
                        ws.send(JSON.stringify({
                            type: 'session-error',
                            error: 'Failed to initialize session. Please try again.',
                            critical: true
                        }));
                        // Clean up connection
                        activeConnections.delete(callSid);
                    }
                    break;

                case 'audio': {
                    // Always get fresh session state from activeConnections
                    const connectionData = activeConnections.get(callSid);
                    const geminiSession = connectionData?.geminiSession;
                    const isSessionActive = connectionData?.isSessionActive;

                    await handleTwilioMedia(callSid, data as TwilioMediaMessage, geminiSession, isSessionActive || false, deps, activeConnections, lifecycleManager, recoveryManager);
                    break;
                }

                case 'mark':
                    websocketLogger.debug('Mark received', { callSid });
                    break;

                case 'end-session':
                    websocketLogger.info('Stop message received from Twilio', { callSid });
                    await handleTwilioEndSession(callSid, deps, activeConnections, lifecycleManager);
                    break;

                default:
                    websocketLogger.warn(`Unknown Twilio message event: ${messageType}`, { callSid });
                    websocketLogger.debug('Unknown message received', { callSid, data });
            }
        } catch (error) {
            if (error instanceof SyntaxError) {
                websocketLogger.error('JSON parsing error for message', { error: error instanceof Error ? error : new Error(String(error)), callSid, message: message.toString() });
                websocketLogger.error(`JSON parsing error for Twilio ${flowType} message`, error instanceof Error ? error : new Error(String(error)));
            } else {
                websocketLogger.error(`Error processing Twilio ${flowType} message`, error instanceof Error ? error : new Error(String(error)), callSid);
            }
        }
    };

    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    ws.on('message', messageHandler);

    const closeHandler = async (code: number, reason: Buffer) => {
        websocketLogger.info(`Twilio ${flowType} connection closed`, {
            callSid,
            code,
            reason: reason ? reason.toString() : 'No reason',
            closeCode: code
        });

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(callSid);
        qualityMonitor.stopMonitoring(callSid);
        startupOptimizer.cleanup(callSid);

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // CRITICAL FIX: Don't end session immediately on WebSocket close
            // Twilio WebSocket can close/reconnect during normal call flow
            // Only end session if this is a deliberate call termination

            // Check if this is a normal close (1000) or abnormal close
            // Code 1005 means "no status received" and is common for Twilio WebSocket connections
            // const isNormalClose = code === 1000 || code === 1005; // Currently unused
            const isCallStillActive = connectionData.isSessionActive && connectionData.geminiSession;

            // CRITICAL FIX: Don't end session on WebSocket close unless call is actually terminated
            // Twilio WebSocket can close while call is still active
            // Only end session if we received a 'stop' message or call status indicates completion
            if (!isCallStillActive || connectionData.callCompleted || connectionData.stopReceived) {
                // Session already inactive or call explicitly ended - safe to end
                websocketLogger.info(`Ending session due to inactive session or explicit call termination`, {
                    callSid,
                    code,
                    isCallStillActive,
                    callCompleted: connectionData.callCompleted,
                    stopReceived: connectionData.stopReceived
                });
                if (lifecycleManager) {
                    await lifecycleManager.endSession(callSid, connectionData, 'twilio_connection_closed');
                } else {
                    endSession(callSid, deps, 'twilio_connection_closed');
                }
            } else {
                // WebSocket closed but session still active - don't end session
                websocketLogger.warn(`WebSocket closed but session still active - keeping session alive`, {
                    callSid,
                    code,
                    isCallStillActive
                });
                connectionData.wsDisconnected = true;
                connectionData.lastDisconnectTime = Date.now();

                // Don't end session - let call status webhook handle actual call termination
            }
        }

        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler as any);
        }
        eventListeners.clear();

        // CRITICAL: Clean up connection data to prevent stale state
        websocketLogger.debug('Cleaning up connection data to prevent stale state', { callSid });
        activeConnections.delete(callSid);
    };

    const errorHandler = async (error: Error) => {
        websocketLogger.error(`Twilio ${flowType} error`, error instanceof Error ? error : new Error(String(error)));

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // Try recovery first if possible
            if (recoveryManager && contextManager.canRecover(callSid)) {
                websocketLogger.info('Twilio WebSocket error detected, attempting session recovery', { callSid });
                contextManager.markSessionInterrupted(callSid, 'twilio_websocket_error');
                scheduleRecovery(callSid, 'twilio_websocket_error', recoveryManager, activeConnections);
            } else {
                // Mark connection as errored but don't immediately end session
                // Let the call status webhook or explicit user action end the session
                websocketLogger.warn('WebSocket error but no recovery available - marking connection as errored', { callSid });
                connectionData.wsError = true;
                connectionData.lastErrorTime = Date.now();

                // Clean up Deepgram transcription
                if (connectionData.deepgramConnection) {
                    deps.transcriptionManager.closeTranscription(callSid);
                }

                // Only end session if this is a critical error and session is not active
                if (!connectionData.isSessionActive || !connectionData.geminiSession) {
                    qualityMonitor.stopMonitoring(callSid);
                    startupOptimizer.cleanup(callSid);
                    endSession(callSid, { ...deps, transcriptionManager: deps.transcriptionManager }, 'twilio_connection_error');
                }
            }
        }
    };

    // Register all event listeners
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}

async function handleTwilioStartSession(
    callSid: string,
    data: TwilioStartMessage,
    deps: FlowDependencies,
    ws: WebSocket,
    flowType: string,
    isIncomingCall: boolean,
    getSessionConfig: () => any,
    activeConnections: Map<string, ConnectionData>,
    healthMonitor: any,
    lifecycleManager: any,
    sessionManager: any
): Promise<void> {
    websocketLogger.info(`Starting Twilio ${flowType} session`, { callSid });
    const { config } = deps;

    startupOptimizer.startTracking(callSid);
    qualityMonitor.startMonitoring(callSid, ws);

    try {
        // Extract Twilio stream information
        const streamInfo = extractTwilioStreamInfo(data);

        // Get and validate session configuration using cached loader
        const configResult = await configLoader.loadConfig(
            callSid,
            () => getValidSessionConfig(callSid, getSessionConfig, deps, isIncomingCall),
            true
        ) as { config: any; isValid: boolean };
        startupOptimizer.markConfigLoaded(callSid);
        const sessionConfig = configResult.config;

        if (!sessionConfig || !sessionConfig.aiInstructions || sessionConfig.aiInstructions.trim().length === 0) {
            const errorMessage = `No AI instructions available for ${flowType} session. Cannot create Gemini session without proper system instructions.`;
            websocketLogger.error(errorMessage, {}, callSid);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: errorMessage,
                    code: 'MISSING_AI_INSTRUCTIONS'
                }));
            }
            qualityMonitor.stopMonitoring(callSid);
            startupOptimizer.cleanup(callSid);
            activeConnections.delete(callSid);
            return;
        }

        // Log stream information
        logTwilioStreamInfo(callSid, streamInfo, sessionConfig);

        // Create and store enhanced connection data
        const connectionData = createTwilioConnectionData(
            callSid, ws, streamInfo, sessionConfig, isIncomingCall, flowType
        );
        activeConnections.set(callSid, connectionData);

        // Track connection health
        healthMonitor.trackConnection(callSid, 'connected', {
            flowType,
            isTwilioCall: true,
            scriptId: sessionConfig.scriptId
        });

        // Create Gemini session with timeout handling
        websocketLogger.debug('Creating Gemini session', { callSid });
        const sessionCreationTimeout = 30000; // 30 second timeout

        let geminiSession;
        try {
            geminiSession = await Promise.race([
                sessionManager.createGeminiSession(callSid, sessionConfig, connectionData),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Gemini session creation timeout')), sessionCreationTimeout)
                )
            ]);
        } catch (error) {
            websocketLogger.error(`Failed to create Gemini session for ${callSid}`, error instanceof Error ? error : new Error(String(error)));
            await handleGeminiSessionFailure(callSid, flowType, ws, deps, endSession);
            return;
        }

        if (geminiSession) {
            connectionData.geminiSession = geminiSession;
            startupOptimizer.markGeminiSessionCreated(callSid);
            websocketLogger.debug('Gemini session created, waiting for activation', { callSid });

            // Session should be active immediately after creation - no polling needed
            if (!connectionData.isSessionActive) {
                const waitStart = Date.now();
                const maxWaitTime = 1000; // Reduced from 10 seconds to 1 second
                
                // Give a very brief moment for async session activation, but no polling delays
                while (!connectionData.isSessionActive && (Date.now() - waitStart) < maxWaitTime) {
                    // Yield to event loop once without delay
                    await new Promise(resolve => process.nextTick(resolve));
                }
                
                if (!connectionData.isSessionActive) {
                    websocketLogger.error(`Gemini session failed to activate within ${maxWaitTime}ms for ${callSid}`);
                    await handleGeminiSessionFailure(callSid, flowType, ws, deps, endSession);
                    return;
                }

                websocketLogger.info('Gemini session activated successfully', { callSid, activationTime: Date.now() - waitStart });
            }

            // Start lifecycle management
            startLifecycleManagement(callSid, lifecycleManager, connectionData, sessionConfig);

            // Start WebSocket heartbeat monitoring
            startTwilioHeartbeat(callSid, ws, globalHeartbeatManager, activeConnections, config);

            // Live API setup is now handled by SessionManager during session creation
            websocketLogger.info(`Live API setup handled by SessionManager for ${flowType}`, { callSid });

            // Log session configuration
            logSessionConfiguration(callSid, sessionConfig, flowType);

            // Send session started message
            sendSessionStartedMessage(ws, callSid, flowType, sessionConfig.scriptId);

            startupOptimizer.markSessionReady(callSid);

            websocketLogger.info(`Twilio ${flowType} session started successfully`, { callSid });
        } else {
            // Handle Gemini session creation failure
            await handleGeminiSessionFailure(callSid, flowType, ws, deps, endSession);
            return;
        }

    } catch (error) {
        handleSessionStartError(error, flowType, ws);
    }
}

async function handleTwilioMedia(
    callSid: string,
    data: TwilioMediaMessage,
    _geminiSession: any,
    _isSessionActive: boolean,
    deps: FlowDependencies,
    activeConnections: Map<string, ConnectionData>,
    lifecycleManager: any,
    recoveryManager: any
): Promise<void> {
    if (data.media && data.media.payload) {
        try {
            // Update activity for session persistence
            lifecycleManager.transitionState(callSid, 'active', 'media_received');

            // Get fresh connection data to check current session state
            const connectionData = activeConnections.get(callSid);

            // CRITICAL VALIDATION: Check connection state before processing audio
            if (!connectionData) {
                websocketLogger.error('No connection data found for media processing', {}, callSid);
                return;
            }

            if (!connectionData.geminiSession) {
                websocketLogger.error('No Gemini session for media processing', {}, callSid);
                return;
            }

            if (!connectionData.isSessionActive) {
                websocketLogger.error('Session not active for media processing', {}, callSid);
                return;
            }

            // Additional validation for session readiness
            if ((connectionData as any).geminiSessionError) {
                websocketLogger.error('Gemini session has error', { error: (connectionData as any).geminiSessionError }, callSid);
                return;
            }

            // Check if session is fully ready (both Twilio and Gemini connected)
            if (!(connectionData as any).fullyReady && connectionData.twilioConnected !== undefined) {
                websocketLogger.warn('Session not fully ready - processing audio anyway', { callSid, twilioConnected: connectionData.twilioConnected, geminiActive: connectionData.isSessionActive });
            }

            const currentIsSessionActive = connectionData.isSessionActive;
            const currentGeminiSession = connectionData.geminiSession;

            // Convert Twilio μ-law audio to PCM
            const ulawAudio = Buffer.from(data.media.payload, 'base64');

            // Final validation before sending audio
            if (deps.sessionManager && currentGeminiSession && currentIsSessionActive) {
                websocketLogger.debug('Sending audio to Gemini - session validated and active', { callSid });
                await deps.sessionManager.sendAudioToGemini(callSid, currentGeminiSession, ulawAudio);

                // Update last activity timestamp
                connectionData.lastActivity = Date.now();
            } else {
                websocketLogger.warn('Skipping audio send - validation failed', { 
                    callSid,
                    hasSessionManager: !!deps.sessionManager,
                    hasGeminiSession: !!currentGeminiSession,
                    isSessionActive: currentIsSessionActive,
                    hasConnectionData: !!connectionData
                });
            }

        } catch (error) {
            websocketLogger.error(`Error processing Twilio media for ${callSid}`, error instanceof Error ? error : new Error(String(error)));

            // Mark connection as having media processing error
            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                connectionData.lastMediaError = Date.now();
                connectionData.mediaErrorCount = (connectionData.mediaErrorCount || 0) + 1;
            }

            // Attempt recovery if needed
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(callSid, activeConnections)) {
                websocketLogger.info('Attempting recovery due to media processing error', { callSid });
                await recoveryManager.recoverSession(callSid, 'media_processing_error', activeConnections);
            }
        }
    } else {
        websocketLogger.warn('Received media message without payload', { callSid });
    }
}

async function handleTwilioEndSession(
    callSid: string, 
    deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any
): Promise<void> {
    websocketLogger.info('Ending Twilio session - stop received', { callSid });

    const connectionData = activeConnections.get(callSid);
    if (connectionData) {
        // Mark that stop was received to prevent premature session termination on WebSocket close
        connectionData.stopReceived = true;

        if (lifecycleManager) {
            await lifecycleManager.endSession(callSid, connectionData, 'twilio_stop_received');
        } else {
            endSession(callSid, deps, 'twilio_stop_received');
        }
    } else {
        endSession(callSid, deps, 'twilio_stop_received');
    }

    qualityMonitor.stopMonitoring(callSid);
    startupOptimizer.cleanup(callSid);
}