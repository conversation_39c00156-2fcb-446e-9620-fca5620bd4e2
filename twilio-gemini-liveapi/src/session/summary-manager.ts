import { logger } from '../utils/logger.js';

export class SessionSummaryManager {
    private summaries: Map<string, any> = new Map();

    constructor() {
        logger.info('📊 Summary manager initialized');
    }

    createSummary(sessionId: string, data: any): void {
        this.summaries.set(sessionId, {
            ...data,
            createdAt: Date.now(),
            sessionId
        });
        logger.info(`📊 Summary created for session: ${sessionId}`);
    }

    getSummary(sessionId: string): any {
        return this.summaries.get(sessionId);
    }

    updateSummary(sessionId: string, updates: any): void {
        const existing = this.summaries.get(sessionId) || {};
        this.summaries.set(sessionId, {
            ...existing,
            ...updates,
            updatedAt: Date.now()
        });
        logger.info(`📊 Summary updated for session: ${sessionId}`);
    }

    deleteSummary(sessionId: string): boolean {
        const deleted = this.summaries.delete(sessionId);
        if (deleted) {
            logger.info(`📊 Summary deleted for session: ${sessionId}`);
        }
        return deleted;
    }

    getAllSummaries(): Map<string, any> {
        return new Map(this.summaries);
    }

    clearSummaries(): void {
        this.summaries.clear();
        logger.info('📊 All summaries cleared');
    }
}