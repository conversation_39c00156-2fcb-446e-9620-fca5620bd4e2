import { logger } from '../utils/logger.js';

export class ConnectionHealthMonitor {
    private isHealthy: boolean = true;
    private lastCheckTime: number = Date.now();

    constructor() {
        logger.info('🔍 Health monitor initialized');
    }

    checkHealth(): boolean {
        this.lastCheckTime = Date.now();
        return this.isHealthy;
    }

    setHealthy(healthy: boolean): void {
        this.isHealthy = healthy;
        logger.info(`🔍 Health status changed: ${healthy ? 'healthy' : 'unhealthy'}`);
    }

    getLastCheckTime(): number {
        return this.lastCheckTime;
    }

    getHealthStatus(): { healthy: boolean; lastCheck: number } {
        return {
            healthy: this.isHealthy,
            lastCheck: this.lastCheckTime
        };
    }
}