// Session configuration routes for the Twilio Gemini service
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { SecurityUtils } from '../middleware/security-utils';
import { AudioProcessor } from '../audio/audio-processor';

import {
    Dependencies,
    ConfigureCallBody,
    SetVoiceBody,
    SetModelBody,
    AudioSettingsBody
} from '../types/api-types';
import { validateBody } from '../middleware/validation';
import { sessionConfigSchema, updateSystemMessageSchema, saveConfigSchema } from '../validation/schemas';

// Type definitions for session configuration routes
interface UpdateSessionConfigBody {
    voice?: string;
    model?: string;
    aiInstructions?: string;
    task?: string; // Legacy field name for backward compatibility
    targetName?: string;
    targetPhoneNumber?: string;
    outputLanguage?: string; // Add support for output language
}

interface UpdateSystemMessageBody {
    systemMessage?: string;
    aiInstructions?: string;
}

interface SaveConfigBody {
    voice?: string;
    model?: string;
    aiInstructions?: string;
    targetName?: string;
    targetPhoneNumber?: string;
}

export function registerSessionConfigRoutes(fastify: FastifyInstance, deps: Dependencies): void {
    console.log('🔧 Registering session configuration routes...');
    
    const {
        sessionManager,
        voiceManager,
        modelManager,
        GEMINI_DEFAULT_VOICE,
        GEMINI_DEFAULT_MODEL
    } = deps;

    // Configure next outbound call
    fastify.post<{ Body: ConfigureCallBody }>('/configure-call', async (request, _reply) => {
        try {
            // Validate request body exists
            if (!request.body) {
                return {
                    success: false,
                    error: 'Request body is required'
                };
            }
            
            const { aiInstructions, voice, model, targetName, targetPhoneNumber } = request.body;
            
            // Get current config from webhook module
            const currentConfig = await (fastify as any).getNextCallConfig() || {
                aiInstructions: '',
                voice: GEMINI_DEFAULT_VOICE,
                model: GEMINI_DEFAULT_MODEL,
                targetName: null,
                targetPhoneNumber: null,
                phoneNumber: '',
                mode: 'outbound'
            };
            
            const updatedConfig = {
                aiInstructions: aiInstructions || currentConfig.aiInstructions,
                voice: voice || GEMINI_DEFAULT_VOICE,
                model: model || GEMINI_DEFAULT_MODEL,
                targetName: targetName || null,
                targetPhoneNumber: targetPhoneNumber || null,
                phoneNumber: targetPhoneNumber || currentConfig.phoneNumber,
                mode: 'outbound'
            };

            // Update config in webhook module
            (fastify as any).setNextCallConfig(updatedConfig);

            console.log(`⚙️ Call configured for: ${updatedConfig.targetName || 'Unknown'}`);
            
            return { 
                success: true, 
                config: updatedConfig 
            };
        } catch (error) {
            console.error('❌ Error configuring call:', error);
            return { 
                success: false, 
                error: (error as Error).message 
            };
        }
    });

    // Get current call configuration
    fastify.get('/get-call-config', async (_request: FastifyRequest, _reply: FastifyReply) => {
        const currentConfig = await (fastify as any).getNextCallConfig();
        return {
            success: true,
            config: currentConfig
        };
    });

    // Set default voice for next calls
    fastify.post<{ Body: SetVoiceBody }>('/set-voice', async (request, _reply) => {
        try {
            const { voice } = request.body;

            if (!voice) {
                return {
                    success: false,
                    error: 'Voice parameter is required'
                };
            }

            const validation = voiceManager.validateVoice(voice);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: validation.error,
                    suggestion: validation.suggestion,
                    availableVoices: validation.availableVoices,
                    voiceMapping: validation.voiceMapping
                };
            }

            const validVoice = validation.voice!;
            // Update config in webhook module
            const currentConfig = await (fastify as any).getNextCallConfig() || {};
            (fastify as any).setNextCallConfig({ ...currentConfig, voice: validVoice });

            const voiceInfo = voiceManager.getVoiceCharacteristics(validVoice);
            return {
                success: true,
                voice: validVoice,
                voiceInfo,
                mapped: validation.mapped || false,
                originalVoice: validation.originalVoice,
                message: `Voice set to: ${validVoice} (${voiceInfo?.characteristics || 'unknown'})`
            };
        } catch (error) {
            console.error('❌ Error setting voice:', error);
            return {
                success: false,
                error: 'Internal server error',
                message: (error as Error).message
            };
        }
    });

    // Set default model for next calls
    fastify.post<{ Body: SetModelBody }>('/set-model', async (request, _reply) => {
        try {
            const { model } = request.body;

            if (!model) {
                return {
                    success: false,
                    error: 'Model parameter is required'
                };
            }

            const validation = modelManager.validateModel(model);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: validation.error,
                    suggestion: validation.suggestion,
                    availableModels: validation.availableModels
                };
            }

            const validModel = validation.model!;
            // Update config in webhook module
            const currentConfig = await (fastify as any).getNextCallConfig() || {};
            (fastify as any).setNextCallConfig({ ...currentConfig, model: validModel });

            const modelInfo = modelManager.getModelInfo(validModel);
            return {
                success: true,
                model: validModel,
                modelInfo,
                message: `Model set to: ${validModel} (${modelInfo?.name || 'unknown'})`
            };
        } catch (error) {
            console.error('❌ Error setting model:', error);
            return {
                success: false,
                error: 'Internal server error',
                message: (error as Error).message
            };
        }
    });

    // Get/Set audio settings (legacy endpoint)
    fastify.get('/audio-settings', async (_request: FastifyRequest, _reply: FastifyReply) => {
        // Validate sessionManager exists
        if (!sessionManager) {
            return {
                success: false,
                error: 'Session manager not available'
            };
        }
        
        const audioSettings = sessionManager.getAudioSettings();
        return {
            success: true,
            settings: audioSettings
        };
    });

    fastify.post<{ Body: AudioSettingsBody }>('/audio-settings', async (request, _reply) => {
        try {
            // Validate request body and sessionManager
            if (!request.body) {
                return {
                    success: false,
                    error: 'Request body is required'
                };
            }
            
            if (!sessionManager) {
                return {
                    success: false,
                    error: 'Session manager not available'
                };
            }
            
            const newSettings = request.body;
            sessionManager.setAudioSettings(newSettings);
            
            return {
                success: true,
                settings: sessionManager.getAudioSettings(),
                message: 'Audio settings updated'
            };
        } catch (error) {
            return {
                success: false,
                error: (error as Error).message
            };
        }
    });

    // Audio settings API endpoints
    fastify.post<{ Body: AudioSettingsBody }>('/api/audio-settings', async (request, reply) => {
        try {
            const validatedSettings = SecurityUtils.validateAudioSettings(request.body);

            if (!validatedSettings) {
                reply.status(400);
                return { success: false, error: 'Invalid audio settings' };
            }

            // Update settings in AudioProcessor
            const audioProcessor = new AudioProcessor();
            audioProcessor.updateAudioSettings(validatedSettings);

            return {
                success: true,
                settings: audioProcessor.getAudioSettings(),
                message: 'Audio settings updated successfully',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error updating audio settings:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.get('/api/audio-settings', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const audioProcessor = new AudioProcessor();
            return {
                success: true,
                settings: audioProcessor.getAudioSettings(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting audio settings:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Update session configuration
    fastify.post<{ Body: UpdateSessionConfigBody }>('/update-session-config', {
        preHandler: validateBody(sessionConfigSchema)
    }, async (request, reply) => {
        try {
            const { voice, model, aiInstructions, task, targetName, targetPhoneNumber, outputLanguage } = request.body;
            
            // 🔍 DEBUG: Log successful validation
            console.log(`🔍 [SESSION-CONFIG] Validation passed for model: "${model}"`);
            if (model) {
                const modelValidation = deps.modelManager.validateModel(model);
                console.log(`🔍 [SESSION-CONFIG] ModelManager validation:`, {
                    model,
                    isValid: modelValidation.isValid,
                    error: modelValidation.error,
                    suggestion: modelValidation.suggestion
                });
            }

            // Get current config from webhook module
            const currentConfig = await (fastify as any).getNextCallConfig() || {
                aiInstructions: '',
                voice: GEMINI_DEFAULT_VOICE,
                model: GEMINI_DEFAULT_MODEL,
                targetName: null,
                targetPhoneNumber: null,
                phoneNumber: '',
                mode: 'outbound'
            };

            // Update next call configuration
            if (voice) {
                const validation = voiceManager.validateVoice(voice);
                if (validation.isValid) {
                    currentConfig.voice = validation.voice!;
                }
            }

            if (model) {
                const validation = modelManager.validateModel(model);
                if (validation.isValid) {
                    currentConfig.model = validation.model!;
                }
            }

            // Handle both aiInstructions and task field names for backward compatibility
            if (aiInstructions || task) {
                currentConfig.aiInstructions = aiInstructions || task;
            }

            if (outputLanguage) {
                currentConfig.outputLanguage = outputLanguage;
            }

            if (targetName) {
                currentConfig.targetName = targetName;
            }

            if (targetPhoneNumber) {
                currentConfig.targetPhoneNumber = targetPhoneNumber;
            }

            // Update config in webhook module
            (fastify as any).setNextCallConfig(currentConfig);

            return {
                status: 'success',
                success: true,
                config: currentConfig,
                message: 'Session configuration updated',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error updating session config:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Update system message endpoint
    fastify.post<{ Body: UpdateSystemMessageBody }>('/update-system-message', {
        preHandler: validateBody(updateSystemMessageSchema)
    }, async (request, _reply) => {
        try {
            const { systemMessage, aiInstructions } = request.body || {};
            
            // Get current config
            const currentConfig = await (fastify as any).getNextCallConfig() || {
                aiInstructions: '',
                voice: GEMINI_DEFAULT_VOICE,
                model: GEMINI_DEFAULT_MODEL
            };
            
            // Update AI instructions
            if (systemMessage || aiInstructions) {
                currentConfig.aiInstructions = systemMessage || aiInstructions;
            }
            
            // Save updated config
            await (fastify as any).setNextCallConfig(currentConfig);
            
            console.log('✅ System message updated successfully');
            return {
                success: true,
                config: currentConfig
            };
        } catch (error) {
            console.error('❌ Error updating system message:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update system message'
            };
        }
    });

    // Save config endpoint
    fastify.post<{ Body: SaveConfigBody }>('/save-config', async (request, _reply) => {
        try {
            const { voice, model, aiInstructions, targetName, targetPhoneNumber } = request.body || {};
            
            // Get current config
            const currentConfig = await (fastify as any).getNextCallConfig() || {
                aiInstructions: '',
                voice: GEMINI_DEFAULT_VOICE,
                model: GEMINI_DEFAULT_MODEL
            };
            
            // Update fields if provided
            if (voice) {
                const validVoice = voiceManager.validateVoice(voice);
                currentConfig.voice = validVoice;
            }
            
            if (model) {
                const validModel = modelManager.validateModel(model);
                currentConfig.model = validModel;
            }
            
            if (aiInstructions !== undefined) {
                currentConfig.aiInstructions = aiInstructions;
            }
            
            if (targetName !== undefined) {
                currentConfig.targetName = targetName;
            }
            
            if (targetPhoneNumber !== undefined) {
                // Validate phone number if provided
                const sanitizedNumber = targetPhoneNumber ? SecurityUtils.validatePhoneNumber(targetPhoneNumber) : null;
                if (targetPhoneNumber && !sanitizedNumber) {
                    return {
                        success: false,
                        error: 'Invalid phone number format'
                    };
                }
                currentConfig.targetPhoneNumber = sanitizedNumber || targetPhoneNumber;
            }
            
            // Save updated config
            await (fastify as any).setNextCallConfig(currentConfig);
            
            console.log('✅ Configuration saved successfully');
            return {
                success: true,
                config: currentConfig
            };
        } catch (error) {
            console.error('❌ Error saving configuration:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to save configuration'
            };
        }
    });

    console.log('✅ Session configuration routes registered successfully!');
}