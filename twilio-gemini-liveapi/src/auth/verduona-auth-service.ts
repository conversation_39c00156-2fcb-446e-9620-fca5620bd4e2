import * as crypto from 'crypto';
import { apiLogger } from '../utils/logger';

interface TokenPayload {
  sub: string;
  name: string;
  email?: string;
  roles?: string[];
  exp: number;
  iat: number;
  iss: string;
}

import type { ValidationResult } from '../types/shared-types';

interface TokenValidationResult extends ValidationResult {
  payload?: TokenPayload;
}

export class VerduonaAuthService {
  private readonly VERDUONA_AUTH_URL: string;
  private readonly JWT_SECRET: string;
  private readonly ISSUER = 'verduona.com';

  constructor() {
    this.VERDUONA_AUTH_URL = process.env.VERDUONA_AUTH_URL || 'https://verduona.com/api/auth';
    this.JWT_SECRET = process.env.JWT_SECRET || process.env.VERDUONA_JWT_SECRET || '';
    
    if (!this.JWT_SECRET) {
      apiLogger.warn('⚠️ JWT_SECRET not configured - token validation will use basic checks only');
    }
  }

  /**
   * Validate JWT token with proper signature and expiration checks
   */
  async validateToken(token: string): Promise<TokenValidationResult> {
    try {
      // Basic format validation
      const parts = token.split('.');
      if (parts.length !== 3) {
        return { isValid: false, error: 'Invalid JWT format' };
      }

      // Decode payload
      const payload = this.decodePayload(token);
      if (!payload) {
        return { isValid: false, error: 'Invalid token payload' };
      }

      // Check expiration
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < now) {
        apiLogger.warn('🔐 Token expired', { 
          expiredAt: new Date(payload.exp * 1000).toISOString(),
          now: new Date(now * 1000).toISOString()
        });
        return { isValid: false, error: 'Token expired' };
      }

      // Check issuer
      if (payload.iss && payload.iss !== this.ISSUER) {
        apiLogger.warn('🔐 Invalid token issuer', { 
          expected: this.ISSUER,
          received: payload.iss
        });
        return { isValid: false, error: 'Invalid token issuer' };
      }

      // Verify signature if JWT_SECRET is available
      if (this.JWT_SECRET) {
        const isSignatureValid = this.verifySignature(token);
        if (!isSignatureValid) {
          apiLogger.warn('🔐 Invalid token signature');
          return { isValid: false, error: 'Invalid token signature' };
        }
      }

      // Optional: Validate with remote auth service
      const isRemoteValid = await this.validateWithAuthService(token);
      if (!isRemoteValid) {
        return { isValid: false, error: 'Token not valid with auth service' };
      }

      apiLogger.info('🔐 Token validation successful', {
        user: payload.sub,
        email: payload.email,
        roles: payload.roles?.length || 0
      });

      return { isValid: true, payload };

    } catch (error) {
      apiLogger.error('🔐 Token validation error:', error as Error);
      return { isValid: false, error: 'Token validation failed' };
    }
  }

  /**
   * Decode JWT payload without verification (for inspection)
   */
  private decodePayload(token: string): TokenPayload | null {
    try {
      const parts = token.split('.');
      const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
      return payload as TokenPayload;
    } catch (error) {
      apiLogger.error('🔐 Failed to decode token payload:', error as Error);
      return null;
    }
  }

  /**
   * Verify JWT signature using HMAC SHA256
   */
  private verifySignature(token: string): boolean {
    try {
      if (!this.JWT_SECRET) {
        return true; // Skip if no secret configured
      }

      const parts = token.split('.');
      const header = parts[0];
      const payload = parts[1];
      const signature = parts[2];

      // Create expected signature
      const data = `${header}.${payload}`;
      const expectedSignature = crypto
        .createHmac('sha256', this.JWT_SECRET)
        .update(data)
        .digest('base64url');

      return signature === expectedSignature;
    } catch (error) {
      apiLogger.error('🔐 Signature verification failed:', error as Error);
      return false;
    }
  }

  /**
   * Validate token with remote Verduona auth service
   */
  private async validateWithAuthService(token: string): Promise<boolean> {
    try {
      // For now, skip remote validation and rely on JWT signature verification
      // In production, you would implement actual HTTP call to main auth service
      apiLogger.info('🔐 Remote auth service validation skipped (using JWT verification)', {
        tokenLength: token.length
      });
      return true;
    } catch (error) {
      apiLogger.error('❌ Error validating with auth service:', error as Error);
      return false;
    }
  }

  /**
   * Extract user information from validated token
   */
  getUserInfo(payload: TokenPayload) {
    return {
      id: payload.sub,
      name: payload.name,
      email: payload.email,
      roles: payload.roles || [],
      isAdmin: payload.roles?.includes('admin') || false
    };
  }
}