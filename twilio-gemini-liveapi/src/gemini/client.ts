import pkg from '@google/genai';
const { GoogleGenAI, Modality } = pkg;
import { geminiLogger as logger } from '../utils/logger';
import { config, getConfigValue } from '../config/config';

// Type definitions
import type { VoiceCharacteristics } from '../types/shared-types';

interface ModelInfo {
    name: string;
    description: string;
    status: string;
    supportsAudio: boolean;
    quality: string;
    region: string;
}

// Available Gemini voices with detailed characteristics (from Google AI Studio)
export const AVAILABLE_GEMINI_VOICES: Record<string, VoiceCharacteristics> = {
    'Aoede': {
        name: '<PERSON><PERSON><PERSON>',
        gender: 'Female',
        characteristics: 'Bright, clear narrator with neutral accent',
        description: 'Professional female voice ideal for presentations, tutorials, and informational content. Natural-sounding with excellent clarity.',
        pitch: 'Mid-range',
        timbre: 'Bright and clear',
        persona: 'Professional narrator',
        useCase: 'Business calls, presentations, tutorials'
    },
    'Puck': {
        name: '<PERSON>uck',
        gender: 'Male',
        characteristics: 'Lively, energetic tenor with higher pitch',
        description: 'Upbeat male voice that conveys enthusiasm and energy. Great for engaging conversations and customer interactions.',
        pitch: 'Higher tenor',
        timbre: 'Lively and energetic',
        persona: 'Enthusiastic and friendly',
        useCase: 'Sales calls, customer service, marketing'
    },
    'Charon': {
        name: 'Charon',
        gender: 'Male',
        characteristics: 'Deep, authoritative baritone with warmth',
        description: 'Commanding male voice that projects authority and trustworthiness. Ideal for serious business communications.',
        pitch: 'Deep baritone',
        timbre: 'Warm and authoritative',
        persona: 'Executive and trustworthy',
        useCase: 'Leadership calls, formal business, finance'
    },
    'Kore': {
        name: 'Kore',
        gender: 'Female',
        characteristics: 'Soft, empathetic alto with caring tone',
        description: 'Gentle female voice that conveys empathy and understanding. Perfect for customer support and healthcare interactions.',
        pitch: 'Soft alto',
        timbre: 'Warm and caring',
        persona: 'Empathetic and supportive',
        useCase: 'Customer support, healthcare, counseling'
    },
    'Fenrir': {
        name: 'Fenrir',
        gender: 'Male',
        characteristics: 'Confident, assertive mid-range voice',
        description: 'Strong male voice that projects confidence and reliability. Excellent for professional business communications.',
        pitch: 'Mid-range',
        timbre: 'Assertive and confident',
        persona: 'Professional and decisive',
        useCase: 'Business development, negotiations, consulting'
    },
    'Leda': {
        name: 'Leda',
        gender: 'Female',
        characteristics: 'Clear, professional announcer with refined accent',
        description: 'Polished female voice with broadcast-quality clarity. Ideal for formal communications and announcements.',
        pitch: 'Clear and articulate',
        timbre: 'Professional and refined',
        persona: 'Sophisticated announcer',
        useCase: 'Corporate communications, announcements, formal calls'
    },
    'Orus': {
        name: 'Orus',
        gender: 'Male',
        characteristics: 'Relaxed, casual tenor with breathy quality',
        description: 'Laid-back male voice that creates a comfortable, approachable atmosphere. Great for informal conversations.',
        pitch: 'Relaxed tenor',
        timbre: 'Breathy and casual',
        persona: 'Friendly and approachable',
        useCase: 'Casual conversations, support calls, friendly outreach'
    },
    'Zephyr': {
        name: 'Zephyr',
        gender: 'Female',
        characteristics: 'Light, youthful soprano with airy quality',
        description: 'Fresh female voice that sounds young and optimistic. Perfect for modern, tech-savvy interactions.',
        pitch: 'Light soprano',
        timbre: 'Airy and youthful',
        persona: 'Modern and optimistic',
        useCase: 'Tech support, modern brands, youth-oriented calls'
    }
};

// Voice mapping for different languages/accents and compatibility
export const VOICE_MAPPING: Record<string, string> = {
    // OpenAI voice mappings (for compatibility)
    'shimmer': 'Orus',
    'alloy': 'Puck',
    'echo': 'Charon',
    'fable': 'Kore',
    'onyx': 'Fenrir',
    'nova': 'Aoede',
    // Gender-based mappings
    'female': 'Kore',
    'male': 'Orus',
    'professional': 'Leda',
    'youthful': 'Zephyr',
    'authoritative': 'Charon',
    'energetic': 'Puck'
};

// Available Gemini models (configurable)
export const AVAILABLE_GEMINI_MODELS: Record<string, ModelInfo> = {
    'gemini-2.5-flash-preview-native-audio-dialog': {
        name: 'Gemini 2.5 Flash - Native Audio Dialog',
        description: 'Recommended default for voice apps. Outputs text and 24 kHz speech in 30 HD voices across 24 languages',
        status: 'Private preview',
        supportsAudio: true,
        quality: '★★★',
        region: 'global'
    },
    'gemini-2.0-flash-live-001': {
        name: 'Gemini 2.0 Flash Live (001)',
        description: 'GA/billing-enabled half-cascade audio. Native audio in, server-side TTS out',
        status: 'Public preview (billing on)',
        supportsAudio: true,
        quality: '★★',
        region: 'global, us-central1, EU4'
    }
};

// Function to map voice names and validate
export function getValidGeminiVoice(requestedVoice?: string | null, defaultVoice?: string | null): string {
    if (!defaultVoice) {
        defaultVoice = getConfigValue('ai.gemini.defaultVoice', 'Kore') as string;
    }

    if (!requestedVoice) {
        logger.info(`🎤 No voice specified, using default: ${defaultVoice} (${AVAILABLE_GEMINI_VOICES[defaultVoice]?.characteristics || 'unknown'})`);
        return defaultVoice;
    }

    // Check if it's already a valid Gemini voice
    if (AVAILABLE_GEMINI_VOICES[requestedVoice]) {
        const voiceInfo = AVAILABLE_GEMINI_VOICES[requestedVoice];
        logger.info(`🎤 Using requested voice: ${requestedVoice} (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
        return requestedVoice;
    }

    // Check if it's an OpenAI voice or other mapping that needs conversion
    if (VOICE_MAPPING[requestedVoice]) {
        const mappedVoice = VOICE_MAPPING[requestedVoice];
        const voiceInfo = AVAILABLE_GEMINI_VOICES[mappedVoice];
        logger.info(`🎤 Mapped voice '${requestedVoice}' → '${mappedVoice}' (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
        return mappedVoice;
    }

    // Default fallback
    const defaultInfo = AVAILABLE_GEMINI_VOICES[defaultVoice];
    logger.warn(`⚠️ Unknown voice '${requestedVoice}', using default: ${defaultVoice} (${defaultInfo?.characteristics || 'unknown'})`);
    return defaultVoice;
}

// Function to validate and get Gemini model
export function getValidGeminiModel(requestedModel?: string | null, defaultModel?: string | null): string {
    if (!defaultModel) {
        defaultModel = getConfigValue('ai.gemini.defaultModel', 'gemini-2.5-flash-preview-native-audio-dialog') as string;
    }

    if (!requestedModel) {
        logger.info(`🤖 No model specified, using default: ${defaultModel}`);
        return defaultModel;
    }

    // Check if it's a valid Gemini model
    if (AVAILABLE_GEMINI_MODELS[requestedModel]) {
        logger.info(`🤖 Using requested model: ${requestedModel}`);
        return requestedModel;
    }

    // Default fallback
    logger.warn(`⚠️ Unknown model '${requestedModel}', using default: ${defaultModel}`);
    return defaultModel;
}

// Initialize Gemini client
export function initializeGeminiClient(apiKey?: string): any | null {
    if (!apiKey) {
        logger.error('❌ GEMINI_API_KEY is required');
        return null;
    }

    try {
        // Pass API key as object (like in working tests)
        const client = new GoogleGenAI({ apiKey: apiKey });
        logger.info('🤖 Gemini client initialized successfully');
        return client;
    } catch (error) {
        logger.error('❌ Error initializing Gemini client', error instanceof Error ? error : new Error(String(error)));
        return null;
    }
}

export { Modality };