// Shared type definitions used across multiple modules
// This file consolidates duplicate interfaces to ensure consistency

import type { FastifyRequest } from 'fastify';

// Voice characteristics for Gemini voices
export interface VoiceCharacteristics {
    name: string;
    gender: 'Male' | 'Female';
    characteristics: string;
    pitch: string;
    timbre: string;
    persona: string;
    description?: string;
    useCase?: string;
}

// Generic validation result interface
export interface ValidationResult {
    isValid?: boolean;
    valid?: boolean;  // Alternative property name for backward compatibility
    payload?: any;
    error?: string;
    disqualify?: boolean;
}

// Auth request interface for middleware
export interface AuthRequest extends FastifyRequest {
    auth?: {
        token: string;
        authenticated: boolean;
    };
    user?: {
        id: string;
        email: string;
        role: string;
        isAuthenticated: boolean;
    } | null;
}

// Script type standardization - using 'incoming' as the standard
export type ScriptType = 'incoming' | 'outbound';

// Call mode type for API operations
export type CallMode = 'outbound' | 'inbound' | 'outbound-testing' | 'inbound-testing';

// Twilio direction type
export type TwilioDirection = 'inbound' | 'outbound';
// Import Zod for schema-derived types
import { z } from 'zod';

// Zod schemas for consistent validation
export const VoiceSchema = z.enum([
    'Aoede', 'Charon', 'Fenrir', 'Kore', 'Puck', 'Leda', 'Orus', 'Zephyr'
]);

export const ModelSchema = z.enum([
    'gemini-2.0-flash-exp',
    'gemini-1.5-flash', 
    'gemini-1.5-pro',
    'gemini-2.5-flash-preview-native-audio-dialog',
    'gemini-2.0-flash-live-001',
    'gemini-2.0-flash',
    'gemini-2.0-flash-live-preview-04-09'
]);

export const ScriptTypeSchema = z.enum(['incoming', 'outbound']);

// Derive TypeScript types from Zod schemas
export type GeminiVoice = z.infer<typeof VoiceSchema>;
export type GeminiModel = z.infer<typeof ModelSchema>;