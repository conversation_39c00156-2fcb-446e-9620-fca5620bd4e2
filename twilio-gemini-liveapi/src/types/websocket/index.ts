// Unified WebSocket message types
// These types are shared across the backend so that handlers
// can operate on consistent structures regardless of the source.

import type { WebSocket } from 'ws';
import type { ConnectionData, GeminiClient, GeminiSession, AppConfig } from '../global';
import type { SessionManager } from '../../session/session-manager';
import type { ContextManager } from '../../session/context-manager';
import type { SessionSummaryManager } from '../../session/summary-manager';
import type { ConnectionHealthMonitor } from '../../session/health-monitor';
import type { LifecycleManager } from '../../session/lifecycle-manager';
import type { RecoveryManager } from '../../session/recovery-manager';
import type { TranscriptionManager } from '../../audio/transcription-manager';
import type { ScriptManager } from '../../scripts/script-manager';
import type { VoiceManager } from '../../gemini/voice-manager';
import type { ModelManager } from '../../gemini/model-manager';

// Import shared types
import type { ScriptType } from '../shared-types';

// Basic WebSocket connection wrapper used by handlers
export interface WebSocketConnection {
    socket?: WebSocket;
    query?: Record<string, string | string[] | undefined>;
}

export { ConnectionData };

// Twilio helper interface
export interface TwilioHelper {
    endCallWithMessage(callSid: string, message: string): Promise<void>;
}

// Import centralized types
import type { GeminiVoice, GeminiModel } from '../shared-types';

// Re-export for backward compatibility
export type { GeminiVoice, GeminiModel };

// Extended connection data with all additional properties used across the codebase
export interface ExtendedConnectionData extends ConnectionData {
    // Session-related properties (from session-manager.ts)
    sessionConfig?: SessionConfig;
    sessionReady?: boolean;
    sessionInitialized?: number;
    geminiSessionError?: string;
    sessionActivatedAt?: number;
    fullyReady?: boolean;
    pendingScript?: string;
    isTwilioCall?: boolean;
    streamSid?: string;
    
    // WebSocket properties (from lifecycle-manager.ts)
    twilioWs?: WebSocket;
    localWs?: WebSocket;
    
    // Summary-related properties (from summary-manager.ts)
    summaryFlowType?: string;
    summaryTimeoutId?: NodeJS.Timeout;
}

// Session configuration returned by the configuration loaders
export interface SessionConfig {
    aiInstructions: string;
    voice: GeminiVoice;
    model: GeminiModel;
    targetName?: string | null;
    targetPhoneNumber?: string | null;
    scriptType: ScriptType;
    scriptId: string;
    isIncomingCall?: boolean;
    isTestMode?: boolean;
    campaignId?: string | number | null;
    sessionType?: string;
    originalAIInstructions?: string;
}

// Dependencies needed by the various WebSocket flows
export interface WebSocketDependencies {
    sessionManager: SessionManager;
    contextManager: ContextManager;
    activeConnections: Map<string, ConnectionData>;
    healthMonitor: ConnectionHealthMonitor;
    summaryManager: SessionSummaryManager;
    lifecycleManager: LifecycleManager;
    recoveryManager: RecoveryManager;
    transcriptionManager: TranscriptionManager;
    scriptManager: ScriptManager;
    voiceManager: VoiceManager;
    modelManager: ModelManager;
    getNextCallConfig?: () => SessionConfig | null;
    twilioHelper?: TwilioHelper;
    GEMINI_DEFAULT_VOICE: string;
    GEMINI_DEFAULT_MODEL: string;
    SUMMARY_GENERATION_PROMPT?: string;
    config?: AppConfig;
    geminiClient?: GeminiClient;
}

// Extended dependencies for a specific flow
export interface FlowDependencies extends WebSocketDependencies {
    flowType: string;
    getSessionConfig: (callSid?: string) => Promise<SessionConfig>;
    isIncomingCall: boolean;
    callType?: string;
    isTestMode?: boolean;
    getOutboundTestConfig?: (deps: WebSocketDependencies) => Promise<SessionConfig>;
    getInboundTestConfig?: (deps: WebSocketDependencies) => Promise<SessionConfig>;
}

// Twilio specific messages received from the Twilio Media Streams API
export interface TwilioMediaMessage {
    event: 'media';
    media: {
        payload: string;
        chunk?: string;
        timestamp?: string;
        track?: string;
    };
    streamSid?: string;
    sequenceNumber?: string;
}

export interface TwilioStartMessage {
    event: 'start';
    start: {
        streamSid: string;
        accountSid: string;
        callSid: string;
        tracks?: string[];
        customParameters?: Record<string, string | number | boolean>;
    };
    streamSid?: string;
}

// Messages used by the local browser based testing flow
export interface LocalAudioMessage {
    type: 'audio-data' | 'audio';
    audio?: string;
    audioData?: string;
}

export interface LocalStartMessage {
    type: 'start-session' | 'start_session';
    aiInstructions?: string;
    voice?: string;
    model?: string;
    scriptId?: string;
}

export interface LocalTextMessage {
    type: 'text-message';
    text: string;
}

export interface LocalEndSessionMessage {
    type: 'end-session' | 'end_session';
}

// Heartbeat utilities keep track of active WebSocket connections
export interface HeartbeatData {
    ws: WebSocket;
    interval: number;
    timeout: number;
    onTimeout: ((sessionId: string, ws: WebSocket) => void) | null;
    intervalId: NodeJS.Timeout | null;
    timeoutId: NodeJS.Timeout | null;
    lastPong: number;
    missedPings: number;
    pongHandler?: () => void;
}

export interface HeartbeatStatus {
    sessionId: string;
    lastPong: number;
    missedPings: number;
    interval: number;
    timeout: number;
    isActive: boolean;
}

export interface HeartbeatStatistics {
    totalSessions: number;
    healthySessions: number;
    unhealthySessions: number;
    averageLastPong: number;
}

// Minimal session state stored in activeConnections
export interface SessionData {
    geminiSession: GeminiSession | null;
    isSessionActive: boolean;
    pendingScript?: string;
}

// Internal message type names used across handlers
export type InternalMessageType =
    | 'start-session'
    | 'audio'
    | 'audio-data'
    | 'text-message'
    | 'turn-complete'
    | 'end-session'
    | 'audio-response'
    | 'heartbeat'
    | 'connected'
    | 'mark';

// Map of Twilio events to internal message types so downstream handlers see
// a consistent event name
export const TWILIO_EVENT_MAP: Record<string, InternalMessageType> = {
    start: 'start-session',
    media: 'audio',
    stop: 'end-session',
    connected: 'connected',
    mark: 'mark'
};

