import crypto from 'crypto';
import { config, getConfigValue } from '../config/config';
import { FastifyRequest, FastifyReply } from 'fastify';
import { SecurityUtils } from '../middleware/security-utils';
import { websocketLogger } from '../utils/logger';

interface TwilioWebhookRequest extends FastifyRequest {
  // protocol is inherited from FastifyRequest
  // Additional Twilio-specific properties can be added here
}

interface ValidationMetrics {
  totalValidations: number;
  successfulValidations: number;
  failedValidations: number;
  suspiciousRequests: number;
  lastReset: number;
}

/**
 * Validate Twilio webhook signatures to ensure requests are from Twilio
 */
export class TwilioWebhookValidator {
  private authToken: string;
  private skipValidation: boolean;
  private metrics: ValidationMetrics;
  private suspiciousIPs: Set<string> = new Set();

  constructor() {
    this.authToken = getConfigValue<string>('twilio.authToken', '') || '';
    // Only allow skipping validation in test environment for security
    this.skipValidation = (getConfigValue<boolean>('twilio.skipWebhookValidation', false) || false) && process.env.NODE_ENV === 'test';

    this.metrics = {
      totalValidations: 0,
      successfulValidations: 0,
      failedValidations: 0,
      suspiciousRequests: 0,
      lastReset: Date.now()
    };
  }

  /**
   * Validate a Twilio webhook request
   * @param signature - The X-Twilio-Signature header value
   * @param url - The full URL of the webhook endpoint
   * @param params - The request body parameters
   * @returns Whether the signature is valid
   */
  validateRequest(
    signature: string | undefined,
    url: string,
    params: Record<string, any> | undefined,
    clientIP?: string
  ): boolean {
    this.metrics.totalValidations++;

    // Enhanced validation for production security
    if (!this.authToken) {
      websocketLogger.error('Twilio auth token not configured');
      this.metrics.failedValidations++;
      return false;
    }

    // Only skip validation in test environment for security
    if (this.skipValidation && process.env.NODE_ENV === 'test') {
      console.warn('⚠️ Twilio webhook signature validation is disabled for testing');
      this.metrics.successfulValidations++;
      return true;
    }

    // Always validate in production and development for security
    if (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'development') {
      console.log('🔒 Enforcing Twilio webhook signature validation');
    }

    // Enhanced signature validation
    if (!signature || typeof signature !== 'string') {
      websocketLogger.error('Missing or invalid Twilio signature header', { clientIP });
      this.metrics.failedValidations++;
      if (clientIP) {this.markSuspicious(clientIP);}
      return false;
    }

    if (!url || typeof url !== 'string') {
      websocketLogger.error('Missing or invalid URL for Twilio validation', { clientIP });
      this.metrics.failedValidations++;
      return false;
    }

    if (!params || typeof params !== 'object') {
      websocketLogger.error('Missing or invalid request parameters for Twilio validation', { clientIP });
      this.metrics.failedValidations++;
      if (clientIP) {this.markSuspicious(clientIP);}
      return false;
    }

    // Validate required Twilio parameters
    if (!this.validateTwilioParams(params, clientIP)) {
      return false;
    }

    try {
      // Sort the POST parameters alphabetically by key
      const sortedParams = Object.keys(params || {})
        .sort()
        .reduce((acc: Record<string, any>, key) => {
          acc[key] = params![key];
          return acc;
        }, {});

      // Build the validation string
      let validationString = url;
      for (const [key, value] of Object.entries(sortedParams)) {
        validationString += key + (value || '');
      }

      // Calculate the expected signature
      const expectedSignature = crypto
        .createHmac('sha1', this.authToken)
        .update(validationString)
        .digest('base64');

      const providedBuf = Buffer.from(signature);
      const expectedBuf = Buffer.from(expectedSignature);

      // If lengths differ timingSafeEqual would throw
      const isValid =
        providedBuf.length === expectedBuf.length &&
        crypto.timingSafeEqual(providedBuf, expectedBuf);
      
      if (!isValid) {
        websocketLogger.error('Twilio webhook signature validation failed', {
          clientIP,
          expected: expectedSignature.substring(0, 10) + '...',
          received: signature.substring(0, 10) + '...'
        });
        this.metrics.failedValidations++;
        if (clientIP) {this.markSuspicious(clientIP);}
      } else {
        this.metrics.successfulValidations++;
      }

      return isValid;
    } catch (error) {
      websocketLogger.error('Error validating Twilio webhook signature', error instanceof Error ? error : new Error(String(error)));
      this.metrics.failedValidations++;
      if (clientIP) {this.markSuspicious(clientIP);}
      return false;
    }
  }

  /**
   * Validate Twilio-specific parameters
   */
  private validateTwilioParams(params: Record<string, any>, clientIP?: string): boolean {
    // Validate CallSid format
    if (params.CallSid && !SecurityUtils.sanitizeCallSid(params.CallSid)) {
      websocketLogger.warn('Invalid CallSid format in Twilio webhook', {
        callSid: params.CallSid?.substring(0, 10) + '...',
        clientIP
      });
      this.metrics.suspiciousRequests++;
      if (clientIP) {this.markSuspicious(clientIP);}
      return false;
    }

    // Validate phone numbers
    if (params.From && !SecurityUtils.validatePhoneNumber(params.From)) {
      websocketLogger.warn('Invalid From phone number in Twilio webhook', {
        from: params.From,
        clientIP
      });
      this.metrics.suspiciousRequests++;
      return false;
    }

    if (params.To && !SecurityUtils.validatePhoneNumber(params.To)) {
      websocketLogger.warn('Invalid To phone number in Twilio webhook', {
        to: params.To,
        clientIP
      });
      this.metrics.suspiciousRequests++;
      return false;
    }

    return true;
  }

  /**
   * Mark IP as suspicious
   */
  private markSuspicious(clientIP: string): void {
    this.suspiciousIPs.add(clientIP);
    this.metrics.suspiciousRequests++;

    if (this.suspiciousIPs.size > 100) {
      // Clear old suspicious IPs if too many
      this.suspiciousIPs.clear();
    }
  }

  /**
   * Get validation metrics
   */
  getMetrics(): ValidationMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset validation metrics
   */
  resetMetrics(): void {
    this.metrics = {
      totalValidations: 0,
      successfulValidations: 0,
      failedValidations: 0,
      suspiciousRequests: 0,
      lastReset: Date.now()
    };
  }

  /**
   * Express/Fastify middleware for validating Twilio webhooks
   */
  middleware() {
    return async (request: TwilioWebhookRequest, reply: FastifyReply): Promise<void> => {
      // Skip validation for non-Twilio endpoints
      const twilioEndpoints = ['/incoming-call', '/call-status', '/voice-status'];
      if (!twilioEndpoints.includes(request.url)) {
        return;
      }

      const signature = request.headers['x-twilio-signature'] as string | undefined;
      const fullUrl = `${request.protocol}://${request.headers.host}${request.url}`;
      
      if (!this.validateRequest(signature, fullUrl, request.body as Record<string, any>)) {
        reply.code(403).send({ error: 'Invalid Twilio signature' });
        return;
      }
    };
  }
}

// Export singleton instance
export const twilioValidator = new TwilioWebhookValidator();

/**
 * Validate a Twilio webhook request
 * @param request - The HTTP request object
 * @returns Whether the request is valid
 */
export function validateTwilioWebhook(request: TwilioWebhookRequest): boolean {
  const signature = request.headers['x-twilio-signature'] as string | undefined;
  const fullUrl = `${request.protocol}://${request.headers.host}${request.url}`;

  // Get client IP for enhanced security tracking
  const clientIP = request.socket.remoteAddress ||
    (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    'unknown';

  return twilioValidator.validateRequest(signature, fullUrl, request.body as Record<string, any>, clientIP);
}