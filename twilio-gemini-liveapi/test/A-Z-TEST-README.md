# A-Z Cyclic Integration Test for Twilio-Gemini

## Overview

The A-Z cyclic integration test (`a-z-cyclic-integration.test.js`) provides comprehensive end-to-end validation of the Twilio-Gemini integration, simulating a complete call flow from US to Czech Republic and back.

## Test Coverage

### 1. Outbound Call Initiation (US → Czech)
- Initiates call from US Twilio number to Czech number
- Validates authentication headers are properly passed
- Loads campaign script with fallback mechanism
- Initializes session lifecycle for test calls

### 2. Inbound Call Reception (Czech Side)
- Handles incoming call as inbound in Czech Republic
- Auto-initializes test sessions (Fix #4)
- Validates immediate readiness without 2-second delay (Fix #2)
- Loads appropriate inbound campaign scripts

### 3. Audio Streaming and AI Communication
- Tests bidirectional audio flow between Twilio and Gemini
- Converts μ-law (Twilio) to PCM (Gemini) format
- Validates continuous streaming without lag
- Tests both direct and browser-based audio formats

### 4. Production Fixes Validation
All 4 critical production fixes are validated:
- **Fix #1**: Script delivery with fallback instructions
- **Fix #2**: 2-second speech lag removal
- **Fix #3**: Auth header passing from frontend
- **Fix #4**: UI state management for test sessions

### 5. Call Summary and Monitoring
- Generates call summaries after conversations
- Monitors all stages of call lifecycle
- Creates detailed assertions for validation
- Tracks complete A-Z cycle metrics

## Running the Test

### Prerequisites

1. **Environment Variables**
Set these in your `.env` file or export them:

```bash
# Required for real API testing
export GEMINI_API_KEY="your-gemini-api-key"
export TWILIO_ACCOUNT_SID="your-twilio-account-sid"
export TWILIO_AUTH_TOKEN="your-twilio-auth-token"

# Test-specific configuration
export TEST_US_TWILIO_NUMBER="+***********"      # Your US Twilio number
export TEST_CZECH_NUMBER="+************"          # Target Czech number
export TEST_AUTH_TOKEN="your-bearer-token"        # Auth token for API
export TEST_SHARED_AUTH="your-shared-secret"      # Shared auth secret

# Optional (defaults provided)
export TEST_BACKEND_URL="http://localhost:3001"
export TEST_WS_URL="ws://localhost:3001"
```

2. **Backend Services**
Ensure the backend is running:
```bash
npm run dev
```

### Running the Test

**Run only the A-Z cyclic test:**
```bash
npm test test/a-z-cyclic-integration.test.js
```

**Run with verbose output:**
```bash
NODE_ENV=test npm test test/a-z-cyclic-integration.test.js -- --reporter spec
```

**Run specific test suites:**
```bash
# Test only outbound phase
npm test test/a-z-cyclic-integration.test.js -- --grep "Phase 1"

# Test only production fixes
npm test test/a-z-cyclic-integration.test.js -- --grep "Phase 4"
```

### Test Configuration

The test uses configuration from `TEST_CONFIG` object:
- `usNumber`: US Twilio number (outbound originator)
- `czechNumber`: Czech number (inbound receiver)
- `backendUrl`: Backend API URL
- `wsUrl`: WebSocket URL for real-time communication
- `authToken`: Bearer token for authentication
- `sharedAuth`: Shared secret for additional auth
- `callTimeout`: Maximum time for full call (60s)
- `wsTimeout`: WebSocket connection timeout (5s)
- `audioTimeout`: Audio processing timeout (3s)

### Expected Output

Successful test run shows:
```
A-Z Cyclic Integration Test
  Phase 1: Outbound Call Initiation
    ✓ should initiate outbound call from US to Czech number with proper auth
    ✓ should validate call monitoring during outbound phase
  Phase 2: Inbound Call Reception
    ✓ should handle call as inbound in Czech Republic with proper state
    ✓ should validate immediate readiness without 2-second delay (Fix #2)
  Phase 3: Audio Streaming and AI Communication
    ✓ should process audio bidirectionally between Twilio and Gemini
    ✓ should handle continuous audio streaming without lag
  Phase 4: Production Fixes Validation
    ✓ should validate all 4 production fixes in integrated flow
  Phase 5: Call Summary and End-to-End Validation
    ✓ should generate call summary after conversation
    ✓ should complete full A-Z cycle with all validations
  Call Monitoring and Assertions
    ✓ should monitor all stages of call lifecycle
    ✓ should create detailed assertions for each validation point

11 passing (Xs)
```

### Validation Points

Each test phase includes specific assertions:

1. **Authentication**: Bearer token and shared secret headers
2. **Script Loading**: Campaign instructions with fallback
3. **Session State**: Auto-initialization for test sessions
4. **Audio Flow**: μ-law to PCM conversion and streaming
5. **Timing**: No artificial delays (especially 2-second lag)
6. **AI Integration**: Gemini session creation and communication
7. **Monitoring**: Event tracking throughout call lifecycle
8. **Summary**: Call outcome and conversation capture

### Troubleshooting

**Test fails with "no real API key":**
- Set `GEMINI_API_KEY` environment variable
- Some tests skip when using mock API key

**Audio processing errors:**
- Verify `AudioProcessor` is properly initialized
- Check μ-law encoding/decoding functions

**Session state errors:**
- Ensure test session IDs follow pattern: `(inbound|outbound)_test-*`
- Check `SessionLifecycleManager` auto-initialization

**Auth header failures:**
- Verify `TEST_AUTH_TOKEN` starts with "Bearer "
- Check `TEST_SHARED_AUTH` is set

### Integration with CI/CD

Add to your CI pipeline:
```yaml
- name: Run A-Z Integration Tests
  env:
    GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
    TWILIO_ACCOUNT_SID: ${{ secrets.TWILIO_ACCOUNT_SID }}
    TWILIO_AUTH_TOKEN: ${{ secrets.TWILIO_AUTH_TOKEN }}
    TEST_US_TWILIO_NUMBER: ${{ secrets.TEST_US_NUMBER }}
    TEST_CZECH_NUMBER: ${{ secrets.TEST_CZECH_NUMBER }}
  run: npm test test/a-z-cyclic-integration.test.js
```

### Notes

- Tests use real Gemini API when `GEMINI_API_KEY` is provided
- Mock responses are used when API key is 'test-key'
- All 4 production fixes are validated in each full cycle
- Test sessions auto-cleanup after completion
- Monitoring data is logged for debugging