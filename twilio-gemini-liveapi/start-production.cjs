#!/usr/bin/env node

// Production start script for PM2 compatibility
const { spawn } = require('child_process');
const path = require('path');

// Set production environment
process.env.NODE_ENV = 'production';

// Start the application
const indexPath = path.join(__dirname, 'dist', 'index.js');
const args = ['--es-module-specifier-resolution=node', indexPath];

const child = spawn('node', args, {
  cwd: __dirname,
  env: process.env,
  stdio: 'inherit'
});

child.on('error', (err) => {
  console.error('Failed to start process:', err);
  process.exit(1);
});

child.on('exit', (code) => {
  process.exit(code);
});